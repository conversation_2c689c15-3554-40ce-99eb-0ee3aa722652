package com.icss.springbootbig.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icss.springbootbig.entity.Account;
import com.icss.springbootbig.entity.BankCard;
import com.icss.springbootbig.entity.Transaction;
import com.icss.springbootbig.enums.TransactionType;
import com.icss.springbootbig.mapper.AccountMapper;
import com.icss.springbootbig.mapper.BankCardMapper;
import com.icss.springbootbig.mapper.TransactionMapper;
import com.icss.springbootbig.exception.ApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

@Service
public class AccountService {
    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private BankCardMapper bankCardMapper;

    @Autowired
    private TransactionMapper transactionMapper;

    public Account getAccountByUserId(Integer userId) {
        QueryWrapper<Account> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        return accountMapper.selectOne(queryWrapper);
    }

    @Transactional
    public void updateBalance(Integer accountId, BigDecimal amount) {
        Account account = accountMapper.selectById(accountId);
        if (account == null) {
            throw new ApiException("账户不存在");
        }
        account.setBalance(account.getBalance().add(amount));
        accountMapper.updateById(account);
    }

    // 新增的deposit方法
    @Transactional
    public void deposit(Integer accountId, BigDecimal amount, Integer cardId) {
        // 验证账户
        Account account = accountMapper.selectById(accountId);
        if (account == null) {
            throw new ApiException("账户不存在");
        }

        // 验证银行卡
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null) {
            throw new ApiException("银行卡不存在");
        }

        // 执行充值
        account.setBalance(account.getBalance().add(amount));
        accountMapper.updateById(account);

        // 生成交易记录
        Transaction transaction = new Transaction();
        String txnNo = UUID.randomUUID().toString().replace("-", "").substring(0, 20);
        Date now = new Date();

        transaction.setTxnNo(txnNo);
        transaction.setUserId(account.getUserId());
        transaction.setAccountId(accountId);
        transaction.setType(TransactionType.DEPOSIT.getCode());
        transaction.setAmount(amount);
        transaction.setBalance(account.getBalance());
        transaction.setCounterparty(card.getBankName() + "(" + card.getCardNumber().substring(card.getCardNumber().length() - 4) + ")");
        transaction.setStatus(1);
        transaction.setRemark("银行卡充值");
        transaction.setCreatedAt(now);
        transaction.setUpdatedAt(now);

        transactionMapper.insert(transaction);
    }

    // 新增的withdraw方法
    @Transactional
    public void withdraw(Integer accountId, BigDecimal amount, Integer cardId) {
        // 验证账户
        Account account = accountMapper.selectById(accountId);
        if (account == null) {
            throw new ApiException("账户不存在");
        }

        // 验证银行卡
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null) {
            throw new ApiException("银行卡不存在");
        }

        // 检查余额
        if (account.getBalance().compareTo(amount) < 0) {
            throw new ApiException("余额不足");
        }

        // 执行提现
        account.setBalance(account.getBalance().subtract(amount));
        accountMapper.updateById(account);

        // 生成交易记录
        Transaction transaction = new Transaction();
        String txnNo = UUID.randomUUID().toString().replace("-", "").substring(0, 20);
        Date now = new Date();

        transaction.setTxnNo(txnNo);
        transaction.setUserId(account.getUserId());
        transaction.setAccountId(accountId);
        transaction.setType(TransactionType.WITHDRAW.getCode());
        transaction.setAmount(amount.negate());
        transaction.setBalance(account.getBalance());
        transaction.setCounterparty(card.getBankName() + "(" + card.getCardNumber().substring(card.getCardNumber().length() - 4) + ")");
        transaction.setStatus(1);
        transaction.setRemark("提现到银行卡");
        transaction.setCreatedAt(now);
        transaction.setUpdatedAt(now);

        transactionMapper.insert(transaction);
    }

    // 新增的updateAccount方法
    public void updateAccount(Account account) {
        account.setUpdatedAt(new Date());
        accountMapper.updateById(account);
    }
}