import { LoadingState } from '../types/index';

/**
 * 加载状态管理器
 * 统一管理页面和组件的加载状态
 */
export class LoadingManager {
  private static instance: LoadingManager;
  private loadingStates: Map<string, LoadingState> = new Map();
  private loadingCallbacks: Map<string, (state: LoadingState) => void> = new Map();

  private constructor() {}

  public static getInstance(): LoadingManager {
    if (!LoadingManager.instance) {
      LoadingManager.instance = new LoadingManager();
    }
    return LoadingManager.instance;
  }

  /**
   * 设置加载状态
   */
  public setLoadingState(key: string, state: LoadingState): void {
    this.loadingStates.set(key, state);
    
    // 通知回调
    const callback = this.loadingCallbacks.get(key);
    if (callback) {
      callback(state);
    }
  }

  /**
   * 获取加载状态
   */
  public getLoadingState(key: string): LoadingState {
    return this.loadingStates.get(key) || LoadingState.IDLE;
  }

  /**
   * 检查是否正在加载
   */
  public isLoading(key: string): boolean {
    return this.getLoadingState(key) === LoadingState.LOADING;
  }

  /**
   * 检查是否加载成功
   */
  public isSuccess(key: string): boolean {
    return this.getLoadingState(key) === LoadingState.SUCCESS;
  }

  /**
   * 检查是否加载失败
   */
  public isError(key: string): boolean {
    return this.getLoadingState(key) === LoadingState.ERROR;
  }

  /**
   * 开始加载
   */
  public startLoading(key: string): void {
    this.setLoadingState(key, LoadingState.LOADING);
  }

  /**
   * 加载成功
   */
  public loadingSuccess(key: string): void {
    this.setLoadingState(key, LoadingState.SUCCESS);
  }

  /**
   * 加载失败
   */
  public loadingError(key: string): void {
    this.setLoadingState(key, LoadingState.ERROR);
  }

  /**
   * 重置加载状态
   */
  public resetLoading(key: string): void {
    this.setLoadingState(key, LoadingState.IDLE);
  }

  /**
   * 注册状态变化回调
   */
  public onStateChange(key: string, callback: (state: LoadingState) => void): void {
    this.loadingCallbacks.set(key, callback);
  }

  /**
   * 移除状态变化回调
   */
  public removeStateChange(key: string): void {
    this.loadingCallbacks.delete(key);
  }

  /**
   * 清除所有状态
   */
  public clearAll(): void {
    this.loadingStates.clear();
    this.loadingCallbacks.clear();
  }

  /**
   * 执行异步操作并管理加载状态
   */
  public async executeWithLoading<T>(
    key: string,
    operation: () => Promise<T>,
    onSuccess?: (result: T) => void,
    onError?: (error: Error) => void
  ): Promise<T | null> {
    try {
      this.startLoading(key);
      const result = await operation();
      this.loadingSuccess(key);
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (error) {
      this.loadingError(key);
      
      if (onError) {
        onError(error);
      } else {
        console.error(`操作失败 [${key}]:`, error);
      }
      
      return null;
    }
  }

  /**
   * 批量设置加载状态
   */
  public setBatchLoadingState(states: Map<string, LoadingState>): void {
    for (const entry of states.entries()) {
      this.setLoadingState(entry[0], entry[1]);
    }
  }

  /**
   * 获取所有加载状态
   */
  public getAllLoadingStates(): Map<string, LoadingState> {
    return new Map(this.loadingStates);
  }
}

/**
 * 常用的加载状态键名
 */
export class LoadingKeys {
  // 用户相关
  static readonly USER_LOGIN: string = 'user_login';
  static readonly USER_REGISTER: string = 'user_register';
  static readonly USER_INFO: string = 'user_info';
  static readonly USER_UPDATE: string = 'user_update';

  // 钱包相关
  static readonly WALLET_BALANCE: string = 'wallet_balance';
  static readonly WALLET_RECHARGE: string = 'wallet_recharge';
  static readonly WALLET_WITHDRAW: string = 'wallet_withdraw';
  static readonly WALLET_TRANSFER: string = 'wallet_transfer';

  // 银行卡相关
  static readonly BANK_CARD_LIST: string = 'bank_card_list';
  static readonly BANK_CARD_BIND: string = 'bank_card_bind';
  static readonly BANK_CARD_UNBIND: string = 'bank_card_unbind';
  static readonly BANK_CARD_DETAIL: string = 'bank_card_detail';

  // 交易相关
  static readonly TRANSACTION_LIST: string = 'transaction_list';
  static readonly TRANSACTION_DETAIL: string = 'transaction_detail';

  // 页面加载
  static readonly PAGE_INIT: string = 'page_init';
  static readonly DATA_REFRESH: string = 'data_refresh';

  // 网络检查
  static readonly NETWORK_CHECK: string = 'network_check';
}

/**
 * 导出单例实例
 */
export const loadingManager = LoadingManager.getInstance();
