package com.icss.springbootbig.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.icss.springbootbig.entity.Bank;
import com.icss.springbootbig.result.R;
import com.icss.springbootbig.service.BankService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@CrossOrigin(origins = {"http://localhost:5173", "http://127.0.0.1:5173"})
@RestController
@RequestMapping("/api/bank")
public class BankController {
    
    @Autowired
    private BankService bankService;
    
    /**
     * 创建银行
     */
    @PostMapping
    public R<Bank> createBank(@RequestBody Bank bank) {
        try {
            Bank createdBank = bankService.createBank(bank);
            return R.success("银行创建成功", createdBank);
        } catch (Exception e) {
            return R.failure("创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取银行信息
     */
    @GetMapping("/{bankId}")
    public R<Bank> getBankById(@PathVariable Integer bankId) {
        try {
            Bank bank = bankService.getBankById(bankId);
            return R.success("查询成功", bank);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据银行代码获取银行信息
     */
    @GetMapping("/code/{bankCode}")
    public R<Bank> getBankByCode(@PathVariable String bankCode) {
        try {
            Bank bank = bankService.getBankByCode(bankCode);
            return R.success("查询成功", bank);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新银行信息
     */
    @PutMapping("/{bankId}")
    public R<Bank> updateBank(@PathVariable Integer bankId, @RequestBody Bank bank) {
        try {
            bank.setBankId(bankId);
            Bank updatedBank = bankService.updateBank(bank);
            return R.success("更新成功", updatedBank);
        } catch (Exception e) {
            return R.failure("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除银行
     */
    @DeleteMapping("/{bankId}")
    public R<String> deleteBank(@PathVariable Integer bankId) {
        try {
            bankService.deleteBank(bankId);
            return R.success("删除成功");
        } catch (Exception e) {
            return R.failure("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 启用/禁用银行
     */
    @PutMapping("/{bankId}/status")
    public R<String> updateBankStatus(@PathVariable Integer bankId, @RequestParam Integer status) {
        try {
            bankService.updateBankStatus(bankId, status);
            String message = status == 1 ? "银行已启用" : "银行已禁用";
            return R.success(message);
        } catch (Exception e) {
            return R.failure("状态更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有银行列表
     */
    @GetMapping("/all")
    public R<List<Bank>> getAllBanks() {
        try {
            List<Bank> banks = bankService.getAllBanks();
            return R.success("查询成功", banks);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取启用的银行列表
     */
    @GetMapping("/active")
    public R<List<Bank>> getActiveBanks() {
        try {
            List<Bank> banks = bankService.getActiveBanks();
            return R.success("查询成功", banks);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询银行
     */
    @GetMapping("/page")
    public R<IPage<Bank>> getBanksByPage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status) {
        try {
            IPage<Bank> page = bankService.getBanksByPage(pageNum, pageSize, keyword, status);
            return R.success("查询成功", page);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新银行排序
     */
    @PutMapping("/{bankId}/sort")
    public R<String> updateBankSortOrder(@PathVariable Integer bankId, @RequestBody Map<String, Integer> request) {
        try {
            Integer sortOrder = request.get("sortOrder");
            bankService.updateBankSortOrder(bankId, sortOrder);
            return R.success("排序更新成功");
        } catch (Exception e) {
            return R.failure("排序更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量更新银行状态
     */
    @PutMapping("/batch/status")
    public R<String> batchUpdateStatus(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> bankIds = (List<Integer>) request.get("bankIds");
            Integer status = (Integer) request.get("status");
            
            for (Integer bankId : bankIds) {
                bankService.updateBankStatus(bankId, status);
            }
            
            String message = status == 1 ? "批量启用成功" : "批量禁用成功";
            return R.success(message);
        } catch (Exception e) {
            return R.failure("批量操作失败: " + e.getMessage());
        }
    }
}
