import promptAction from '@ohos.promptAction';
import { ErrorType, ErrorInfo, ApiError } from '../types/index';

/**
 * 统一错误处理工具类
 * 提供错误分类、错误提示、错误日志等功能
 */
export class ErrorHandler {
  private static instance: ErrorHandler;

  private constructor() {}

  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * 处理错误并显示用户友好的提示
   */
  public async handleError(error: Error | ApiError | object, context?: string) {
    const errorInfo = this.parseError(error);
    
    // 记录错误日志
    this.logError(errorInfo, context);
    
    // 显示用户友好的错误提示
    await this.showErrorToast(errorInfo);
  }

  /**
   * 解析错误对象，转换为标准错误信息
   */
  public parseError(error: Error | ApiError | object): ErrorInfo {
    if (error instanceof ApiError) {
      return error.toErrorInfo();
    }

    if (error instanceof Error) {
      return {
        type: ErrorType.UNKNOWN_ERROR,
        code: -1,
        message: error.message,
        details: error.stack,
        timestamp: new Date().toISOString()
      };
    }

    // 处理网络错误
    if (typeof error === 'object' && error !== null && this.hasCodeAndMessage(error)) {
      const networkError = error as NetworkError;
      return {
        type: ErrorType.NETWORK_ERROR,
        code: networkError.code,
        message: this.getNetworkErrorMessage(networkError.code),
        details: networkError.message,
        timestamp: new Date().toISOString()
      };
    }

    // 处理字符串错误
    if (typeof error === 'string') {
      return {
        type: ErrorType.UNKNOWN_ERROR,
        code: -1,
        message: error,
        timestamp: new Date().toISOString()
      };
    }

    // 默认错误
    return {
      type: ErrorType.UNKNOWN_ERROR,
      code: -1,
      message: '未知错误',
      details: error?.toString() || 'Unknown error',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 显示错误提示
   */
  private async showErrorToast(errorInfo: ErrorInfo) {
    const message = this.getUserFriendlyMessage(errorInfo);
    
    try {
      await promptAction.showToast({
        message: message,
        duration: 3000
      });
    } catch (toastError) {
      console.error('显示错误提示失败:', toastError);
    }
  }

  /**
   * 获取用户友好的错误消息
   */
  private getUserFriendlyMessage(errorInfo: ErrorInfo): string {
    switch (errorInfo.type) {
      case ErrorType.NETWORK_ERROR:
        return this.getNetworkErrorMessage(errorInfo.code);
      
      case ErrorType.AUTH_ERROR:
        return '登录已过期，请重新登录';
      
      case ErrorType.VALIDATION_ERROR:
        return errorInfo.message || '输入信息有误，请检查后重试';
      
      case ErrorType.BUSINESS_ERROR:
        return errorInfo.message || '操作失败，请稍后重试';
      
      case ErrorType.API_ERROR:
        // 根据错误码返回具体消息
        return this.getApiErrorMessage(errorInfo.code, errorInfo.message);
      
      default:
        return '操作失败，请稍后重试';
    }
  }

  /**
   * 获取网络错误消息
   */
  private getNetworkErrorMessage(code: number): string {
    switch (code) {
      case 2300001:
        return '网络连接失败，请检查网络设置';
      case 2300002:
        return '网络连接超时，请稍后重试';
      case 2300003:
        return '网络连接中断，请检查网络状态';
      case 2300007:
        return '网络地址解析失败';
      case 2300009:
        return '网络访问被拒绝';
      default:
        return '网络连接异常，请检查网络设置';
    }
  }

  /**
   * 获取API错误消息
   */
  private getApiErrorMessage(code: number, originalMessage: string): string {
    switch (code) {
      case 400:
        return '请求参数错误';
      case 401:
        return '登录已过期，请重新登录';
      case 403:
        return '没有权限执行此操作';
      case 404:
        return '请求的资源不存在';
      case 500:
        return '服务器内部错误，请稍后重试';
      case 502:
        return '服务器网关错误';
      case 503:
        return '服务暂时不可用，请稍后重试';
      default:
        // 如果有具体的业务错误消息，优先使用
        return originalMessage || '操作失败，请稍后重试';
    }
  }

  /**
   * 检查对象是否有code和message属性
   */
  private hasCodeAndMessage(obj: object): boolean {
    const networkObj = obj as NetworkError;
    return typeof networkObj.code === 'number' &&
           typeof networkObj.message === 'string';
  }

  /**
   * 记录错误日志
   */
  private logError(errorInfo: ErrorInfo, context?: string): void {
    const logMessage = `[${errorInfo.type}] ${context || 'Unknown'}: ${errorInfo.message}`;
    const logDetails = new ErrorLogDetails(errorInfo, context);

    console.error(logMessage, logDetails);

    // 在生产环境中，这里可以添加错误上报逻辑
    // 例如发送到错误监控服务
  }

  /**
   * 创建业务错误
   */
  public static createBusinessError(message: string, code: number = -1): ApiError {
    return new ApiError(code, message, ErrorType.BUSINESS_ERROR);
  }

  /**
   * 创建验证错误
   */
  public static createValidationError(message: string): ApiError {
    return new ApiError(-1, message, ErrorType.VALIDATION_ERROR);
  }

  /**
   * 创建认证错误
   */
  public static createAuthError(message: string = '认证失败'): ApiError {
    return new ApiError(401, message, ErrorType.AUTH_ERROR);
  }

  /**
   * 创建网络错误
   */
  public static createNetworkError(code: number, message: string): ApiError {
    return new ApiError(code, message, ErrorType.NETWORK_ERROR);
  }
}

/**
 * 网络错误接口
 */
interface NetworkError {
  code: number;
  message: string;
}

/**
 * 错误日志详情类
 */
class ErrorLogDetails {
  public type: ErrorType;
  public code: number;
  public message: string;
  public details?: string;
  public timestamp: string;
  public context?: string;

  constructor(errorInfo: ErrorInfo, context?: string) {
    this.type = errorInfo.type;
    this.code = errorInfo.code;
    this.message = errorInfo.message;
    this.details = errorInfo.details;
    this.timestamp = errorInfo.timestamp;
    this.context = context;
  }
}

/**
 * 导出单例实例
 */
export const errorHandler = ErrorHandler.getInstance();
