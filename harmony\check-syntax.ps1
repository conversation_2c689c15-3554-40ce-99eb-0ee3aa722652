# HarmonyOS ArkTS 语法检查脚本
Write-Host "检查 HarmonyOS ArkTS 语法错误..." -ForegroundColor Green

$errorCount = 0
$files = Get-ChildItem -Path ".\entry\src\main\ets" -Filter "*.ets" -Recurse

foreach ($file in $files) {
    Write-Host "检查文件: $($file.FullName)" -ForegroundColor Yellow
    
    $content = Get-Content $file.FullName -Raw
    
    # 检查 Promise<void> 语法错误
    if ($content -match "Promise<void>") {
        Write-Host "  ❌ 发现 Promise<void> 语法错误" -ForegroundColor Red
        $errorCount++
    }
    
    # 检查已删除页面的引用
    $deletedPages = @("RechargePage", "WithdrawPage", "TransferPage", "BankCardDemoPage", "BankCardSelectorPage", "BankCardTestPage")
    foreach ($page in $deletedPages) {
        if ($content -match $page) {
            Write-Host "  ❌ 发现对已删除页面的引用: $page" -ForegroundColor Red
            $errorCount++
        }
    }
    
    # 检查基本语法结构
    if ($content -match "@Entry\s*@Component" -or $content -match "@Component") {
        Write-Host "  ✅ 组件结构正确" -ForegroundColor Green
    }
}

Write-Host "`n检查完成!" -ForegroundColor Green
if ($errorCount -eq 0) {
    Write-Host "✅ 未发现语法错误，代码应该可以正常编译" -ForegroundColor Green
} else {
    Write-Host "❌ 发现 $errorCount 个潜在错误" -ForegroundColor Red
}

Write-Host "`n建议:" -ForegroundColor Cyan
Write-Host "1. 使用 DevEco Studio 打开项目进行完整编译测试" -ForegroundColor White
Write-Host "2. 确保安装了 HarmonyOS SDK 和 Java 环境" -ForegroundColor White
Write-Host "3. 让 DevEco Studio 自动生成缺失的构建文件" -ForegroundColor White
