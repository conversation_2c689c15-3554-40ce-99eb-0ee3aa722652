{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"init": 681800, "PreBuild": 117129800, "MergeProfile": 7300400, "CreateBuildProfile": 4297300, "PreCheckSyscap": 523900, "GeneratePkgContextInfo": 1430400, "ProcessProfile": 189983900, "ProcessRouterMap": 8148400, "PreviewProcessResource": 4421700, "GenerateLoaderJson": 30590500, "PreviewCompileResource": 243607600, "PreviewHookCompileResource": 336200, "CopyPreviewProfile": 7983500, "ReplacePreviewerPage": 608400, "buildPreviewerResource": 379500, "PreviewUpdateAssets": 15095500, "PreviewArkTS": 21467159500, "PreviewBuild": 1031700}, "APP": {"init": 303500}}, "BUILD_ID": "202506251214331400", "TOTAL_TIME": 22434597600}}