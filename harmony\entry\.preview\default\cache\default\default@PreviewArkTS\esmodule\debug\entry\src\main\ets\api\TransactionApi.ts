import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
import type { Transaction, TransactionQueryParams, PageResult } from '../common/types/index';
/**
 * 交易记录相关API接口
 */
export class TransactionApi {
    /**
     * 查询交易记录
     */
    static async getTransactionList(params: TransactionQueryParams): Promise<PageResult<Transaction>> {
        const response = await httpClient.get<PageResult<Transaction>>('/transaction/list', params as Record<string, string>);
        return response.data;
    }
    /**
     * 查询交易详情
     */
    static async getTransactionDetail(transactionId: number): Promise<Transaction> {
        const response = await httpClient.get<Transaction>(`/transaction/${transactionId}`);
        return response.data;
    }
}
