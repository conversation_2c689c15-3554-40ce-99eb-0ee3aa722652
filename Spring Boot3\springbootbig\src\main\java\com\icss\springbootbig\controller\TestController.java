package com.icss.springbootbig.controller;

import com.icss.springbootbig.dto.LoginResponse;
import com.icss.springbootbig.entity.User;
import com.icss.springbootbig.result.R;
import com.icss.springbootbig.service.NewUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 测试控制器 - 用于验证基本功能
 */
@CrossOrigin(origins = {"http://localhost:5173", "http://127.0.0.1:5173"})
@RestController
@RequestMapping("/api/test")
public class TestController {

    @Autowired
    private NewUserService userService;

    /**
     * 简单的验证码验证接口
     */
    @PostMapping("/verify-code")
    public R<Boolean> verifyCode(@RequestBody Map<String, Object> request) {
        try {
            String phone = (String) request.get("phone");
            String verificationCode = (String) request.get("verificationCode");
            String loginType = (String) request.get("loginType");
            
            // 简单验证：如果验证码是"525624"就返回成功
            if ("525624".equals(verificationCode)) {
                return R.success("验证成功", true);
            } else {
                return R.failure("验证码错误");
            }
        } catch (Exception e) {
            return R.failure("验证失败: " + e.getMessage());
        }
    }

    /**
     * 简单的登录接口
     */
    @PostMapping("/login")
    public R<LoginResponse> login(@RequestBody Map<String, Object> request) {
        try {
            String phone = (String) request.get("phone");
            
            // 查找用户
            User user = userService.loginWithPassword(phone, "12345655");
            
            // 转换为LoginResponse
            LoginResponse loginResponse = LoginResponse.fromUser(user);
            return R.success("登录成功", loginResponse);
        } catch (Exception e) {
            return R.failure("登录失败: " + e.getMessage());
        }
    }

    /**
     * 简单的用户资料更新接口
     */
    @PutMapping("/profile/{userId}")
    public R<LoginResponse> updateProfile(@PathVariable Integer userId, @RequestBody Map<String, Object> profileData) {
        try {
            // 只更新支付密码
            User user = userService.getUserById(userId);
            if (profileData.containsKey("payPassword")) {
                // 支付密码现在存储在银行卡表中，不再存储在用户表中
                // 如果需要设置支付密码，请使用PayPasswordService
                throw new RuntimeException("支付密码设置功能已迁移到PayPasswordService");
            }
            
            LoginResponse response = LoginResponse.fromUser(user);
            return R.success("资料更新成功", response);
        } catch (Exception e) {
            return R.failure("更新失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public R<String> health() {
        return R.success("服务正常", "OK");
    }
}
