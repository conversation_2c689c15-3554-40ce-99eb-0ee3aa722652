import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { UserApi } from '../api/UserApi';
import { UpdatePayPasswordRequest, UserInfo } from '../common/types/index';
import { storageManager } from '../common/storage/StorageManager';

@Entry
@Component
struct ChangePayPasswordPage {
  @State oldPassword: string = '';
  @State newPassword: string = '';
  @State confirmPassword: string = '';
  @State isSubmitting: boolean = false;
  @State isFirstTimeSetup: boolean = false; // 是否为首次设置
  @State isLoading: boolean = true; // 是否正在加载用户信息

  aboutToAppear() {
    this.checkPayPasswordStatus();
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button('返回')
          .fontSize(16)
          .fontColor('#1976D2')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            router.back();
          })

        Text(this.isFirstTimeSetup ? '设置支付密码' : '修改支付密码')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(60) // 占位，保持标题居中
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')

      Scroll() {
        Column() {
          // 说明信息
          Column() {
            Text('安全提示')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 12 })

            Text('• 支付密码用于转账、提现等资金操作')
              .fontSize(14)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 })

            Text('• 请设置6位数字密码，避免使用生日等简单密码')
              .fontSize(14)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 })

            Text('• 请妥善保管您的支付密码，不要告诉他人')
              .fontSize(14)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
          }
          .width('100%')
          .padding(20)
          .borderRadius(12)
          .backgroundColor('#FFF3E0')
          .margin({ top: 16 })

          // 密码修改表单
          Column() {
            Text('密码信息')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 20 })

            // 原支付密码（仅修改时显示）
            if (!this.isFirstTimeSetup) {
              Column() {
                Text('原支付密码')
                  .fontSize(14)
                  .fontColor('#333333')
                  .alignSelf(ItemAlign.Start)
                  .margin({ bottom: 8 })

                TextInput({ placeholder: '请输入原支付密码' })
                  .type(InputType.Password)
                  .maxLength(6)
                  .fontSize(16)
                  .height(48)
                  .borderRadius(8)
                  .backgroundColor('#F8F9FA')
                  .border({ width: 1, color: this.oldPassword.length > 0 ? '#1976D2' : '#E0E0E0' })
                  .onChange((value: string) => {
                    this.oldPassword = value;
                  })
              }
              .width('100%')
              .alignItems(HorizontalAlign.Start)
              .margin({ bottom: 20 })
            }

            // 新支付密码
            Column() {
              Text('新支付密码')
                .fontSize(14)
                .fontColor('#333333')
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 8 })

              TextInput({ placeholder: '请输入6位数字密码' })
                .type(InputType.Password)
                .maxLength(6)
                .fontSize(16)
                .height(48)
                .borderRadius(8)
                .backgroundColor('#F8F9FA')
                .border({ width: 1, color: this.newPassword.length > 0 ? '#1976D2' : '#E0E0E0' })
                .onChange((value: string) => {
                  this.newPassword = value;
                })

              if (this.newPassword.length > 0 && this.newPassword.length < 6) {
                Text('密码长度必须为6位')
                  .fontSize(12)
                  .fontColor('#F44336')
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 4 })
              }
            }
            .width('100%')
            .alignItems(HorizontalAlign.Start)
            .margin({ bottom: 20 })

            // 确认新密码
            Column() {
              Text('确认新密码')
                .fontSize(14)
                .fontColor('#333333')
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 8 })

              TextInput({ placeholder: '请再次输入新密码' })
                .type(InputType.Password)
                .maxLength(6)
                .fontSize(16)
                .height(48)
                .borderRadius(8)
                .backgroundColor('#F8F9FA')
                .border({ width: 1, color: this.confirmPassword.length > 0 ? '#1976D2' : '#E0E0E0' })
                .onChange((value: string) => {
                  this.confirmPassword = value;
                })

              if (this.confirmPassword.length > 0 && this.newPassword !== this.confirmPassword) {
                Text('两次输入的密码不一致')
                  .fontSize(12)
                  .fontColor('#F44336')
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 4 })
              }
            }
            .width('100%')
            .alignItems(HorizontalAlign.Start)
            .margin({ bottom: 30 })

            // 提交按钮
            Button(this.isSubmitting ? '修改中...' : '确认修改')
              .width('100%')
              .height(48)
              .fontSize(16)
              .fontColor(Color.White)
              .backgroundColor(this.isFormValid() && !this.isSubmitting ? '#1976D2' : '#CCCCCC')
              .borderRadius(8)
              .enabled(this.isFormValid() && !this.isSubmitting)
              .onClick(() => {
                this.submitForm();
              })
          }
          .width('100%')
          .padding(20)
          .borderRadius(12)
          .backgroundColor('#FFFFFF')
          .margin({ top: 16 })
        }
        .padding({ left: 16, right: 16, bottom: 20 })
      }
      .layoutWeight(1)
      .backgroundColor('#F5F5F5')
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  // 表单验证
  isFormValid(): boolean {
    const newPasswordValid = this.newPassword.length === 6 &&
                            this.confirmPassword.length === 6 &&
                            this.newPassword === this.confirmPassword;

    if (this.isFirstTimeSetup) {
      // 首次设置，只需要验证新密码
      return newPasswordValid;
    } else {
      // 修改密码，需要验证原密码和新密码
      return this.oldPassword.length === 6 && newPasswordValid;
    }
  }

  // 提交表单
  async submitForm() {
    if (!this.isFormValid() || this.isSubmitting) return;

    this.isSubmitting = true;

    try {
      const data: UpdatePayPasswordRequest = {
        oldPassword: this.isFirstTimeSetup ? undefined : this.oldPassword,
        newPassword: this.newPassword
      };

      await UserApi.updatePayPassword(data);

      const successMessage = this.isFirstTimeSetup ? '支付密码设置成功' : '支付密码修改成功';
      promptAction.showToast({ message: successMessage });

      // 如果是首次设置，更新状态
      if (this.isFirstTimeSetup) {
        this.isFirstTimeSetup = false;
      }

      // 返回设置页面
      router.back();

    } catch (error) {
      console.error('修改支付密码失败:', error);
      const errorMessage = this.isFirstTimeSetup ? '设置失败，请重试' : '修改失败，请检查原密码是否正确';
      promptAction.showToast({ message: errorMessage });
    } finally {
      this.isSubmitting = false;
    }
  }

  /**
   * 检查支付密码状态
   */
  async checkPayPasswordStatus() {
    try {
      // 获取用户信息来判断是否已设置支付密码
      const userInfo = await UserApi.getUserInfo();

      console.log('用户信息:', userInfo);
      console.log('hasPayPassword:', userInfo.hasPayPassword);

      // 根据后端返回的hasPayPassword字段判断
      this.isFirstTimeSetup = !userInfo.hasPayPassword;

      console.log('isFirstTimeSetup:', this.isFirstTimeSetup);

    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 默认为修改模式
      this.isFirstTimeSetup = false;
    } finally {
      this.isLoading = false;
    }
  }
}
