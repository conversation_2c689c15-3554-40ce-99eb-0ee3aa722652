[2025-06-25T12:14:05.874] [DEBUG] debug-file - env: nodejsVersion=v18.20.1
[2025-06-25T12:14:08.739] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: false,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-25T12:14:08.793] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:20.590] [DEBUG] debug-file - hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }
[2025-06-25T12:14:21.068] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-25T12:14:21.091] [DEBUG] debug-file - Local scan or download HarmonyOS sdk components toolchains,ets,js,native,previewer
[2025-06-25T12:14:21.097] [DEBUG] debug-file - Local scan or download hmscore sdk components toolchains,ets,native
[2025-06-25T12:14:21.110] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-25T12:14:21.144] [DEBUG] debug-file - Sdk init in 59 ms 
[2025-06-25T12:14:21.184] [DEBUG] debug-file - Project task initialization takes 37 ms 
[2025-06-25T12:14:21.200] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:21.231] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-25T12:14:21.242] [DEBUG] debug-file - Module entry task initialization takes 5 ms 
[2025-06-25T12:14:21.344] [DEBUG] debug-file - Configuration task cost before running: 12 s 640 ms 
[2025-06-25T12:14:21.248] [DEBUG] debug-file - project has submodules:entry
[2025-06-25T12:14:21.349] [DEBUG] debug-file - Executing task :entry:clean
[2025-06-25T12:14:05.876] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  modelVersion: '5.0.3',
  dependencies: {},
  execution: {},
  logging: {},
  debugging: {},
  nodeOptions: {}
}
[2025-06-25T12:14:21.355] [DEBUG] debug-file - entry : clean cost memory 0.2402801513671875
[2025-06-25T12:14:08.743] [DEBUG] debug-file - Since current hvigor version 5.15.3 differs from last hvigor version 
      undefined, delete file-cache.json and task-cache.json.
[2025-06-25T12:14:21.402] [DEBUG] debug-file - Create  resident worker with id: 0.
[2025-06-25T12:14:21.076] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-25T12:14:20.591] [DEBUG] debug-file - hvigorfile, binding system plugins [Function: appTasks]
[2025-06-25T12:14:21.069] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-25T12:14:21.184] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T12:14:21.209] [DEBUG] debug-file - hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }
[2025-06-25T12:14:21.231] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-25T12:14:21.242] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T12:14:21.249] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-25T12:14:21.350] [DEBUG] debug-file - clean: Worker pool is inactive.
[2025-06-25T12:14:05.878] [DEBUG] debug-file - env: daemon=false
[2025-06-25T12:14:21.355] [DEBUG] debug-file - runTaskFromQueue task cost before running: 12 s 651 ms 
[2025-06-25T12:14:08.744] [DEBUG] debug-file - Cache service initialization finished in 4 ms 
[2025-06-25T12:14:21.404] [DEBUG] debug-file - Create  resident worker with id: 1.
[2025-06-25T12:14:21.084] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-25T12:14:21.375] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T12:14:21.069] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-25T12:14:21.184] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:21.209] [DEBUG] debug-file - hvigorfile, binding system plugins [Function: hapTasks]
[2025-06-25T12:14:21.232] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-25T12:14:21.243] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:21.316] [DEBUG] debug-file - load to the disk finished
[2025-06-25T12:14:05.878] [DEBUG] debug-file - no-daemon, use the parent process.execArgv --max-old-space-size=8192,--expose-gc
[2025-06-25T12:14:21.356] [INFO] debug-file - Finished :entry:clean... after 6 ms 
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Current worker pool is stopped or closed.
[2025-06-25T12:14:21.381] [DEBUG] debug-file - Module 'entry' target 'ohosTest' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
} in this build.
[2025-06-25T12:14:21.069] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-25T12:14:21.184] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:21.232] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-25T12:14:21.243] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:21.318] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T12:14:21.356] [DEBUG] debug-file - Executing task ::clean
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Clear worker 0.
[2025-06-25T12:14:21.394] [DEBUG] debug-file - Since there is no instance or instance is terminated, create a new worker pool.
[2025-06-25T12:14:21.232] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-25T12:14:21.330] [DEBUG] debug-file - Module wallet Collected Dependency: 
[2025-06-25T12:14:21.356] [DEBUG] debug-file - clean: Worker pool is inactive.
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Worker 0 has been cleared.
[2025-06-25T12:14:21.394] [DEBUG] debug-file - Worker pool is initialized with config:  {
  minPoolNum: 2,
  maxPoolNum: undefined,
  maxCoreSize: undefined,
  cacheCapacity: undefined,
  cacheTtl: undefined
}
[2025-06-25T12:14:21.232] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-25T12:14:21.330] [DEBUG] debug-file - Module wallet's total dependency: 0
[2025-06-25T12:14:21.357] [DEBUG] debug-file - wallet : clean cost memory 0.0303955078125
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Current idle worker size: 1.
[2025-06-25T12:14:21.233] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T12:14:21.331] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T12:14:21.357] [DEBUG] debug-file - runTaskFromQueue task cost before running: 12 s 653 ms 
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Current busy worker size: 0.
[2025-06-25T12:14:21.335] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-25T12:14:21.357] [INFO] debug-file - Finished ::clean... after 1 ms 
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Clear worker 1.
[2025-06-25T12:14:21.335] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-25T12:14:21.357] [DEBUG] debug-file - Executing task :entry:init
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Worker 1 has been cleared.
[2025-06-25T12:14:21.340] [DEBUG] debug-file - Configuration phase cost:12 s 577 ms 
[2025-06-25T12:14:21.357] [DEBUG] debug-file - entry : init cost memory 0.01016998291015625
[2025-06-25T12:14:21.407] [DEBUG] debug-file - Current idle worker size: 0.
[2025-06-25T12:14:21.357] [DEBUG] debug-file - runTaskFromQueue task cost before running: 12 s 654 ms 
[2025-06-25T12:14:21.408] [DEBUG] debug-file - Current busy worker size: 0.
[2025-06-25T12:14:21.357] [INFO] debug-file - Finished :entry:init... after 1 ms 
[2025-06-25T12:14:21.408] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-25T12:14:21.358] [DEBUG] debug-file - Executing task ::init
[2025-06-25T12:14:21.425] [DEBUG] debug-file - worker[0] exits with exit code 0.
[2025-06-25T12:14:21.358] [DEBUG] debug-file - wallet : init cost memory 0.00897216796875
[2025-06-25T12:14:21.425] [DEBUG] debug-file - worker[1] exits with exit code 0.
[2025-06-25T12:14:21.358] [DEBUG] debug-file - runTaskFromQueue task cost before running: 12 s 654 ms 
[2025-06-25T12:14:21.426] [DEBUG] debug-file - Current worker pool is terminated.
[2025-06-25T12:14:21.358] [INFO] debug-file - Finished ::init... after 1 ms 
[2025-06-25T12:14:26.261] [DEBUG] debug-file - env: nodejsVersion=v18.20.1
[2025-06-25T12:14:26.529] [DEBUG] debug-file - env: daemon=true
[2025-06-25T12:14:26.264] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  modelVersion: '5.0.3',
  dependencies: {},
  execution: {},
  logging: {},
  debugging: {},
  nodeOptions: {}
}
[2025-06-25T12:14:28.811] [DEBUG] debug-file - java daemon tryConnect failed Error: connect ECONNREFUSED 127.0.0.1:45050
[2025-06-25T12:14:28.866] [DEBUG] debug-file - java daemon started at port 45050 pid 6328
[2025-06-25T12:14:28.898] [DEBUG] debug-file - session manager: set active socket. socketId=qSgahDwZSYEKw4ITAAAB
[2025-06-25T12:14:29.737] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T12:14:29.763] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-25T12:14:29.765] [DEBUG] debug-file - Cache service initialization finished in 3 ms 
[2025-06-25T12:14:29.778] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:32.277] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T12:14:32.279] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T12:14:32.507] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-25T12:14:32.508] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-25T12:14:32.509] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-25T12:14:32.510] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-25T12:14:32.516] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-25T12:14:32.533] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-25T12:14:32.540] [DEBUG] debug-file - Local scan or download HarmonyOS sdk components toolchains,ets,js,native,previewer
[2025-06-25T12:14:32.546] [DEBUG] debug-file - Local scan or download hmscore sdk components toolchains,ets,native
[2025-06-25T12:14:32.560] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-25T12:14:32.591] [DEBUG] debug-file - Sdk init in 57 ms 
[2025-06-25T12:14:32.622] [DEBUG] debug-file - Project task initialization takes 29 ms 
[2025-06-25T12:14:32.622] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T12:14:32.622] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:32.622] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:32.631] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:32.638] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T12:14:32.638] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T12:14:32.653] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-25T12:14:32.653] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-25T12:14:32.654] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-25T12:14:32.654] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-25T12:14:32.654] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-25T12:14:32.654] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-25T12:14:32.655] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T12:14:32.663] [DEBUG] debug-file - Module entry task initialization takes 5 ms 
[2025-06-25T12:14:32.663] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T12:14:32.663] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:32.663] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:32.680] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 15 ms 
[2025-06-25T12:14:32.682] [DEBUG] debug-file - project has submodules:entry
[2025-06-25T12:14:32.684] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-25T12:14:32.756] [DEBUG] debug-file - load to the disk finished
[2025-06-25T12:14:32.758] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T12:14:32.772] [DEBUG] debug-file - Module wallet Collected Dependency: 
[2025-06-25T12:14:32.772] [DEBUG] debug-file - Module wallet's total dependency: 0
[2025-06-25T12:14:32.773] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T12:14:32.778] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-25T12:14:32.778] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-25T12:14:32.788] [DEBUG] debug-file - Configuration phase cost:3 s 18 ms 
[2025-06-25T12:14:32.791] [DEBUG] debug-file - Configuration task cost before running: 3 s 48 ms 
[2025-06-25T12:14:32.791] [DEBUG] debug-file - Executing task :entry:init
[2025-06-25T12:14:32.792] [DEBUG] debug-file - entry : init cost memory 0.0649261474609375
[2025-06-25T12:14:32.792] [DEBUG] debug-file - runTaskFromQueue task cost before running: 3 s 49 ms 
[2025-06-25T12:14:32.792] [INFO] debug-file - Finished :entry:init... after 1 ms 
[2025-06-25T12:14:32.792] [DEBUG] debug-file - Executing task ::init
[2025-06-25T12:14:32.792] [DEBUG] debug-file - wallet : init cost memory 0.01282501220703125
[2025-06-25T12:14:32.792] [DEBUG] debug-file - runTaskFromQueue task cost before running: 3 s 49 ms 
[2025-06-25T12:14:32.793] [INFO] debug-file - Finished ::init... after 1 ms 
[2025-06-25T12:14:32.815] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T12:14:32.824] [DEBUG] debug-file - Module 'entry' target 'ohosTest' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
} in this build.
[2025-06-25T12:14:32.835] [DEBUG] debug-file - Since there is no instance or instance is terminated, create a new worker pool.
[2025-06-25T12:14:32.851] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T12:14:32.852] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T12:14:32.835] [DEBUG] debug-file - Worker pool is initialized with config:  {
  minPoolNum: 2,
  maxPoolNum: undefined,
  maxCoreSize: undefined,
  cacheCapacity: undefined,
  cacheTtl: undefined
}
[2025-06-25T12:14:32.838] [DEBUG] debug-file - Create  resident worker with id: 0.
[2025-06-25T12:14:32.841] [DEBUG] debug-file - Create  resident worker with id: 1.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Cleanup worker 0.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Worker 0 has been cleaned up.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Current idle worker size: 1.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Current resident worker size: 2.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Cleanup worker 1.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Worker 1 has been cleaned up.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Current idle worker size: 0.
[2025-06-25T12:14:32.845] [DEBUG] debug-file - Current resident worker size: 2.
[2025-06-25T12:14:32.846] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-25T12:14:32.848] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T12:14:32.849] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T12:14:32.857] [DEBUG] debug-file - worker[0] exits with exit code 0.
[2025-06-25T12:14:32.862] [DEBUG] debug-file - worker[1] exits with exit code 0.
[2025-06-25T12:14:33.133] [DEBUG] debug-file - session manager: set active socket. socketId=pdjZSrDoZXBA_742AAAD
[2025-06-25T12:14:33.137] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T12:14:33.154] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-25T12:14:33.155] [DEBUG] debug-file - Cache service initialization finished in 2 ms 
[2025-06-25T12:14:33.171] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:33.176] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T12:14:33.176] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T12:14:33.183] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-25T12:14:33.183] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-25T12:14:33.184] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-25T12:14:33.184] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-25T12:14:33.186] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-25T12:14:33.190] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-25T12:14:33.199] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-25T12:14:33.220] [DEBUG] debug-file - Sdk init in 30 ms 
[2025-06-25T12:14:33.242] [DEBUG] debug-file - Project task initialization takes 21 ms 
[2025-06-25T12:14:33.242] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T12:14:33.242] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:33.242] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\hvigorfile.ts
[2025-06-25T12:14:33.249] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:33.255] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T12:14:33.255] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T12:14:33.262] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-25T12:14:33.262] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-25T12:14:33.263] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-25T12:14:33.263] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-25T12:14:33.263] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-25T12:14:33.263] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-25T12:14:33.263] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T12:14:33.266] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-25T12:14:33.266] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T12:14:33.266] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:33.266] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\wallet\entry\hvigorfile.ts
[2025-06-25T12:14:33.281] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 14 ms 
[2025-06-25T12:14:33.283] [DEBUG] debug-file - project has submodules:entry
[2025-06-25T12:14:33.286] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-25T12:14:33.347] [DEBUG] debug-file - load to the disk finished
[2025-06-25T12:14:33.348] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T12:14:33.353] [DEBUG] debug-file - Module wallet Collected Dependency: 
[2025-06-25T12:14:33.353] [DEBUG] debug-file - Module wallet's total dependency: 0
[2025-06-25T12:14:33.353] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T12:14:33.355] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-25T12:14:33.356] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-25T12:14:33.358] [DEBUG] debug-file - Configuration phase cost:197 ms 
[2025-06-25T12:14:33.359] [DEBUG] debug-file - Configuration task cost before running: 219 ms 
[2025-06-25T12:14:33.362] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.362] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.366] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-25T12:14:33.373] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.373] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.479] [DEBUG] debug-file - current product is not Atomic service.
[2025-06-25T12:14:33.480] [DEBUG] debug-file - Use tool [win32: JAVA_HOME, CLASSPATH]
 [
  { JAVA_HOME: 'D:\\app\\devecostudio\\DevEco Studio\\jbr' },
  { CLASSPATH: undefined }
]
[2025-06-25T12:14:33.480] [DEBUG] debug-file - Use tool [win32: NODE_HOME]
 [ { NODE_HOME: 'D:\\app\\devecostudio\\DevEco Studio\\tools\\node' } ]
[2025-06-25T12:14:33.481] [DEBUG] debug-file - entry : default@PreBuild cost memory 12.284629821777344
[2025-06-25T12:14:33.481] [DEBUG] debug-file - runTaskFromQueue task cost before running: 341 ms 
[2025-06-25T12:14:33.483] [INFO] debug-file - Finished :entry:default@PreBuild... after 115 ms 
[2025-06-25T12:14:33.486] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.486] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.487] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-25T12:14:33.489] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.489] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.489] [DEBUG] debug-file - Change app api release type with 'Release'
[2025-06-25T12:14:33.489] [DEBUG] debug-file - Change app compile API version with '*********'
[2025-06-25T12:14:33.489] [DEBUG] debug-file - Change app target API version with '50003015'
[2025-06-25T12:14:33.490] [DEBUG] debug-file - Change app minimum API version with '50003015'
[2025-06-25T12:14:33.490] [DEBUG] debug-file - Use cli appEnvironment
[2025-06-25T12:14:33.494] [DEBUG] debug-file - entry : default@MergeProfile cost memory -13.746086120605469
[2025-06-25T12:14:33.494] [DEBUG] debug-file - runTaskFromQueue task cost before running: 354 ms 
[2025-06-25T12:14:33.494] [INFO] debug-file - Finished :entry:default@MergeProfile... after 8 ms 
[2025-06-25T12:14:33.497] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.497] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.498] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-25T12:14:33.499] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms 
[2025-06-25T12:14:33.499] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.499] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.502] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.1068267822265625
[2025-06-25T12:14:33.502] [DEBUG] debug-file - runTaskFromQueue task cost before running: 362 ms 
[2025-06-25T12:14:33.502] [INFO] debug-file - Finished :entry:default@CreateBuildProfile... after 5 ms 
[2025-06-25T12:14:33.504] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.504] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.505] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-25T12:14:33.505] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.505] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.506] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.03759765625
[2025-06-25T12:14:33.506] [DEBUG] debug-file - runTaskFromQueue task cost before running: 366 ms 
[2025-06-25T12:14:33.506] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-25T12:14:33.508] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.509] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.518] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-25T12:14:33.518] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-25T12:14:33.519] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.04282379150390625
[2025-06-25T12:14:33.519] [DEBUG] debug-file - runTaskFromQueue task cost before running: 379 ms 
[2025-06-25T12:14:33.520] [INFO] debug-file - Finished :entry:default@GeneratePkgContextInfo... after 2 ms 
[2025-06-25T12:14:33.522] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.522] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.524] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-25T12:14:33.524] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.524] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.526] [DEBUG] debug-file - [
  'D:\\app\\devecostudio\\DevEco Studio\\tools\\node\\node.exe',
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\ets\\build-tools\\ets-loader\\bin\\ark\\ts2abc.js',
  '--target-api-version',
  '15'
]
[2025-06-25T12:14:33.709] [DEBUG] debug-file - ********
[2025-06-25T12:14:33.714] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 1.9788284301757812
[2025-06-25T12:14:33.714] [DEBUG] debug-file - runTaskFromQueue task cost before running: 574 ms 
[2025-06-25T12:14:33.714] [INFO] debug-file - Finished :entry:default@ProcessProfile... after 190 ms 
[2025-06-25T12:14:33.718] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.718] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.720] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-25T12:14:33.723] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.724] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.727] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.150177001953125
[2025-06-25T12:14:33.727] [DEBUG] debug-file - runTaskFromQueue task cost before running: 587 ms 
[2025-06-25T12:14:33.729] [INFO] debug-file - Finished :entry:default@ProcessRouterMap... after 7 ms 
[2025-06-25T12:14:33.731] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.732] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.734] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-25T12:14:33.738] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-25T12:14:33.738] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.738] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.738] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.07375335693359375
[2025-06-25T12:14:33.739] [DEBUG] debug-file - runTaskFromQueue task cost before running: 599 ms 
[2025-06-25T12:14:33.741] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 3 ms 
[2025-06-25T12:14:33.743] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.743] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.748] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-25T12:14:33.772] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.7722091674804688
[2025-06-25T12:14:33.772] [DEBUG] debug-file - runTaskFromQueue task cost before running: 632 ms 
[2025-06-25T12:14:33.779] [INFO] debug-file - Finished :entry:default@GenerateLoaderJson... after 25 ms 
[2025-06-25T12:14:33.781] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:33.781] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:33.782] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-25T12:14:33.785] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-25T12:14:33.793] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\resources',
  '-o',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled'
]
[2025-06-25T12:14:33.795] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 209657856,
  heapTotal: 158199808,
  heapUsed: 130996128,
  external: 3295817,
  arrayBuffers: 285072
} os memoryUsage :12.628124237060547
[2025-06-25T12:14:33.894] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T12:14:33.897] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources',
  '-o',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled'
]
[2025-06-25T12:14:33.899] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 209686528,
  heapTotal: 158199808,
  heapUsed: 131258120,
  external: 3295943,
  arrayBuffers: 285213
} os memoryUsage :12.629745483398438
[2025-06-25T12:14:33.934] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T12:14:33.944] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-m',
  'entry',
  '-f',
  '-j',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json',
  '-p',
  'c***n',
  '-r',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h',
  '-z',
  '--ids',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map',
  '-i',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled',
  '-i',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled',
  '-o',
  'D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default'
]
[2025-06-25T12:14:33.946] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 209768448,
  heapTotal: 158199808,
  heapUsed: 131534608,
  external: 3304261,
  arrayBuffers: 294238
} os memoryUsage :12.63656234741211
[2025-06-25T12:14:34.007] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T12:14:34.027] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory 1.1359710693359375
[2025-06-25T12:14:34.027] [DEBUG] debug-file - runTaskFromQueue task cost before running: 887 ms 
[2025-06-25T12:14:34.028] [INFO] debug-file - Finished :entry:default@PreviewCompileResource... after 243 ms 
[2025-06-25T12:14:34.031] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.031] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.031] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-25T12:14:34.031] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.031] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.031] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.039154052734375
[2025-06-25T12:14:34.031] [DEBUG] debug-file - runTaskFromQueue task cost before running: 891 ms 
[2025-06-25T12:14:34.031] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-25T12:14:34.034] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.034] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.035] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-25T12:14:34.035] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.035] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.042] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.40596771240234375
[2025-06-25T12:14:34.042] [DEBUG] debug-file - runTaskFromQueue task cost before running: 902 ms 
[2025-06-25T12:14:34.043] [INFO] debug-file - Finished :entry:default@CopyPreviewProfile... after 8 ms 
[2025-06-25T12:14:34.045] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.045] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.046] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-25T12:14:34.046] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.046] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.047] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.03907012939453125
[2025-06-25T12:14:34.047] [DEBUG] debug-file - runTaskFromQueue task cost before running: 907 ms 
[2025-06-25T12:14:34.047] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-25T12:14:34.049] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-25T12:14:34.049] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.01219940185546875
[2025-06-25T12:14:34.049] [DEBUG] debug-file - runTaskFromQueue task cost before running: 909 ms 
[2025-06-25T12:14:34.049] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-25T12:14:34.052] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.052] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.054] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-25T12:14:34.057] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.057] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.068] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.08829498291015625
[2025-06-25T12:14:34.068] [DEBUG] debug-file - runTaskFromQueue task cost before running: 928 ms 
[2025-06-25T12:14:34.069] [INFO] debug-file - Finished :entry:default@PreviewUpdateAssets... after 15 ms 
[2025-06-25T12:14:34.074] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:14:34.074] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:14:34.083] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-25T12:14:34.131] [DEBUG] debug-file - session manager: binding session. socketId=pdjZSrDoZXBA_742AAAD, threadId=1@1.
[2025-06-25T12:14:34.122] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory 1.3090362548828125
[2025-06-25T12:14:40.264] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-25T12:14:40.281] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
