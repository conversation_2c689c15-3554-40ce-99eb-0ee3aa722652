package com.icss.springbootbig.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;

@Data
@TableName("users")
public class User {
    @TableId(type = IdType.AUTO)
    private Integer userId;

    private String phone;
    private String password;
    private Integer status; // 1-正常 0-冻结

    // 数据库中的字段名是 last_login，不是 lastLoginTime
    @TableField("last_login")
    private Date lastLogin;

    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

    // username字段在数据库中存在
    private String username;

    // 以下字段在数据库中不存在，标记为不存在的字段
    @TableField(exist = false)
    private String realName;

    @TableField(exist = false)
    private String idCard;

    @TableField(exist = false)
    private String email;

    @TableField(exist = false)
    private String avatar;

    @TableField(exist = false)
    private Integer loginAttempts;

    @TableField(exist = false)
    private Date lastLoginTime;

    @TableField(exist = false)
    private Integer fingerprintEnabled;
}