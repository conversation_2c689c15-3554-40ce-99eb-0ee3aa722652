if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface RechargePage_Params {
    amount?: string;
    payPassword?: string;
    selectedCardId?: number;
    bankCards?: BankCard[];
    isLoading?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { WalletApi } from "@normalized:N&&&entry/src/main/ets/api/WalletApi&";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
import { globalStateManager, RefreshTypes } from "@normalized:N&&&entry/src/main/ets/common/utils/EventManager&";
import type { WalletRechargeRequest, BankCard, RechargePageParams } from '../common/types/index';
import { tempDataManager } from "@normalized:N&&&entry/src/main/ets/common/storage/TempDataManager&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
class RechargePage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__amount = new ObservedPropertySimplePU('', this, "amount");
        this.__payPassword = new ObservedPropertySimplePU('', this, "payPassword");
        this.__selectedCardId = new ObservedPropertySimplePU(-1, this, "selectedCardId");
        this.__bankCards = new ObservedPropertyObjectPU([], this, "bankCards");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: RechargePage_Params) {
        if (params.amount !== undefined) {
            this.amount = params.amount;
        }
        if (params.payPassword !== undefined) {
            this.payPassword = params.payPassword;
        }
        if (params.selectedCardId !== undefined) {
            this.selectedCardId = params.selectedCardId;
        }
        if (params.bankCards !== undefined) {
            this.bankCards = params.bankCards;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
    }
    updateStateVars(params: RechargePage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__amount.purgeDependencyOnElmtId(rmElmtId);
        this.__payPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCardId.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCards.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__amount.aboutToBeDeleted();
        this.__payPassword.aboutToBeDeleted();
        this.__selectedCardId.aboutToBeDeleted();
        this.__bankCards.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __amount: ObservedPropertySimplePU<string>;
    get amount() {
        return this.__amount.get();
    }
    set amount(newValue: string) {
        this.__amount.set(newValue);
    }
    private __payPassword: ObservedPropertySimplePU<string>;
    get payPassword() {
        return this.__payPassword.get();
    }
    set payPassword(newValue: string) {
        this.__payPassword.set(newValue);
    }
    private __selectedCardId: ObservedPropertySimplePU<number>;
    get selectedCardId() {
        return this.__selectedCardId.get();
    }
    set selectedCardId(newValue: number) {
        this.__selectedCardId.set(newValue);
    }
    private __bankCards: ObservedPropertyObjectPU<BankCard[]>;
    get bankCards() {
        return this.__bankCards.get();
    }
    set bankCards(newValue: BankCard[]) {
        this.__bankCards.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    aboutToAppear() {
        this.loadBankCards();
    }
    onPageShow() {
        // 检查是否有选择的银行卡返回
        const params = router.getParams() as RechargePageParams;
        if (params?.selectedCard) {
            const selectedCard = params.selectedCard;
            console.log('收到选择的银行卡:', selectedCard);
            // 更新选中的银行卡ID
            this.selectedCardId = selectedCard.cardId;
            // 如果银行卡列表中没有这张卡，添加到列表中
            const existingCard = this.bankCards.find(card => card.cardId === selectedCard.cardId);
            if (!existingCard) {
                this.bankCards.push(selectedCard);
            }
            promptAction.showToast({ message: `已选择 ${selectedCard.bankName}` });
        }
        // 检查是否有银行卡添加事件
        const cardAdded = tempDataManager.getData('BANK_CARD_ADDED');
        console.log('RechargePage onPageShow - 检查银行卡添加事件:', cardAdded);
        if (cardAdded) {
            console.log('RechargePage - 检测到银行卡添加，重新加载银行卡列表');
            // 重新加载银行卡列表
            this.loadBankCards();
            // 不删除事件标志，让其他页面也能处理
        }
        // 检查是否有银行卡解绑事件
        const cardUnbound = tempDataManager.getData('BANK_CARD_UNBOUND');
        console.log('RechargePage onPageShow - 检查银行卡解绑事件:', cardUnbound);
        if (cardUnbound) {
            console.log('RechargePage - 检测到银行卡解绑，重新加载银行卡列表');
            // 重新加载银行卡列表
            this.loadBankCards();
            // 重置选中的银行卡（如果被解绑的话）
            if (this.selectedCardId !== -1) {
                const stillExists = this.bankCards.some(card => card.cardId === this.selectedCardId);
                if (!stillExists) {
                    this.selectedCardId = -1;
                }
            }
            // 不删除事件标志，让其他页面也能处理
        }
    }
    async loadBankCards() {
        try {
            // 只获取已绑定的银行卡
            this.bankCards = await BankCardApi.getCardList(1);
        }
        catch (error) {
            console.error('获取银行卡列表失败:', error);
            promptAction.showToast({ message: '获取银行卡列表失败' });
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RechargePage.ets(82:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/RechargePage.ets(84:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('返回');
            Button.debugLine("entry/src/main/ets/pages/RechargePage.ets(85:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包充值');
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(93:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(99:9)", "entry");
            Text.width(60);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/RechargePage.ets(107:7)", "entry");
            Scroll.layoutWeight(1);
            Scroll.backgroundColor('#F5F5F5');
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RechargePage.ets(108:9)", "entry");
            Column.padding({ left: 16, right: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 充值金额输入
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RechargePage.ets(112:11)", "entry");
            // 充值金额输入
            Column.width('100%');
            // 充值金额输入
            Column.padding(20);
            // 充值金额输入
            Column.margin({ top: 12 });
            // 充值金额输入
            Column.borderRadius(12);
            // 充值金额输入
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('充值金额');
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(113:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入充值金额', text: this.amount });
            TextInput.debugLine("entry/src/main/ets/pages/RechargePage.ets(120:13)", "entry");
            TextInput.type(InputType.Number);
            TextInput.fontSize(24);
            TextInput.fontWeight(FontWeight.Bold);
            TextInput.height(60);
            TextInput.borderRadius(12);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.onChange((value: string) => {
                this.amount = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 快捷金额按钮
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/RechargePage.ets(133:13)", "entry");
            // 快捷金额按钮
            Row.width('100%');
            // 快捷金额按钮
            Row.justifyContent(FlexAlign.SpaceBetween);
            // 快捷金额按钮
            Row.margin({ top: 16 });
        }, Row);
        this.QuickAmountButton.bind(this)('100');
        this.QuickAmountButton.bind(this)('500');
        this.QuickAmountButton.bind(this)('1000');
        this.QuickAmountButton.bind(this)('2000');
        // 快捷金额按钮
        Row.pop();
        // 充值金额输入
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 选择银行卡
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RechargePage.ets(150:11)", "entry");
            // 选择银行卡
            Column.width('100%');
            // 选择银行卡
            Column.padding(20);
            // 选择银行卡
            Column.margin({ top: 12 });
            // 选择银行卡
            Column.borderRadius(12);
            // 选择银行卡
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择银行卡');
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(151:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.bankCards.length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/RechargePage.ets(159:15)", "entry");
                        Column.width('100%');
                        Column.height(120);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                        Column.backgroundColor('#F8F9FA');
                        Column.borderRadius(8);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无可用银行卡');
                        Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(160:17)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('请先添加银行卡后再进行充值');
                        Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(164:17)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#999999');
                        Text.margin({ top: 8 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('去我的银行卡添加');
                        Button.debugLine("entry/src/main/ets/pages/RechargePage.ets(169:17)", "entry");
                        Button.fontSize(14);
                        Button.fontColor('#1976D2');
                        Button.backgroundColor('#E3F2FD');
                        Button.borderRadius(8);
                        Button.margin({ top: 12 });
                        Button.onClick(() => {
                            router.pushUrl({
                                url: 'pages/BankCardPage',
                                params: {
                                    selectMode: false // 管理模式，不是选择模式
                                }
                            });
                        });
                    }, Button);
                    Button.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/RechargePage.ets(191:15)", "entry");
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = (_item, index: number) => {
                            const card = _item;
                            this.BankCardItem.bind(this)(card, index);
                        };
                        this.forEachUpdateFunction(elmtId, this.bankCards, forEachItemGenFunction, undefined, true, false);
                    }, ForEach);
                    ForEach.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        // 选择银行卡
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付密码输入
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RechargePage.ets(205:11)", "entry");
            // 支付密码输入
            Column.width('100%');
            // 支付密码输入
            Column.padding(20);
            // 支付密码输入
            Column.margin({ top: 12 });
            // 支付密码输入
            Column.borderRadius(12);
            // 支付密码输入
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付密码');
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(206:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入6位支付密码' });
            TextInput.debugLine("entry/src/main/ets/pages/RechargePage.ets(213:13)", "entry");
            TextInput.type(InputType.Password);
            TextInput.maxLength(6);
            TextInput.fontSize(16);
            TextInput.height(48);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.showPasswordIcon(true);
            TextInput.onChange((value: string) => {
                this.payPassword = value;
            });
        }, TextInput);
        // 支付密码输入
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 充值按钮
            Button.createWithLabel('确认充值');
            Button.debugLine("entry/src/main/ets/pages/RechargePage.ets(233:11)", "entry");
            // 充值按钮
            Button.width('100%');
            // 充值按钮
            Button.height(48);
            // 充值按钮
            Button.fontSize(16);
            // 充值按钮
            Button.fontColor(Color.White);
            // 充值按钮
            Button.backgroundColor('#1976D2');
            // 充值按钮
            Button.borderRadius(8);
            // 充值按钮
            Button.margin({ top: 24, bottom: 20 });
            // 充值按钮
            Button.enabled(!this.isLoading && this.isFormValid());
            // 充值按钮
            Button.opacity((!this.isLoading && this.isFormValid()) ? 1 : 0.5);
            // 充值按钮
            Button.onClick(() => {
                this.handleRecharge();
            });
        }, Button);
        // 充值按钮
        Button.pop();
        Column.pop();
        Scroll.pop();
        Column.pop();
    }
    QuickAmountButton(amount: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(amount);
            Button.debugLine("entry/src/main/ets/pages/RechargePage.ets(260:5)", "entry");
            Button.fontSize(14);
            Button.fontColor('#1976D2');
            Button.backgroundColor('#E3F2FD');
            Button.borderRadius(8);
            Button.width('22%');
            Button.height(36);
            Button.onClick(() => {
                this.amount = amount;
            });
        }, Button);
        Button.pop();
    }
    BankCardItem(card: BankCard, index: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/RechargePage.ets(274:5)", "entry");
            Row.width('100%');
            Row.padding(12);
            Row.margin({ bottom: 8 });
            Row.borderRadius(8);
            Row.backgroundColor(this.selectedCardId === card.cardId ? '#E3F2FD' : '#F8F9FA');
            Row.border({
                width: 1,
                color: this.selectedCardId === card.cardId ? '#1976D2' : '#E0E0E0'
            });
            Row.onClick(() => {
                this.selectedCardId = card.cardId;
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Radio.create({ value: index.toString(), group: 'bankCard' });
            Radio.debugLine("entry/src/main/ets/pages/RechargePage.ets(275:7)", "entry");
            Radio.checked(this.selectedCardId === card.cardId);
            Radio.onChange((isChecked: boolean) => {
                if (isChecked) {
                    this.selectedCardId = card.cardId;
                }
            });
        }, Radio);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RechargePage.ets(283:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ left: 12 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.bankName);
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(284:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${card.cardType} **** ${card.cardNo.slice(-4)}`);
            Text.debugLine("entry/src/main/ets/pages/RechargePage.ets(290:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    isFormValid(): boolean {
        const amountNum = parseFloat(this.amount);
        return !isNaN(amountNum) &&
            amountNum > 0 &&
            amountNum <= 50000 &&
            this.selectedCardId !== -1 &&
            this.payPassword.length === 6;
    }
    async handleRecharge() {
        if (this.isLoading)
            return;
        const amountNum = parseFloat(this.amount);
        if (isNaN(amountNum) || amountNum <= 0) {
            promptAction.showToast({ message: '请输入正确的充值金额' });
            return;
        }
        if (amountNum > 50000) {
            promptAction.showToast({ message: '单次充值金额不能超过50000元' });
            return;
        }
        if (this.selectedCardId === -1) {
            promptAction.showToast({ message: '请选择银行卡' });
            return;
        }
        if (this.payPassword.length !== 6) {
            promptAction.showToast({ message: '请输入6位支付密码' });
            return;
        }
        this.isLoading = true;
        try {
            // 获取当前用户ID
            const cachedUserInfo = await storageManager.getUserInfo();
            if (!cachedUserInfo || !cachedUserInfo.userId) {
                promptAction.showToast({ message: '用户信息获取失败，请重新登录' });
                return;
            }
            const rechargeData: WalletRechargeRequest = {
                userId: cachedUserInfo.userId,
                amount: amountNum,
                cardId: this.selectedCardId,
                payPassword: this.payPassword
            };
            await WalletApi.recharge(rechargeData);
            promptAction.showToast({ message: '充值成功' });
            // 标记需要刷新钱包数据
            globalStateManager.markForRefresh(RefreshTypes.WALLET);
            // 返回主页
            router.back();
        }
        catch (error) {
            console.error('充值失败:', error);
            let errorMessage = '充值失败，请稍后重试';
            if (error instanceof Error) {
                errorMessage = `充值失败: ${error.message}`;
            }
            promptAction.showToast({ message: errorMessage });
        }
        finally {
            this.isLoading = false;
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "RechargePage";
    }
}
registerNamedRoute(() => new RechargePage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/RechargePage", pageFullPath: "entry/src/main/ets/pages/RechargePage", integratedHsp: "false", moduleType: "followWithHap" });
