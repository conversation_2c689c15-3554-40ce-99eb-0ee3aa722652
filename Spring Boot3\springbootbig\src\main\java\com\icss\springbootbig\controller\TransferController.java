package com.icss.springbootbig.controller;

import com.icss.springbootbig.exception.ApiException;
import com.icss.springbootbig.result.R;
import com.icss.springbootbig.service.TransferService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
@CrossOrigin(origins = {"http://localhost:5173", "http://127.0.0.1:5173"})
@RestController
@RequestMapping("/api/transfer")
public class TransferController {
    @Autowired
    private TransferService transferService;

    @PostMapping
    public R<String> transfer(
            @RequestParam Integer fromUserId,
            @RequestParam Integer toUserId,
            @RequestParam BigDecimal amount) {
        try {
            // 参数校验
            if (fromUserId == null || toUserId == null || amount == null) {
                return R.failure("参数不能为空");
            }
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                return R.failure("转账金额必须大于0");
            }
            if (fromUserId.equals(toUserId)) {
                return R.failure("不能转账给自己");
            }

            transferService.transfer(fromUserId, toUserId, amount);
            return R.success("转账成功");
        } catch (ApiException e) {
            return R.failure(e.getMessage());
        } catch (Exception e) {
            return R.failure("系统错误: " + e.getMessage());
        }
    }
}