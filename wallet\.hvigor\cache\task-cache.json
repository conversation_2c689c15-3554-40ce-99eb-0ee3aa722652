{":wallet:entry:default@PreBuild": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":15,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":15,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetStatusCode\",\"_value\":2,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apiType\",\"_value\":\"stageMode\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceType\",\"_value\":[\"phone\",\"tablet\",\"2in1\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"codeType\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"*********\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"profileModuleName\",\"_value\":\"entry\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"jsonFilePath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\module.json5\\\",\\\"profile\\\":{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":false,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}],\\\"extensionAbilities\\\":[{\\\"name\\\":\\\"EntryBackupAbility\\\",\\\"srcEntry\\\":\\\"./ets/entrybackupability/EntryBackupAbility.ets\\\",\\\"type\\\":\\\"backup\\\",\\\"exported\\\":false,\\\"metadata\\\":[{\\\"name\\\":\\\"ohos.extension.backup\\\",\\\"resource\\\":\\\"$profile:backup_config\\\"}]}]}},\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deviceConfig\\\":\\\"deviceTypes\\\",\\\"configurationProfile\\\":\\\"module.json5\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSupportOhpmProj\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integrated_hsp\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sourceRoots\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configuration\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configurationFile<PERSON>son\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"mockConfigSources\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@PreBuild", "_key": ":wallet:entry:default@PreBuild", "_executionId": ":wallet:entry:default@PreBuild:1750824873370", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\app.json5", {"isDirectory": false, "fileSnapShotHashValue": "a8c1646eefce46bc0d4107bbdc054086"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5", {"isDirectory": false, "fileSnapShotHashValue": "3eb6a9d08f34df77dcc54dcc1e5c62bf"}], ["D:\\vue\\daxiangmuwallet\\wallet\\build-profile.json5", {"fileSnapShotHashValue": "c4b3fba7184e63bfc5ecd1aa6906b872"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\build-profile.json5", {"fileSnapShotHashValue": "d6e4e3f6b623f30d5d18a9ae8c85a180"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources\\base\\profile\\main_pages.json", {"fileSnapShotHashValue": "92fc1089961fcf89ff9ab18227d5890e"}], ["D:\\vue\\daxiangmuwallet\\wallet\\hvigor\\hvigor-config.json5", {"isDirectory": true, "fileSnapShotHashValue": "292e8df8413f77f25951022589c777bd"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5", {"isDirectory": false, "fileSnapShotHashValue": "c56dcb1db162f96dba73188827c40cc9"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\oh-package.json5", {"fileSnapShotHashValue": "214c89d78cb9691df413f8064dc0a807"}], ["D:\\vue\\daxiangmuwallet\\wallet\\oh-package.json5", {"fileSnapShotHashValue": "d598e3ca9be157ef14b3fb83e9d27d06"}]]}, "_outputFiles": {"dataType": "Map", "value": []}}, ":wallet:entry:default@MergeProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"targetSdkVersion\",\"_value\":15,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":15,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildRoot\",\"_value\":\".preview\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isDebug\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"asanEnable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"tsan<PERSON>nable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hwasan<PERSON><PERSON><PERSON>\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ubsanEnable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\",\\\"2in1\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":false,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}],\\\"extensionAbilities\\\":[{\\\"name\\\":\\\"EntryBackupAbility\\\",\\\"srcEntry\\\":\\\"./ets/entrybackupability/EntryBackupAbility.ets\\\",\\\"type\\\":\\\"backup\\\",\\\"exported\\\":false,\\\"metadata\\\":[{\\\"name\\\":\\\"ohos.extension.backup\\\",\\\"resource\\\":\\\"$profile:backup_config\\\"}]}]}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarModule\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"multiProjects\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileAbilities\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"appJsonOpt\",\"_value\":\"{\\\"app\\\":{\\\"bundleName\\\":\\\"com.icss.myapplication\\\",\\\"vendor\\\":\\\"example\\\",\\\"versionCode\\\":1000000,\\\"versionName\\\":\\\"1.0.0\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:app_name\\\"}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integratedHsp\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@MergeProfile", "_key": ":wallet:entry:default@MergeProfile", "_executionId": ":wallet:entry:default@MergeProfile:1750824873488", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\app.json5", {"fileSnapShotHashValue": "a8c1646eefce46bc0d4107bbdc054086"}], ["D:\\vue\\daxiangmuwallet\\wallet\\build-profile.json5", {"fileSnapShotHashValue": "c4b3fba7184e63bfc5ecd1aa6906b872"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "3eb6a9d08f34df77dcc54dcc1e5c62bf"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "961c9698e14e8fea6f0778733ba94e75"}]]}}, ":wallet:entry:default@CreateBuildProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"buildMode\",\"_value\":\"Debug\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildModeName\",\"_value\":\"debug\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileFields\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectOhosConfigAppOpt\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@CreateBuildProfile", "_key": ":wallet:entry:default@CreateBuildProfile", "_executionId": ":wallet:entry:default@CreateBuildProfile:1750824873499", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\app.json5", {"fileSnapShotHashValue": "a8c1646eefce46bc0d4107bbdc054086"}], ["D:\\vue\\daxiangmuwallet\\wallet\\build-profile.json5", {"fileSnapShotHashValue": "c4b3fba7184e63bfc5ecd1aa6906b872"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "a97543b30f7e182ee8d0bd92f654a1ea"}]]}}, ":wallet:entry:default@GeneratePkgContextInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-@ohos/hypium\",\"_value\":\"69dbd9141bebef47c71175aada1bd7bf\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-@ohos/hamock\",\"_value\":\"566d0d1ba1afb93928c8984a8fae6421\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-entry\",\"_value\":\"a8164913e569f3b3b2485e1811ddf44d\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@GeneratePkgContextInfo", "_key": ":wallet:entry:default@GeneratePkgContextInfo", "_executionId": ":wallet:entry:default@GeneratePkgContextInfo:1750824873519", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"isDirectory": false, "fileSnapShotHashValue": "7385b645a8e7906f1ce5a33b79fbabe5"}]]}}, ":wallet:entry:default@ProcessProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"arkEnable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"phone\",\"tablet\",\"2in1\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"dependency\",\"_value\":\"[]\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@ProcessProfile", "_key": ":wallet:entry:default@ProcessProfile", "_executionId": ":wallet:entry:default@ProcessProfile:1750824873524", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "961c9698e14e8fea6f0778733ba94e75"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json", {"fileSnapShotHashValue": "ad8d0f963bc11d757ace2191175ad195"}]]}}, ":wallet:entry:default@ProcessRouterMap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"obfuscated\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@ProcessRouterMap", "_key": ":wallet:entry:default@ProcessRouterMap", "_executionId": ":wallet:entry:default@ProcessRouterMap:1750824873722", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\oh-package.json5", {"fileSnapShotHashValue": "214c89d78cb9691df413f8064dc0a807"}], ["D:\\vue\\daxiangmuwallet\\wallet\\oh-package.json5", {"fileSnapShotHashValue": "d598e3ca9be157ef14b3fb83e9d27d06"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5", {"fileSnapShotHashValue": "3eb6a9d08f34df77dcc54dcc1e5c62bf"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "7385b645a8e7906f1ce5a33b79fbabe5"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json", {"fileSnapShotHashValue": "5471cee32ce4da8a174795a01f1d9530"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\loader-router-map.json", {"fileSnapShotHashValue": "1107544c7eb2c9ae8d344fbd64d8e0f7"}]]}}, ":wallet:entry:default@GenerateLoaderJson": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"projectRootPath\",\"_value\":\"D:\\\\vue\\\\daxiangmuwallet\\\\wallet\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"modulePathMap\",\"_value\":\"{\\\"entry\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"module_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"overrides\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"project_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isOhosTest\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isFullCompilationEnabled\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"patchConfig\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"nodeModulesPath\",\"_value\":\"D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\loader_out\\\\default\\\\node_modules\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetConfig\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hspNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"harNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"anBuildMode\",\"_value\":\"type\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apPath\",\"_value\":\"D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\modules.ap\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"fallbackAnBuild\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarWithCoverage\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":15,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":15,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"shouldTreatHarAsHap\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needSubmitArkTsWidget\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@GenerateLoaderJson", "_key": ":wallet:entry:default@GenerateLoaderJson", "_executionId": ":wallet:entry:default@GenerateLoaderJson:1750824873759", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5", {"fileSnapShotHashValue": "c56dcb1db162f96dba73188827c40cc9"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "7385b645a8e7906f1ce5a33b79fbabe5"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json", {"fileSnapShotHashValue": "5471cee32ce4da8a174795a01f1d9530"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", {"isDirectory": false, "fileSnapShotHashValue": "f8f9782f6ec75415749b17eeb69a979a"}]]}}, ":wallet:entry:default@PreviewCompileResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"PREVIEWER_REPLACE_PAGE\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"PREVIEWER_REPLACE_SRCPATH\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"TARGET_CONFIG\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@PreviewCompileResource", "_key": ":wallet:entry:default@PreviewCompileResource", "_executionId": ":wallet:entry:default@PreviewCompileResource:1750824873786", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources", {"fileSnapShotHashValue": "9fb853d53af926ec1d5d93e8744c3e0d"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json", {"fileSnapShotHashValue": "961c9698e14e8fea6f0778733ba94e75"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default", {"isDirectory": true, "fileSnapShotHashValue": "fa66a760d6b15bc2c4f38dbdc587fe5b"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\r\\default", {"isDirectory": true, "fileSnapShotHashValue": "526535a6fd214aeffa278df4f267013f"}]]}}, ":wallet:entry:default@CopyPreviewProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@CopyPreviewProfile", "_key": ":wallet:entry:default@CopyPreviewProfile", "_executionId": ":wallet:entry:default@CopyPreviewProfile:1750824874035", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources\\base\\profile", {"fileSnapShotHashValue": "ccfe67585ba94d77ea6d5d52799b4615"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", {"fileSnapShotHashValue": "2c69eb4cc459b89fe14185cc907b22db"}]]}}, ":wallet:entry:default@PreviewUpdateAssets": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"previewBuildConfigJson\",\"_value\":\"{\\\"deviceType\\\":\\\"phone,tablet,2in1\\\",\\\"buildMode\\\":\\\"debug\\\",\\\"note\\\":\\\"false\\\",\\\"logLevel\\\":\\\"3\\\",\\\"isPreview\\\":\\\"true\\\",\\\"checkEntry\\\":\\\"true\\\",\\\"localPropertiesPath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\local.properties\\\",\\\"Path\\\":\\\"D:\\\\\\\\app\\\\\\\\devecostudio\\\\\\\\DevEco Studio\\\\\\\\tools\\\\\\\\node\\\\\\\\\\\",\\\"aceProfilePath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\resources\\\\\\\\base\\\\\\\\profile\\\",\\\"hapMode\\\":\\\"false\\\",\\\"img2bin\\\":\\\"true\\\",\\\"projectProfilePath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\build-profile.json5\\\",\\\"watchMode\\\":\\\"true\\\",\\\"appResource\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\ResourceTable.txt\\\",\\\"aceBuildJson\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\loader\\\\\\\\default\\\\\\\\loader.json\\\",\\\"aceModuleRoot\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\src\\\\\\\\main\\\\\\\\ets\\\",\\\"aceSoPath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\cache\\\\\\\\nativeDependencies.txt\\\",\\\"cachePath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\cache\\\\\\\\.default\\\",\\\"aceModuleBuild\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\assets\\\\\\\\default\\\\\\\\ets\\\",\\\"aceModuleJsonPath\\\":\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\module.json\\\",\\\"stageRouterConfig\\\":{\\\"paths\\\":[\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\module.json\\\",\\\"D:\\\\\\\\vue\\\\\\\\daxiangmuwallet\\\\\\\\wallet\\\\\\\\entry\\\\\\\\.preview\\\\\\\\default\\\\\\\\intermediates\\\\\\\\res\\\\\\\\default\\\\\\\\resources\\\\\\\\base\\\\\\\\profile\\\\\\\\main_pages.json\\\"],\\\"contents\\\":[\\\"{\\\\\\\"module\\\\\\\":{\\\\\\\"pages\\\\\\\":\\\\\\\"$profile:main_pages\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"entry\\\\\\\"}}\\\",\\\"{\\\\\\\"src\\\\\\\":[\\\\\\\"pages/Index\\\\\\\"]}\\\"]}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@PreviewUpdateAssets", "_key": ":wallet:entry:default@PreviewUpdateAssets", "_executionId": ":wallet:entry:default@PreviewUpdateAssets:1750824874056", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", {"isDirectory": false, "fileSnapShotHashValue": "3992d3152189274885952e825ab9c2c0"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json", {"isDirectory": false, "fileSnapShotHashValue": "6772a33ce762b7f69b19b5aeb0de3f35"}]]}}, ":wallet:entry:default@PreviewArkTS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ark.tsImportSendable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"caseSensitive<PERSON><PERSON><PERSON>\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"autoLazyImport\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"allowEmptyBundleName\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkTsWdiget\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"OBFUSCATION_ENABLE\",\"_value\":\"undefined\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceEnable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceExcludes\",\"_value\":[],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"noExternalImportByPath\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "wallet", "_moduleName": "entry", "_taskName": "default@PreviewArkTS", "_key": ":wallet:entry:default@PreviewArkTS", "_executionId": ":wallet:entry:default@PreviewArkTS:1750824874088", "_inputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default", {"isDirectory": true, "fileSnapShotHashValue": "cf296dde1e3fbb6ab1cd7ab1aeb523a5"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", {"fileSnapShotHashValue": "f0e411cae7d3449d77b242572b25ff14"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json", {"fileSnapShotHashValue": "ad8d0f963bc11d757ace2191175ad195"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", {"isDirectory": true, "fileSnapShotHashValue": "2c69eb4cc459b89fe14185cc907b22db"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\ets", {"isDirectory": true, "fileSnapShotHashValue": "7afb1b423bb0daa4ec7aa3139e8838ce"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json", {"fileSnapShotHashValue": "7385b645a8e7906f1ce5a33b79fbabe5"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets", {"fileSnapShotHashValue": "a97543b30f7e182ee8d0bd92f654a1ea"}], ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5", {"isDirectory": false, "fileSnapShotHashValue": "c56dcb1db162f96dba73188827c40cc9"}]]}, "_outputFiles": {"dataType": "Map", "value": [["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}}