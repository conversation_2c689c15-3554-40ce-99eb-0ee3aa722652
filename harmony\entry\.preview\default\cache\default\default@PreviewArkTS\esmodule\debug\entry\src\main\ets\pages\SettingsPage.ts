if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface SettingsPage_Params {
    userInfo?: LocalUserInfo | null;
    showLogoutDialog?: boolean;
    showUserSwitchDialog?: boolean;
    showSecuritySettings?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import type { LocalUserInfo } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
class SettingsPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__userInfo = new ObservedPropertyObjectPU(null, this, "userInfo");
        this.__showLogoutDialog = new ObservedPropertySimplePU(false, this, "showLogoutDialog");
        this.__showUserSwitchDialog = new ObservedPropertySimplePU(false, this, "showUserSwitchDialog");
        this.__showSecuritySettings = new ObservedPropertySimplePU(false, this, "showSecuritySettings");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: SettingsPage_Params) {
        if (params.userInfo !== undefined) {
            this.userInfo = params.userInfo;
        }
        if (params.showLogoutDialog !== undefined) {
            this.showLogoutDialog = params.showLogoutDialog;
        }
        if (params.showUserSwitchDialog !== undefined) {
            this.showUserSwitchDialog = params.showUserSwitchDialog;
        }
        if (params.showSecuritySettings !== undefined) {
            this.showSecuritySettings = params.showSecuritySettings;
        }
    }
    updateStateVars(params: SettingsPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__userInfo.purgeDependencyOnElmtId(rmElmtId);
        this.__showLogoutDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__showUserSwitchDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__showSecuritySettings.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__userInfo.aboutToBeDeleted();
        this.__showLogoutDialog.aboutToBeDeleted();
        this.__showUserSwitchDialog.aboutToBeDeleted();
        this.__showSecuritySettings.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __userInfo: ObservedPropertyObjectPU<LocalUserInfo | null>;
    get userInfo() {
        return this.__userInfo.get();
    }
    set userInfo(newValue: LocalUserInfo | null) {
        this.__userInfo.set(newValue);
    }
    private __showLogoutDialog: ObservedPropertySimplePU<boolean>;
    get showLogoutDialog() {
        return this.__showLogoutDialog.get();
    }
    set showLogoutDialog(newValue: boolean) {
        this.__showLogoutDialog.set(newValue);
    }
    private __showUserSwitchDialog: ObservedPropertySimplePU<boolean>;
    get showUserSwitchDialog() {
        return this.__showUserSwitchDialog.get();
    }
    set showUserSwitchDialog(newValue: boolean) {
        this.__showUserSwitchDialog.set(newValue);
    }
    private __showSecuritySettings: ObservedPropertySimplePU<boolean>;
    get showSecuritySettings() {
        return this.__showSecuritySettings.get();
    }
    set showSecuritySettings(newValue: boolean) {
        this.__showSecuritySettings.set(newValue);
    }
    aboutToAppear() {
        this.loadUserInfo();
    }
    onPageShow() {
        // 页面显示时重新加载用户信息，确保数据是最新的
        console.log('设置页面显示，重新加载用户信息');
        this.loadUserInfo();
    }
    async loadUserInfo(): Promise<void> {
        try {
            // 直接从本地存储获取用户信息
            const cachedUserInfo = await storageManager.getUserInfo();
            if (cachedUserInfo) {
                this.userInfo = cachedUserInfo;
                console.log('设置页面加载用户信息，支付限额:', this.userInfo.payLimit);
            }
        }
        catch (error) {
            console.error('获取用户信息失败:', error);
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/SettingsPage.ets(40:5)", "entry");
            Stack.width('100%');
            Stack.height('100%');
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 主要内容
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(42:7)", "entry");
            // 主要内容
            Column.width('100%');
            // 主要内容
            Column.height('100%');
            // 主要内容
            Column.backgroundColor('#F5F5F5');
            // 主要内容
            Column.bindSheet({ value: this.showSecuritySettings, changeEvent: newValue => { this.showSecuritySettings = newValue; } }, { builder: () => {
                    this.SecuritySettingsDialog.call(this);
                } }, {
                height: 500,
                showClose: true,
                dragBar: true,
                onDisappear: () => {
                    this.showSecuritySettings = false;
                }
            });
            // 主要内容
            Column.bindSheet({ value: this.showUserSwitchDialog, changeEvent: newValue => { this.showUserSwitchDialog = newValue; } }, { builder: () => {
                    this.UserSwitchDialog.call(this);
                } }, {
                height: 200,
                showClose: true,
                dragBar: true,
                onDisappear: () => {
                    this.showUserSwitchDialog = false;
                }
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(44:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('返回');
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(45:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(53:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(59:9)", "entry");
            Text.width(60);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/SettingsPage.ets(67:7)", "entry");
            Scroll.layoutWeight(1);
            Scroll.backgroundColor('#F5F5F5');
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(68:9)", "entry");
            Column.padding({ left: 16, right: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 个人信息
            if (this.userInfo) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(71:13)", "entry");
                        Column.width('100%');
                        Column.padding(20);
                        Column.margin({ top: 16 });
                        Column.borderRadius(12);
                        Column.backgroundColor('#FFFFFF');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('个人信息');
                        Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(72:15)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#333333');
                        Text.fontWeight(FontWeight.Medium);
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ bottom: 16 });
                    }, Text);
                    Text.pop();
                    this.InfoItem.bind(this)('真实姓名', this.maskRealName(this.userInfo.realName));
                    this.InfoItem.bind(this)('手机号', this.maskPhone(this.userInfo.phone));
                    this.InfoItem.bind(this)('身份证号', this.maskIdCard(this.userInfo.idCard));
                    this.InfoItem.bind(this)('注册时间', this.formatDate(this.userInfo.createTime));
                    Column.pop();
                });
            }
            // 安全设置
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 安全设置
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(92:11)", "entry");
            // 安全设置
            Column.width('100%');
            // 安全设置
            Column.padding(20);
            // 安全设置
            Column.margin({ top: 16 });
            // 安全设置
            Column.borderRadius(12);
            // 安全设置
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('安全设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(93:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.SettingItem.bind(this)('修改支付密码', '保护您的资金安全', () => {
            router.pushUrl({ url: 'pages/ChangePayPasswordPage' });
        });
        this.SettingItem.bind(this)('支付限额', '设置您的限额', () => {
            router.pushUrl({ url: 'pages/PayLimitSettingPage' });
        });
        this.SettingItem.bind(this)('银行卡管理', '管理您的银行卡', () => {
            router.pushUrl({ url: 'pages/BankCardPage' });
        });
        this.SettingItem.bind(this)('安全设置', '账户安全管理', () => {
            this.showSecuritySettings = true;
        });
        this.SettingItem.bind(this)('用户切换', '切换登录账户', () => {
            this.showUserSwitchDialog = true;
        });
        // 安全设置
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 退出登录
            Button.createWithLabel('退出登录');
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(127:11)", "entry");
            // 退出登录
            Button.width('100%');
            // 退出登录
            Button.height(48);
            // 退出登录
            Button.fontSize(16);
            // 退出登录
            Button.fontColor('#F44336');
            // 退出登录
            Button.backgroundColor('#FFFFFF');
            // 退出登录
            Button.borderRadius(8);
            // 退出登录
            Button.border({ width: 1, color: '#F44336' });
            // 退出登录
            Button.margin({ top: 24, bottom: 20 });
            // 退出登录
            Button.onClick(() => {
                this.showLogoutDialog = true;
            });
        }, Button);
        // 退出登录
        Button.pop();
        Column.pop();
        Scroll.pop();
        // 主要内容
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 退出登录确认弹窗
            if (this.showLogoutDialog) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(167:9)", "entry");
                        Column.width('100%');
                        Column.height('100%');
                        Column.position({ x: 0, y: 0 });
                        Column.zIndex(1000);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 遮罩层
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(169:11)", "entry");
                        // 遮罩层
                        Column.width('100%');
                        // 遮罩层
                        Column.height('100%');
                        // 遮罩层
                        Column.backgroundColor('#80000000');
                        // 遮罩层
                        Column.onClick(() => {
                            this.showLogoutDialog = false;
                        });
                    }, Column);
                    // 遮罩层
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 弹窗内容
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(178:11)", "entry");
                        // 弹窗内容
                        Column.width('280vp');
                        // 弹窗内容
                        Column.padding(24);
                        // 弹窗内容
                        Column.borderRadius(16);
                        // 弹窗内容
                        Column.backgroundColor('#FFFFFF');
                        // 弹窗内容
                        Column.shadow({
                            radius: 16,
                            color: '#40000000',
                            offsetX: 0,
                            offsetY: 8
                        });
                        // 弹窗内容
                        Column.position({ x: '50%', y: '50%' });
                        // 弹窗内容
                        Column.translate({ x: '-50%', y: '-50%' });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('确认退出');
                        Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(179:13)", "entry");
                        Text.fontSize(18);
                        Text.fontWeight(FontWeight.Medium);
                        Text.fontColor('#333333');
                        Text.margin({ bottom: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('确定要退出登录吗？');
                        Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(185:13)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#666666');
                        Text.textAlign(TextAlign.Center);
                        Text.margin({ bottom: 24 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(191:13)", "entry");
                        Row.width('100%');
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('取消');
                        Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(192:15)", "entry");
                        Button.fontSize(16);
                        Button.fontColor('#666666');
                        Button.backgroundColor('#F8F9FA');
                        Button.borderRadius(8);
                        Button.layoutWeight(1);
                        Button.height(44);
                        Button.onClick(() => {
                            this.showLogoutDialog = false;
                        });
                    }, Button);
                    Button.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('确认退出');
                        Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(203:15)", "entry");
                        Button.fontSize(16);
                        Button.fontColor('#FFFFFF');
                        Button.backgroundColor('#F44336');
                        Button.borderRadius(8);
                        Button.layoutWeight(1);
                        Button.height(44);
                        Button.margin({ left: 12 });
                        Button.onClick(() => {
                            this.showLogoutDialog = false;
                            this.handleLogout();
                        });
                    }, Button);
                    Button.pop();
                    Row.pop();
                    // 弹窗内容
                    Column.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Stack.pop();
    }
    InfoItem(label: string, value: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(243:5)", "entry");
            Row.width('100%');
            Row.height(44);
            Row.justifyContent(FlexAlign.SpaceBetween);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(244:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(value);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(249:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.End);
        }, Text);
        Text.pop();
        Row.pop();
    }
    SettingItem(title: string, subtitle: string, onClick: () => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(263:5)", "entry");
            Row.width('100%');
            Row.height(60);
            Row.padding({ top: 8, bottom: 8 });
            Row.justifyContent(FlexAlign.SpaceBetween);
            Row.alignItems(VerticalAlign.Center);
            Row.onClick(onClick);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(264:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(265:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(subtitle);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(270:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(279:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#CCCCCC');
        }, Text);
        Text.pop();
        Row.pop();
    }
    // 工具方法
    maskRealName(name: string): string {
        if (!name || name.length < 2)
            return name;
        if (name.length === 2) {
            return name.charAt(0) + '*';
        }
        else {
            return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1);
        }
    }
    maskPhone(phone: string): string {
        if (!phone || phone.length !== 11)
            return phone;
        return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    }
    maskIdCard(idCard: string): string {
        if (!idCard || idCard.length < 8)
            return idCard;
        if (idCard.length === 18) {
            return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
        }
        return idCard;
    }
    formatDate(dateTime: string): string {
        const date = new Date(dateTime);
        return date.toLocaleDateString('zh-CN');
    }
    async handleLogout(): Promise<void> {
        try {
            // 清除本地数据
            await storageManager.clearUserData();
            httpClient.clearAuthToken();
            promptAction.showToast({ message: '已退出登录' });
            // 跳转到登录页
            router.replaceUrl({ url: 'pages/LoginPage' });
        }
        catch (error) {
            console.error('退出登录失败:', error);
            promptAction.showToast({ message: '退出登录失败' });
        }
    }
    SecuritySettingsDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(352:5)", "entry");
            Column.width('100%');
            Column.padding(20);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('安全设置');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(353:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(358:7)", "entry");
            Column.width('100%');
        }, Column);
        this.SecurityItem.bind(this)('登录密码', '修改登录密码', () => {
            promptAction.showToast({ message: '跳转到修改登录密码页面' });
        });
        this.SecurityItem.bind(this)('支付密码', '修改支付密码', () => {
            router.pushUrl({ url: 'pages/ChangePayPasswordPage' });
        });
        this.SecurityItem.bind(this)('手机绑定', '更换绑定手机号', () => {
            promptAction.showToast({ message: '手机绑定功能开发中' });
        });
        this.SecurityItem.bind(this)('风险提醒', '账户安全提醒设置', () => {
            promptAction.showToast({ message: '风险提醒设置功能开发中' });
        });
        this.SecurityItem.bind(this)('设备管理', '管理登录设备', () => {
            promptAction.showToast({ message: '设备管理功能开发中' });
        });
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('关闭');
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(381:7)", "entry");
            Button.width('100%');
            Button.height(48);
            Button.fontSize(16);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#1976D2');
            Button.borderRadius(8);
            Button.margin({ top: 20 });
            Button.onClick(() => {
                this.showSecuritySettings = false;
            });
        }, Button);
        Button.pop();
        Column.pop();
    }
    SecurityItem(title: string, subtitle: string, onClick: () => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(399:5)", "entry");
            Row.width('100%');
            Row.padding({ top: 12, bottom: 12 });
            Row.onClick(onClick);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(400:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(title);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(401:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(subtitle);
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(407:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('>');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(416:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#999999');
        }, Text);
        Text.pop();
        Row.pop();
    }
    UserSwitchDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/SettingsPage.ets(427:5)", "entry");
            Column.width('100%');
            Column.padding(20);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('用户切换');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(428:7)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确定要切换用户吗？');
            Text.debugLine("entry/src/main/ets/pages/SettingsPage.ets(433:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#666666');
            Text.margin({ bottom: 20 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/SettingsPage.ets(438:7)", "entry");
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(439:9)", "entry");
            Button.fontSize(14);
            Button.fontColor('#666666');
            Button.backgroundColor('#F5F5F5');
            Button.borderRadius(8);
            Button.layoutWeight(1);
            Button.margin({ right: 8 });
            Button.onClick(() => {
                this.showUserSwitchDialog = false;
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('确定');
            Button.debugLine("entry/src/main/ets/pages/SettingsPage.ets(450:9)", "entry");
            Button.fontSize(14);
            Button.fontColor('#FFFFFF');
            Button.backgroundColor('#1976D2');
            Button.borderRadius(8);
            Button.layoutWeight(1);
            Button.margin({ left: 8 });
            Button.onClick(() => {
                this.switchUser();
            });
        }, Button);
        Button.pop();
        Row.pop();
        Column.pop();
    }
    async switchUser() {
        try {
            // 清除当前用户数据
            await storageManager.clearUserData();
            httpClient.clearAuthToken();
            this.showUserSwitchDialog = false;
            promptAction.showToast({ message: '已切换用户，请重新登录' });
            // 跳转到登录页面
            router.replaceUrl({ url: 'pages/LoginPage' });
        }
        catch (error) {
            console.error('切换用户失败:', error);
            promptAction.showToast({ message: '切换用户失败' });
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "SettingsPage";
    }
}
registerNamedRoute(() => new SettingsPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/SettingsPage", pageFullPath: "entry/src/main/ets/pages/SettingsPage", integratedHsp: "false", moduleType: "followWithHap" });
