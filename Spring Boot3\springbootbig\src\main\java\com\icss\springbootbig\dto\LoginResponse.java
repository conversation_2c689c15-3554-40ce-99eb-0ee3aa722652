package com.icss.springbootbig.dto;

import lombok.Data;
import java.util.Date;

/**
 * 登录响应DTO
 * 只包含数据库中实际存在的字段
 */
@Data
public class LoginResponse {
    private Integer userId;
    private String phone;
    private String password;
    // payPassword字段已移除，支付密码现在存储在银行卡表中
    // private String payPassword;
    private Integer status;
    private Date lastLogin;
    private Date createdAt;
    private Date updatedAt;

    /**
     * 从User实体转换为LoginResponse
     */
    public static LoginResponse fromUser(com.icss.springbootbig.entity.User user) {
        LoginResponse response = new LoginResponse();
        response.setUserId(user.getUserId());
        response.setPhone(user.getPhone());
        response.setPassword(user.getPassword());
        // 支付密码现在存储在银行卡表中，不再从用户表获取
        // response.setPayPassword(user.getPayPassword());
        response.setStatus(user.getStatus());
        response.setLastLogin(user.getLastLogin());
        response.setCreatedAt(user.getCreatedAt());
        response.setUpdatedAt(user.getUpdatedAt());
        return response;
    }
}
