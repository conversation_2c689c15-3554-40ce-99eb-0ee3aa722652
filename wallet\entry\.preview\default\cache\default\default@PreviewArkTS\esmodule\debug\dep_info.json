{"resolveConflictMode": true, "depName2RootPath": {"@ohos/hypium": "D:\\vue\\daxiangmuwallet\\wallet\\oh_modules\\.ohpm\\@ohos+hypium@1.0.21\\oh_modules\\@ohos\\hypium", "@ohos/hamock": "D:\\vue\\daxiangmuwallet\\wallet\\oh_modules\\.ohpm\\@ohos+hamock@1.0.0\\oh_modules\\@ohos\\hamock"}, "depName2DepInfo": {"@ohos/hypium": {"pkgRootPath": "D:\\vue\\daxiangmuwallet\\wallet\\oh_modules\\.ohpm\\@ohos+hypium@1.0.21\\oh_modules\\@ohos\\hypium", "pkgName": "@ohos/hypium", "pkgVersion": "1.0.21"}, "@ohos/hamock": {"pkgRootPath": "D:\\vue\\daxiangmuwallet\\wallet\\oh_modules\\.ohpm\\@ohos+hamock@1.0.0\\oh_modules\\@ohos\\hamock", "pkgName": "@ohos/hamock", "pkgVersion": "1.0.0"}}}