# 电子钱包系统 (E-Wallet System)

一个完整的电子钱包系统，包含Vue3前端、Spring Boot3后端和MySQL数据库，实现了真实银行系统的核心功能。

## 🚀 系统特性

### 核心功能模块

#### 1. 用户账户模块 👤
- **手机号注册与登录**：支持验证码和密码双重验证
- **账户信息管理**：个人资料修改、头像上传
- **账户状态控制**：冻结/解冻、注销功能

#### 2. 钱包资产模块 💰
- **余额查询**：实时显示钱包余额和银行卡余额
- **充值功能**：银行卡充值到钱包
- **提现功能**：钱包余额提现到银行卡，支持银行转银行
- **限额设置**：单笔限额、每日限额、月限额配置

#### 3. 银行卡管理模块 💳
- **银行卡列表查询**：显示所有绑定卡和默认卡
- **银行卡详情查看**：卡号脱敏展示，完整信息查看
- **银行卡绑定**：支持借记卡和信用卡绑定
- **银行卡解除绑定**：支持保留1张默认卡的安全机制

#### 4. 交易处理模块 📊
- **转账功能**：钱包间转账，输入对方手机号
- **收款功能**：生成收款码，扫码收款
- **交易记录查询**：按类型、时间、状态筛选
- **交易详情查看**：流水号、金额、对方信息、交易路径

#### 5. 支付功能模块 💸
- **钱包支付**：直接扣除钱包余额
- **银行卡支付**：调用银行接口扣款
- **扫码支付**：扫描商户二维码付款
- **NFC支付**：贴近POS机完成支付

#### 6. 安全设置模块 🔒
- **支付密码管理**：设置、修改、验证6位数字密码
- **登录密码修改**：定期更新安全密码
- **安全设备管理**：登录设备记录与授权
- **风险提醒**：异常交易实时通知
- **指纹支付**：生物识别支付验证

## 🛠️ 技术栈

### 前端技术
- **Vue 3**：渐进式JavaScript框架
- **Element Plus**：Vue 3组件库
- **Vue Router**：路由管理
- **Axios**：HTTP客户端
- **Vite**：构建工具

### 后端技术
- **Spring Boot 3**：Java企业级框架
- **MyBatis Plus**：持久层框架
- **MySQL**：关系型数据库
- **JWT**：身份验证
- **Spring Security**：安全框架

### 数据库设计
- **users**：用户基本信息表
- **accounts**：钱包账户表
- **bank_cards**：银行卡信息表
- **merchants**：商户信息表
- **transactions**：交易记录表

## 📦 安装与运行

### 前端启动
```bash
cd VUE3/e-wallet
npm install
npm run dev
```

### 后端启动
```bash
cd "Spring Boot3/springbootbig"
mvn spring-boot:run
```

### 数据库配置
1. 创建MySQL数据库：`ewallet_db`
2. 导入数据库脚本：`database/ewallet_db.sql`
3. 配置数据库连接信息

## 🔧 配置说明

### 前端配置
- **API地址**：`http://localhost:8096`
- **路由模式**：History模式
- **状态管理**：Composition API

### 后端配置
- **服务端口**：8096
- **数据库**：MySQL 8.0+
- **JWT密钥**：可在配置文件中修改

## 🧪 测试功能

访问 `/api-test` 页面可以测试：
- 前后端连接状态
- 各模块API接口
- 数据库连接状态
- 用户认证状态

## 🔐 安全特性

- **双重密码体系**：登录密码 + 支付密码
- **交易验证**：支付密码验证所有资金操作
- **限额控制**：多层级限额保护
- **设备管理**：登录设备授权管理
- **风险监控**：异常交易检测和提醒

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Compile and Minify for Production

```sh
npm run build
```
