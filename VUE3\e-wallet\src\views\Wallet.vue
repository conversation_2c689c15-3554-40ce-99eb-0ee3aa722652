<template>
  <div class="wallet-container">
    <!-- 钱包概览 -->
    <el-row :gutter="20" class="wallet-overview">
      <el-col :span="6">
        <el-card class="overview-card balance-overview">
          <div class="overview-content">
            <div class="overview-icon">💰</div>
            <div class="overview-info">
              <h3>钱包余额</h3>
              <p class="overview-amount">¥{{ walletBalance.toLocaleString() }}</p>
              <p class="overview-desc">可用余额</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="overview-card bank-overview">
          <div class="overview-content">
            <div class="overview-icon">🏦</div>
            <div class="overview-info">
              <h3>银行余额</h3>
              <p class="overview-amount">¥{{ totalBankBalance.toLocaleString() }}</p>
              <p class="overview-desc">{{ userBankCards.length }} 张银行卡</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="overview-card limit-overview">
          <div class="overview-content">
            <div class="overview-icon">📊</div>
            <div class="overview-info">
              <h3>今日限额</h3>
              <p class="overview-amount">¥{{ dailyLimit.toLocaleString() }}</p>
              <p class="overview-desc">已使用: ¥{{ dailyUsed.toLocaleString() }}</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="overview-card security-overview">
          <div class="overview-content">
            <div class="overview-icon">🔒</div>
            <div class="overview-info">
              <h3>安全等级</h3>
              <p class="overview-level">{{ securityLevel }}</p>
              <p class="overview-desc">{{ securityStatus }}</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 钱包余额卡片 -->
    <el-card class="balance-card" shadow="hover">
      <div class="balance-header">
        <div class="balance-info">
          <h2>钱包余额</h2>
          <div class="balance-amount">¥{{ walletBalance.toLocaleString() }}</div>
          <p class="balance-desc">可用余额</p>
        </div>
        <div class="balance-icon">💰</div>
      </div>

      <div class="balance-actions">
        <el-button type="primary" @click="showRechargeDialog = true">
          充值
        </el-button>
        <el-button type="success" @click="showWithdrawDialog = true">
          提现
        </el-button>
        <el-button type="warning" @click="showTransferDialog = true">
          转账
        </el-button>
        <el-button type="info" @click="showReceiveDialog = true">
          收钱
        </el-button>
      </div>
    </el-card>



    <!-- 最近交易 -->
    <el-card class="recent-transactions" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="card-title">最近交易</span>
          <el-button type="text" @click="$router.push('/transaction')">查看全部</el-button>
        </div>
      </template>

      <div class="transaction-list">
        <div
          v-for="transaction in recentTransactions"
          :key="transaction.id"
          class="transaction-item"
        >
          <div class="transaction-icon" :class="transaction.type">
            {{ getTransactionIcon(transaction.type) }}
          </div>
          <div class="transaction-details">
            <div class="transaction-title">{{ transaction.title }}</div>
            <div class="transaction-time">{{ transaction.time }}</div>
          </div>
          <div class="transaction-amount" :class="transaction.type">
            {{ transaction.type === 'income' ? '+' : '-' }}¥{{ transaction.amount.toLocaleString() }}
          </div>
        </div>
      </div>
    </el-card>

    <!-- 充值对话框 -->
    <el-dialog v-model="showRechargeDialog" title="钱包充值" width="450px">
      <el-form :model="rechargeForm" :rules="rechargeRules" ref="rechargeFormRef" label-width="100px">
        <el-form-item label="充值金额" prop="amount">
          <el-input v-model="rechargeForm.amount" placeholder="请输入充值金额" type="number">
            <template #prepend>¥</template>
          </el-input>
        </el-form-item>
        <el-form-item label="选择银行卡" prop="bankCardId">
          <el-select
            v-model="rechargeForm.bankCardId"
            placeholder="请选择充值银行卡"
            style="width: 100%"
            :loading="bankCardsLoading"
            @focus="loadUserBankCards"
          >
            <template #empty>
              <div class="empty-bank-cards">
                <el-icon><CreditCard /></el-icon>
                <p>暂无可用银行卡</p>
                <el-button type="primary" size="small" @click="$router.push('/bankcard')">
                  去添加银行卡
                </el-button>
              </div>
            </template>
            <el-option
              v-for="card in availableBankCards"
              :key="card.cardId"
              :label="`${card.bankName} (****${card.cardNumber.slice(-4)})`"
              :value="card.cardId"
              :disabled="card.status !== 1"
            >
              <div class="bank-card-option">
                <div class="card-info">
                  <div class="card-main-info">
                    <span class="bank-name">{{ card.bankName }}</span>
                    <el-tag
                      v-if="card.isDefault === 1"
                      type="success"
                      size="small"
                      style="margin-left: 8px;"
                    >
                      默认
                    </el-tag>
                  </div>
                  <span class="card-number">****{{ card.cardNumber.slice(-4) }}</span>
                  <div class="card-details">
                    <span class="card-balance">余额: ¥{{ (card.balance || 0).toLocaleString() }}</span>
                    <span class="card-holder">{{ card.cardHolder }}</span>
                  </div>
                </div>
                <div class="card-type">
                  <el-tag :type="card.cardType === 1 ? 'success' : 'warning'" size="small">
                    {{ card.cardType === 1 ? '储蓄卡' : '信用卡' }}
                  </el-tag>
                  <el-tag
                    v-if="card.status !== 1"
                    type="danger"
                    size="small"
                    style="margin-top: 4px;"
                  >
                    {{ card.status === 0 ? '未连接' : '已冻结' }}
                  </el-tag>
                </div>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">
            <el-icon><InfoFilled /></el-icon>
            只显示您名下的银行卡，共 {{ userBankCards.length }} 张，可用 {{ availableBankCards.length }} 张
          </div>
        </el-form-item>
        <el-form-item label="支付密码" prop="payPassword">
          <el-input
            v-model="rechargeForm.payPassword"
            type="password"
            placeholder="请输入6位支付密码"
            maxlength="6"
            show-password
          ></el-input>
        </el-form-item>
        <el-form-item label="充值说明">
          <el-input v-model="rechargeForm.remark" placeholder="请输入充值说明（可选）"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showRechargeDialog = false">取消</el-button>
        <el-button type="primary" @click="handleRecharge" :loading="rechargeLoading">确认充值</el-button>
      </template>
    </el-dialog>

    <!-- 提现对话框 -->
    <el-dialog v-model="showWithdrawDialog" title="钱包提现" width="500px">
      <el-form :model="withdrawForm" :rules="withdrawRules" ref="withdrawFormRef" label-width="100px">
        <el-form-item label="提现金额" prop="amount">
          <el-input v-model="withdrawForm.amount" placeholder="请输入提现金额" type="number">
            <template #prepend>¥</template>
          </el-input>
          <div class="form-tip">当前余额：¥{{ walletBalance.toLocaleString() }}</div>
        </el-form-item>
        <el-form-item label="提现到" prop="bankCardId">
          <el-select
            v-model="withdrawForm.bankCardId"
            placeholder="请选择银行卡"
            style="width: 100%"
            :loading="bankCardsLoading"
            @focus="loadUserBankCards"
          >
            <template #empty>
              <div class="empty-bank-cards">
                <el-icon><CreditCard /></el-icon>
                <p>暂无可用银行卡</p>
                <el-button type="primary" size="small" @click="$router.push('/bankcard')">
                  去添加银行卡
                </el-button>
              </div>
            </template>
            <el-option
              v-for="card in availableBankCards"
              :key="card.cardId"
              :label="`${card.bankName} (****${card.cardNumber.slice(-4)})`"
              :value="card.cardId"
              :disabled="card.status !== 1"
            >
              <div class="bank-card-option">
                <div class="card-info">
                  <div class="card-main-info">
                    <span class="bank-name">{{ card.bankName }}</span>
                    <el-tag
                      v-if="card.isDefault === 1"
                      type="success"
                      size="small"
                      style="margin-left: 8px;"
                    >
                      默认
                    </el-tag>
                  </div>
                  <span class="card-number">****{{ card.cardNumber.slice(-4) }}</span>
                  <div class="card-details">
                    <span class="card-holder">{{ card.cardHolder }}</span>
                  </div>
                </div>
                <div class="card-type">
                  <el-tag :type="card.cardType === 1 ? 'success' : 'warning'" size="small">
                    {{ card.cardType === 1 ? '储蓄卡' : '信用卡' }}
                  </el-tag>
                  <el-tag
                    v-if="card.status !== 1"
                    type="danger"
                    size="small"
                    style="margin-top: 4px;"
                  >
                    {{ card.status === 0 ? '未连接' : '已冻结' }}
                  </el-tag>
                </div>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">
            <el-icon><InfoFilled /></el-icon>
            只显示您名下的银行卡，共 {{ userBankCards.length }} 张，可用 {{ availableBankCards.length }} 张
          </div>
        </el-form-item>
        <el-form-item label="支付密码" prop="payPassword">
          <el-input
            v-model="withdrawForm.payPassword"
            type="password"
            placeholder="请输入6位支付密码"
            maxlength="6"
            show-password
          ></el-input>
        </el-form-item>
        <el-form-item label="提现说明">
          <el-input v-model="withdrawForm.remark" placeholder="请输入提现说明（可选）"></el-input>
        </el-form-item>
        <el-form-item>
          <el-alert
            title="提现说明"
            type="info"
            description="提现将在1-3个工作日内到账，请确保银行卡信息正确"
            show-icon
            :closable="false"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showWithdrawDialog = false">取消</el-button>
        <el-button type="primary" @click="handleWithdraw" :loading="withdrawLoading">确认提现</el-button>
      </template>
    </el-dialog>

    <!-- 转账对话框 -->
    <el-dialog v-model="showTransferDialog" title="转账" width="500px">
      <el-form :model="transferForm" :rules="transferRules" ref="transferFormRef" label-width="100px">
        <el-form-item label="转账方式" prop="transferType">
          <el-radio-group v-model="transferForm.transferType" @change="onTransferTypeChange">
            <el-radio-button label="wallet">钱包转账</el-radio-button>
            <el-radio-button label="bank">银行卡转账</el-radio-button>
          </el-radio-group>
          <div class="form-tip">
            <el-icon><InfoFilled /></el-icon>
            {{ transferForm.transferType === 'wallet' ? '钱包转账会影响钱包余额' : '银行卡转账不影响钱包余额' }}
          </div>
        </el-form-item>

        <!-- 银行卡转账时显示发送方银行卡选择 -->
        <el-form-item
          v-if="transferForm.transferType === 'bank'"
          label="转出银行卡"
          prop="fromCardId"
        >
          <el-select
            v-model="transferForm.fromCardId"
            placeholder="请选择转出银行卡"
            style="width: 100%"
            :loading="bankCardsLoading"
            @focus="loadUserBankCards"
          >
            <template #empty>
              <div class="empty-bank-cards">
                <el-icon><CreditCard /></el-icon>
                <p>暂无可用银行卡</p>
                <el-button type="primary" size="small" @click="$router.push('/bankcard')">
                  去添加银行卡
                </el-button>
              </div>
            </template>
            <el-option
              v-for="card in availableBankCards"
              :key="card.cardId"
              :label="`${card.bankName} (****${card.cardNumber.slice(-4)})`"
              :value="card.cardId"
              :disabled="card.status !== 1"
            >
              <div class="bank-card-option">
                <div class="card-info">
                  <div class="card-main-info">
                    <span class="bank-name">{{ card.bankName }}</span>
                    <el-tag
                      v-if="card.isDefault === 1"
                      type="success"
                      size="small"
                      style="margin-left: 8px;"
                    >
                      默认
                    </el-tag>
                  </div>
                  <span class="card-number">****{{ card.cardNumber.slice(-4) }}</span>
                  <div class="card-details">
                    <span class="card-holder">{{ card.cardHolder }}</span>
                  </div>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 钱包转账时显示收款银行账号输入 -->
        <el-form-item
          v-if="transferForm.transferType === 'wallet'"
          label="收款人"
          prop="recipient"
        >
          <el-input v-model="transferForm.recipient" placeholder="请输入收款人银行账号">
            <template #prepend>
              <el-icon><CreditCard /></el-icon>
            </template>
          </el-input>
          <div class="form-tip">
            <el-icon><InfoFilled /></el-icon>
            钱包转账收款人为银行账号
          </div>
        </el-form-item>

        <!-- 银行卡转账时显示收款人手机号输入 -->
        <el-form-item
          v-if="transferForm.transferType === 'bank'"
          label="收款人"
          prop="recipient"
        >
          <el-input v-model="transferForm.recipient" placeholder="请输入收款人手机号">
            <template #prepend>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
          <div class="form-tip">
            <el-icon><InfoFilled /></el-icon>
            银行卡转账收款人为手机号
          </div>
        </el-form-item>

        <!-- 银行卡转账时显示接收方银行卡选择 -->
        <el-form-item
          v-if="transferForm.transferType === 'bank'"
          label="收款银行卡"
          prop="toCardId"
        >
          <el-select
            v-model="transferForm.toCardId"
            placeholder="请选择收款银行卡"
            style="width: 100%"
            :loading="allCardsLoading"
            @focus="loadAllBankCards"
            filterable
            remote
            :remote-method="searchBankCards"
          >
            <template #empty>
              <div class="empty-bank-cards">
                <el-icon><CreditCard /></el-icon>
                <p>暂无可用银行卡</p>
                <el-button type="primary" size="small" @click="$router.push('/admin/bank-management')">
                  去银行管理添加
                </el-button>
              </div>
            </template>
            <el-option
              v-for="card in allBankCards"
              :key="card.cardId"
              :label="`${card.bankName} (****${card.cardNumber.slice(-4)}) - ${card.cardHolder}`"
              :value="card.cardId"
            >
              <div class="bank-card-option">
                <div class="card-info">
                  <div class="card-main-info">
                    <span class="bank-name">{{ card.bankName }}</span>
                    <el-tag v-if="card.cardType === 1" type="success" size="small">储蓄卡</el-tag>
                    <el-tag v-else type="warning" size="small">信用卡</el-tag>
                  </div>
                  <span class="card-number">****{{ card.cardNumber.slice(-4) }}</span>
                  <div class="card-details">
                    <span class="card-holder">{{ card.cardHolder }}</span>
                  </div>
                </div>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">
            <el-icon><InfoFilled /></el-icon>
            银行卡可以自己根据数据库添加
          </div>
        </el-form-item>

        <el-form-item label="转账金额" prop="amount">
          <el-input v-model="transferForm.amount" placeholder="请输入转账金额" type="number">
            <template #prepend>¥</template>
          </el-input>
          <div class="form-tip">
            {{ transferForm.transferType === 'wallet' ? `当前钱包余额：¥${walletBalance.toLocaleString()}` : '银行卡转账不影响钱包余额' }}
          </div>
        </el-form-item>
        <el-form-item label="支付密码" prop="payPassword">
          <el-input
            v-model="transferForm.payPassword"
            type="password"
            placeholder="请输入6位支付密码"
            maxlength="6"
            show-password
          ></el-input>
        </el-form-item>
        <el-form-item label="转账备注">
          <el-input v-model="transferForm.remark" placeholder="请输入转账备注（可选）"></el-input>
        </el-form-item>
        <el-form-item>
          <el-alert
            :title="transferForm.transferType === 'wallet' ? '钱包转账提醒' : '银行卡转账提醒'"
            type="warning"
            :description="transferForm.transferType === 'wallet' ? '钱包转账会从您的钱包余额中扣除，请确认收款人信息' : '银行卡转账是银行间直接转账，不影响钱包余额，请确认收款人银行卡信息'"
            show-icon
            :closable="false"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showTransferDialog = false">取消</el-button>
        <el-button type="primary" @click="handleTransfer" :loading="transferLoading">
          {{ transferForm.transferType === 'wallet' ? '确认钱包转账' : '确认银行卡转账' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 收钱对话框 -->
    <el-dialog v-model="showReceiveDialog" title="收钱码" width="400px">
      <div class="receive-money-content">
        <div class="qr-code">
          <div class="qr-placeholder">
            <div class="qr-icon">📱</div>
            <p>收款二维码</p>
          </div>
        </div>
        <el-form :model="receiveForm" label-width="80px">
          <el-form-item label="收款金额">
            <el-input v-model="receiveForm.amount" placeholder="请输入收款金额（可选）">
              <template #prepend>¥</template>
            </el-input>
          </el-form-item>
          <el-form-item label="收款说明">
            <el-input v-model="receiveForm.description" placeholder="请输入收款说明（可选）"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="showReceiveDialog = false">关闭</el-button>
        <el-button type="primary" @click="generateQRCode">生成收款码</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { CreditCard, InfoFilled } from '@element-plus/icons-vue';
import { bankCardApi } from '@/api/bankCard';
import { walletApi } from '@/api/wallet';
import { transactionApi } from '@/api/transaction';
import { getCurrentUserId } from '@/stores/user';

const router = useRouter();

// 数据
const walletBalance = ref(0);
const userBankCards = ref([]);
const bankCardsLoading = ref(false);
const currentUserId = getCurrentUserId();

// 钱包限额信息
const dailyLimit = ref(50000);
const dailyUsed = ref(2350);
const monthlyLimit = ref(200000);
const monthlyUsed = ref(15680);

// 安全信息
const securityLevel = ref('高');
const securityStatus = ref('账户安全');

// 计算属性
const totalBankBalance = computed(() => {
  return userBankCards.value.reduce((total, card) => {
    return total + (card.balance || 0);
  }, 0);
});

const remainingDailyLimit = computed(() => {
  return dailyLimit.value - dailyUsed.value;
});

const remainingMonthlyLimit = computed(() => {
  return monthlyLimit.value - monthlyUsed.value;
});

// 可用银行卡（只显示状态正常的银行卡）
const availableBankCards = computed(() => {
  return userBankCards.value.filter(card => card.status === 1);
});

// 对话框显示状态
const showRechargeDialog = ref(false);
const showWithdrawDialog = ref(false);
const showTransferDialog = ref(false);
const showReceiveDialog = ref(false);

// 加载状态
const rechargeLoading = ref(false);
const withdrawLoading = ref(false);
const transferLoading = ref(false);

// 表单数据
const rechargeForm = ref({
  amount: '',
  bankCardId: '',
  payPassword: '',
  remark: ''
});

// 表单验证规则
const rechargeRules = {
  amount: [
    { required: true, message: '请输入充值金额', trigger: 'blur' },
    { pattern: /^[1-9]\d*(\.\d{1,2})?$/, message: '请输入有效的金额', trigger: 'blur' }
  ],
  bankCardId: [
    { required: true, message: '请选择银行卡', trigger: 'change' }
  ],
  payPassword: [
    { required: true, message: '请输入支付密码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '支付密码必须是6位数字', trigger: 'blur' }
  ]
};

const rechargeFormRef = ref(null);
const withdrawFormRef = ref(null);
const transferFormRef = ref(null);

const withdrawForm = ref({
  amount: '',
  bankCardId: '',
  payPassword: '',
  remark: ''
});

// 提现表单验证规则
const withdrawRules = {
  amount: [
    { required: true, message: '请输入提现金额', trigger: 'blur' },
    { pattern: /^[1-9]\d*(\.\d{1,2})?$/, message: '请输入有效的金额', trigger: 'blur' }
  ],
  bankCardId: [
    { required: true, message: '请选择银行卡', trigger: 'change' }
  ],
  payPassword: [
    { required: true, message: '请输入支付密码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '支付密码必须是6位数字', trigger: 'blur' }
  ]
};

const transferForm = ref({
  transferType: 'wallet', // 默认钱包转账
  fromCardId: '', // 转出银行卡ID（银行卡转账时使用）
  toCardId: '', // 收款银行卡ID（银行卡转账时使用）
  recipient: '',
  amount: '',
  payPassword: '',
  remark: ''
});

// 收款人银行卡列表
const recipientBankCards = ref([]);
const recipientCardsLoading = ref(false);

// 所有银行卡列表（用于银行转账选择）
const allBankCards = ref([]);
const allCardsLoading = ref(false);

// 转账表单验证规则
const transferRules = computed(() => {
  const baseRules = {
    transferType: [
      { required: true, message: '请选择转账方式', trigger: 'change' }
    ],
    recipient: transferForm.value.transferType === 'wallet'
      ? [
          { required: true, message: '请输入收款人银行账号', trigger: 'blur' },
          { min: 10, max: 25, message: '银行账号长度应为10-25位', trigger: 'blur' },
          { pattern: /^\d+$/, message: '银行账号只能包含数字', trigger: 'blur' }
        ]
      : [
          { required: true, message: '请输入收款人手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
        ],
    amount: [
      { required: true, message: '请输入转账金额', trigger: 'blur' },
      { pattern: /^[1-9]\d*(\.\d{1,2})?$/, message: '请输入有效的金额', trigger: 'blur' }
    ],
    payPassword: [
      { required: true, message: '请输入支付密码', trigger: 'blur' },
      { pattern: /^\d{6}$/, message: '支付密码必须是6位数字', trigger: 'blur' }
    ]
  };

  // 银行卡转账时添加额外验证
  if (transferForm.value.transferType === 'bank') {
    baseRules.fromCardId = [
      { required: true, message: '请选择转出银行卡', trigger: 'change' }
    ];
    baseRules.toCardId = [
      { required: true, message: '请选择收款银行卡', trigger: 'change' }
    ];
  }

  return baseRules;
});

const receiveForm = ref({
  amount: '',
  description: ''
});

// 银行卡数据
const bankCards = ref([
  { id: 1, name: '招商银行储蓄卡 (**** 1234)' },
  { id: 2, name: '建设银行信用卡 (**** 5678)' }
]);

// 最近交易数据
const recentTransactions = ref([]);

// 获取交易图标
const getTransactionIcon = (type) => {
  return type === 'income' ? '💰' : '💸';
};

// 获取交易类型显示文本
const getTransactionTitle = (transaction) => {
  const typeMap = {
    1: '充值',
    2: '转账',
    3: '收款',
    4: '消费',
    5: '提现'
  };

  let title = typeMap[transaction.type] || '未知交易';

  // 如果有交易对方，显示更详细的信息
  if (transaction.counterparty) {
    if (transaction.type === 2) {
      title = `转账给${transaction.counterparty}`;
    } else if (transaction.type === 3) {
      title = `收到${transaction.counterparty}转账`;
    }
  }

  return title;
};

// 获取交易类型（收入/支出）
const getTransactionType = (transaction) => {
  // 充值(1)和收款(3)是收入，转账(2)、消费(4)、提现(5)是支出
  return [1, 3].includes(transaction.type) ? 'income' : 'expense';
};

// 格式化交易时间
const formatTransactionTime = (dateStr) => {
  const date = new Date(dateStr);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  const transactionDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  const timeStr = date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });

  if (transactionDate.getTime() === today.getTime()) {
    return `今天 ${timeStr}`;
  } else if (transactionDate.getTime() === yesterday.getTime()) {
    return `昨天 ${timeStr}`;
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    }) + ' ' + timeStr;
  }
};

// 加载最近交易记录
const loadRecentTransactions = async () => {
  try {
    const response = await transactionApi.getRecentTransactions(currentUserId, 4);

    if (response && response.code === 0) {
      // 转换数据格式以适配前端显示
      recentTransactions.value = (response.data || []).map(transaction => ({
        id: transaction.txnId,
        title: getTransactionTitle(transaction),
        type: getTransactionType(transaction),
        amount: transaction.amount,
        time: formatTransactionTime(transaction.createdAt),
        remark: transaction.remark,
        counterparty: transaction.counterparty,
        txnNo: transaction.txnNo
      }));

      console.log(`成功加载 ${recentTransactions.value.length} 条最近交易记录`);
    } else {
      console.warn('获取最近交易失败:', response?.msg);
    }
  } catch (error) {
    console.error('获取最近交易失败:', error);
  }
};

// 转账方式切换
const onTransferTypeChange = (type) => {
  // 清空相关字段
  transferForm.value.fromCardId = '';
  transferForm.value.toCardId = '';
  transferForm.value.recipient = '';
  recipientBankCards.value = [];
  allBankCards.value = [];

  if (transferFormRef.value) {
    transferFormRef.value.clearValidate();
  }
};

// 加载收款人银行卡
const loadRecipientBankCards = async () => {
  if (!transferForm.value.recipient || recipientCardsLoading.value) return;

  recipientCardsLoading.value = true;
  try {
    // 根据手机号查询用户银行卡
    const response = await bankCardApi.getCardsByPhone(transferForm.value.recipient);

    if (response && response.code === 0) {
      recipientBankCards.value = response.data || [];
      if (recipientBankCards.value.length === 0) {
        ElMessage.warning('该用户暂无可用银行卡');
      }
    } else {
      ElMessage.error(response?.msg || '获取收款人银行卡失败');
    }
  } catch (error) {
    console.error('获取收款人银行卡失败:', error);
    ElMessage.error('获取收款人银行卡失败');
  } finally {
    recipientCardsLoading.value = false;
  }
};

// 加载所有银行卡（用于银行转账选择）
const loadAllBankCards = async () => {
  if (allCardsLoading.value) return;

  allCardsLoading.value = true;
  try {
    const response = await bankCardApi.getAllCards();

    if (response && response.code === 0) {
      allBankCards.value = response.data || [];
      console.log(`加载了 ${allBankCards.value.length} 张银行卡`);
    } else {
      ElMessage.error(response?.msg || '获取银行卡列表失败');
    }
  } catch (error) {
    console.error('获取银行卡列表失败:', error);
    ElMessage.error('获取银行卡列表失败');
  } finally {
    allCardsLoading.value = false;
  }
};

// 搜索银行卡
const searchBankCards = async (query) => {
  if (!query) {
    loadAllBankCards();
    return;
  }

  allCardsLoading.value = true;
  try {
    // 在已加载的银行卡中搜索
    allBankCards.value = allBankCards.value.filter(card =>
      card.bankName.includes(query) ||
      card.cardNumber.includes(query) ||
      card.cardHolder.includes(query)
    );
  } catch (error) {
    console.error('搜索银行卡失败:', error);
  } finally {
    allCardsLoading.value = false;
  }
};

// 加载用户银行卡
const loadUserBankCards = async () => {
  if (bankCardsLoading.value) return; // 防止重复加载

  bankCardsLoading.value = true;
  try {
    console.log('加载用户银行卡，用户ID:', currentUserId);
    const response = await bankCardApi.getUserCards(currentUserId);

    if (response && response.code === 0) {
      userBankCards.value = response.data || [];
      console.log(`成功加载 ${userBankCards.value.length} 张银行卡`);

      if (userBankCards.value.length === 0) {
        ElMessage.info('您还没有添加银行卡，请先添加银行卡');
      }
    } else {
      console.warn('获取银行卡失败:', response?.msg);
      ElMessage.error(response?.msg || '获取银行卡列表失败');
    }
  } catch (error) {
    console.error('获取银行卡失败:', error);
    ElMessage.error('网络连接失败，无法获取银行卡列表');
  } finally {
    bankCardsLoading.value = false;
  }
};

// 处理充值
const handleRecharge = async () => {
  if (!rechargeFormRef.value) return;

  try {
    await rechargeFormRef.value.validate();

    rechargeLoading.value = true;

    const amount = parseFloat(rechargeForm.value.amount);
    const selectedCard = userBankCards.value.find(card => card.cardId === rechargeForm.value.bankCardId);

    // 调用充值API
    const rechargeData = {
      userId: currentUserId,
      amount: amount,
      cardId: rechargeForm.value.bankCardId,
      payPassword: rechargeForm.value.payPassword,
      remark: rechargeForm.value.remark || '钱包充值'
    };

    const response = await walletApi.recharge(rechargeData);

    if (response.code === 0) {
      // 重新获取钱包余额和最近交易
      await loadWalletBalance();
      await loadRecentTransactions();
      ElMessage.success(`充值成功！从${selectedCard.bankName}充值¥${amount.toLocaleString()}`);
      showRechargeDialog.value = false;

      // 重置表单
      rechargeForm.value = { amount: '', bankCardId: '', payPassword: '', remark: '' };
      if (rechargeFormRef.value) {
        rechargeFormRef.value.resetFields();
      }
    } else {
      ElMessage.error(response.msg || '充值失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('充值失败:', error);
      ElMessage.error('充值失败，请重试');
    }
  } finally {
    rechargeLoading.value = false;
  }
};

// 处理提现
const handleWithdraw = async () => {
  if (!withdrawFormRef.value) return;

  try {
    await withdrawFormRef.value.validate();
    withdrawLoading.value = true;

    const amount = parseFloat(withdrawForm.value.amount);
    const selectedCard = userBankCards.value.find(card => card.cardId === withdrawForm.value.bankCardId);

    if (amount > walletBalance.value) {
      ElMessage.error('余额不足');
      return;
    }

    // 调用提现API
    const withdrawData = {
      userId: currentUserId,
      amount: amount,
      cardId: withdrawForm.value.bankCardId,
      payPassword: withdrawForm.value.payPassword,
      remark: withdrawForm.value.remark || '钱包提现'
    };

    const response = await walletApi.withdraw(withdrawData);

    if (response.code === 0) {
      // 重新获取钱包余额和最近交易
      await loadWalletBalance();
      await loadRecentTransactions();
      ElMessage.success(`提现成功！提现到${selectedCard.bankName}，金额：¥${amount.toLocaleString()}`);
      showWithdrawDialog.value = false;

      // 重置表单
      withdrawForm.value = { amount: '', bankCardId: '', payPassword: '', remark: '' };
      if (withdrawFormRef.value) {
        withdrawFormRef.value.resetFields();
      }
    } else {
      ElMessage.error(response.msg || '提现失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提现失败:', error);
      ElMessage.error('提现失败，请重试');
    }
  } finally {
    withdrawLoading.value = false;
  }
};

// 处理转账
const handleTransfer = async () => {
  if (!transferFormRef.value) return;

  try {
    await transferFormRef.value.validate();
    transferLoading.value = true;

    const amount = parseFloat(transferForm.value.amount);

    // 钱包转账时检查余额
    if (transferForm.value.transferType === 'wallet' && amount > walletBalance.value) {
      ElMessage.error('钱包余额不足');
      return;
    }

    let response;

    if (transferForm.value.transferType === 'bank') {
      // 银行卡转账
      const bankTransferData = {
        fromUserId: currentUserId,
        fromCardId: transferForm.value.fromCardId,
        toPhone: transferForm.value.recipient,
        toCardId: transferForm.value.toCardId,
        amount: amount,
        payPassword: transferForm.value.payPassword,
        remark: transferForm.value.remark || '银行卡转账'
      };

      response = await walletApi.bankTransfer(bankTransferData);

      if (response.code === 0) {
        ElMessage.success(`银行卡转账成功！转账给${transferForm.value.recipient}，金额：¥${amount.toLocaleString()}`);
        // 银行卡转账不影响钱包余额
      }
    } else {
      // 钱包转账（使用银行账号）
      const walletTransferData = {
        fromUserId: currentUserId,
        toAccount: transferForm.value.recipient, // 收款银行账号
        amount: amount,
        payPassword: transferForm.value.payPassword,
        remark: transferForm.value.remark || '钱包转账'
      };

      response = await walletApi.walletTransfer(walletTransferData);

      if (response.code === 0) {
        // 重新获取钱包余额和最近交易
        await loadWalletBalance();
        await loadRecentTransactions();
        ElMessage.success(`钱包转账成功！转账到银行账号${transferForm.value.recipient}，金额：¥${amount.toLocaleString()}`);
      }
    }

    if (response.code === 0) {
      showTransferDialog.value = false;

      // 重置表单
      transferForm.value = {
        transferType: 'wallet',
        fromCardId: '',
        toCardId: '',
        recipient: '',
        amount: '',
        payPassword: '',
        remark: ''
      };
      recipientBankCards.value = [];

      if (transferFormRef.value) {
        transferFormRef.value.resetFields();
      }
    } else {
      ElMessage.error(response.msg || '转账失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('转账失败:', error);
      ElMessage.error('转账失败，请重试');
    }
  } finally {
    transferLoading.value = false;
  }
};

// 生成收款码
const generateQRCode = () => {
  ElMessage.success('收款码已生成！');
  // 这里可以实现实际的二维码生成逻辑
};

// 获取钱包余额
const loadWalletBalance = async () => {
  try {
    const response = await walletApi.getBalance(currentUserId);
    if (response && response.code === 0) {
      walletBalance.value = response.data.balance || 0;
    } else {
      console.warn('获取钱包余额失败:', response?.msg);
    }
  } catch (error) {
    console.error('获取钱包余额出错:', error);
  }
};

// 生命周期
onMounted(() => {
  loadUserBankCards();
  loadWalletBalance();
  loadRecentTransactions();
});
</script>

<style scoped>
.wallet-container {
  padding: 20px;
}

/* 钱包概览 */
.wallet-overview {
  margin-bottom: 30px;
}

.overview-card {
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.overview-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.overview-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.overview-icon {
  font-size: 40px;
  opacity: 0.8;
}

.overview-info h3 {
  font-size: 14px;
  color: #666;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.overview-amount {
  font-size: 24px;
  font-weight: 700;
  margin: 5px 0;
  color: #2c3e50;
}

.overview-level {
  font-size: 20px;
  font-weight: 600;
  margin: 5px 0;
  color: #27ae60;
}

.overview-desc {
  font-size: 12px;
  color: #999;
  margin: 0;
}

/* 不同类型的概览卡片颜色 */
.balance-overview {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.balance-overview .overview-info h3,
.balance-overview .overview-amount,
.balance-overview .overview-desc {
  color: white;
}

.bank-overview {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.bank-overview .overview-info h3,
.bank-overview .overview-amount,
.bank-overview .overview-desc {
  color: white;
}

.limit-overview {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.limit-overview .overview-info h3,
.limit-overview .overview-amount,
.limit-overview .overview-desc {
  color: white;
}

.security-overview {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.security-overview .overview-info h3,
.security-overview .overview-level,
.security-overview .overview-desc {
  color: white;
}

/* 余额卡片 */
.balance-card {
  margin-bottom: 30px;
  border-radius: 16px;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.balance-card :deep(.el-card__body) {
  padding: 30px;
}

.balance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.balance-info h2 {
  font-size: 18px;
  margin: 0 0 10px 0;
  opacity: 0.9;
}

.balance-amount {
  font-size: 36px;
  font-weight: 700;
  margin: 10px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.balance-desc {
  font-size: 14px;
  margin: 0;
  opacity: 0.8;
}

.balance-icon {
  font-size: 60px;
  opacity: 0.8;
}

.balance-actions {
  display: flex;
  gap: 15px;
}

.balance-actions .el-button {
  flex: 1;
  height: 45px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-weight: 600;
}

.balance-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 快捷功能 */
.quick-functions {
  margin-bottom: 30px;
}

.function-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  border: none;
}

.function-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.function-content {
  text-align: center;
  padding: 25px 15px;
}

.function-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.function-content h3 {
  font-size: 18px;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.function-content p {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

/* 最近交易 */
.recent-transactions {
  border-radius: 12px;
  border: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.transaction-list {
  padding: 0;
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f1f3f4;
  transition: all 0.2s ease;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-item:hover {
  background: #f8f9fa;
  margin: 0 -20px;
  padding: 16px 20px;
  border-radius: 8px;
}

.transaction-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin-right: 12px;
}

.transaction-icon.income {
  background: #d4edda;
}

.transaction-icon.expense {
  background: #f8d7da;
}

.transaction-details {
  flex: 1;
}

.transaction-title {
  font-size: 15px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.transaction-time {
  font-size: 13px;
  color: #6c757d;
}

.transaction-amount {
  font-size: 16px;
  font-weight: 600;
}

.transaction-amount.income {
  color: #28a745;
}

.transaction-amount.expense {
  color: #dc3545;
}

/* 收钱对话框 */
.receive-money-content {
  text-align: center;
}

.qr-code {
  margin-bottom: 20px;
}

.qr-placeholder {
  width: 200px;
  height: 200px;
  border: 2px dashed #ddd;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  background: #f8f9fa;
}

.qr-icon {
  font-size: 48px;
  margin-bottom: 10px;
  opacity: 0.6;
}

.qr-placeholder p {
  color: #7f8c8d;
  margin: 0;
}

/* 银行卡选项样式 */
.bank-card-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 0;
}

.card-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.card-main-info {
  display: flex;
  align-items: center;
}

.bank-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.card-number {
  font-size: 12px;
  color: #7f8c8d;
  margin-top: 2px;
}

.card-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

.card-balance {
  font-size: 12px;
  color: #27ae60;
  font-weight: 600;
}

.card-holder {
  font-size: 12px;
  color: #666;
}

.card-type {
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-end;
}

/* 空银行卡状态 */
.empty-bank-cards {
  text-align: center;
  padding: 20px;
  color: #999;
}

.empty-bank-cards .el-icon {
  font-size: 48px;
  margin-bottom: 10px;
  opacity: 0.6;
}

.empty-bank-cards p {
  margin: 10px 0;
  font-size: 14px;
}

/* 表单提示样式 */
.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* 对话框内的警告信息 */
.el-alert {
  margin-top: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .balance-actions {
    flex-direction: column;
  }

  .quick-functions .el-col {
    margin-bottom: 15px;
  }

  .function-content {
    padding: 20px 10px;
  }

  .function-icon {
    font-size: 36px;
  }
}
</style>