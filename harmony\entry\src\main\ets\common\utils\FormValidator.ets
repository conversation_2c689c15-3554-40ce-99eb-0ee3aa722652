import { ValidationRule, ValidationResult, FormValidationState } from '../types/index';

/**
 * 表单验证工具类
 * 提供常用的表单验证规则和验证方法
 */
export class FormValidator {
  private static instance: FormValidator;

  private constructor() {}

  public static getInstance(): FormValidator {
    if (!FormValidator.instance) {
      FormValidator.instance = new FormValidator();
    }
    return FormValidator.instance;
  }

  /**
   * 验证单个字段
   */
  public validateField(value: any, rules: ValidationRule[]): ValidationResult {
    for (const rule of rules) {
      const result = this.applyRule(value, rule);
      if (!result.isValid) {
        return result;
      }
    }
    return { isValid: true };
  }

  /**
   * 验证整个表单
   */
  public validateForm(formData: Record<string, any>, formRules: Record<string, ValidationRule[]>): FormValidationState {
    const validationState: FormValidationState = {};

    for (const fieldName in formRules) {
      const value = formData[fieldName];
      const rules = formRules[fieldName];
      validationState[fieldName] = this.validateField(value, rules);
    }

    return validationState;
  }

  /**
   * 检查表单是否全部有效
   */
  public isFormValid(validationState: FormValidationState): boolean {
    return Object.values(validationState).every(result => result.isValid);
  }

  /**
   * 应用单个验证规则
   */
  private applyRule(value: any, rule: ValidationRule): ValidationResult {
    // 必填验证
    if (rule.required && this.isEmpty(value)) {
      return { isValid: false, errorMessage: '此字段为必填项' };
    }

    // 如果值为空且不是必填，则跳过其他验证
    if (this.isEmpty(value) && !rule.required) {
      return { isValid: true };
    }

    // 最小长度验证
    if (rule.minLength !== undefined && value.length < rule.minLength) {
      return { isValid: false, errorMessage: `最少需要${rule.minLength}个字符` };
    }

    // 最大长度验证
    if (rule.maxLength !== undefined && value.length > rule.maxLength) {
      return { isValid: false, errorMessage: `最多允许${rule.maxLength}个字符` };
    }

    // 正则表达式验证
    if (rule.pattern && !rule.pattern.test(value)) {
      return { isValid: false, errorMessage: '格式不正确' };
    }

    // 自定义验证器
    if (rule.validator) {
      const result = rule.validator(value);
      if (typeof result === 'string') {
        return { isValid: false, errorMessage: result };
      }
      if (!result) {
        return { isValid: false, errorMessage: '验证失败' };
      }
    }

    return { isValid: true };
  }

  /**
   * 检查值是否为空
   */
  private isEmpty(value: any): boolean {
    return value === null || value === undefined || value === '' || 
           (Array.isArray(value) && value.length === 0);
  }

  // ==================== 常用验证规则 ====================

  /**
   * 手机号验证规则
   */
  public static phoneRules(): ValidationRule[] {
    return [
      { required: true },
      { minLength: 11, maxLength: 11 },
      { pattern: /^1[3-9]\d{9}$/, validator: (value) => /^1[3-9]\d{9}$/.test(value) || '请输入正确的手机号' }
    ];
  }

  /**
   * 密码验证规则
   */
  public static passwordRules(minLength: number = 6): ValidationRule[] {
    return [
      { required: true },
      { minLength: minLength },
      { validator: (value) => value.length >= minLength || `密码长度不能少于${minLength}位` }
    ];
  }

  /**
   * 身份证号验证规则
   */
  public static idCardRules(): ValidationRule[] {
    return [
      { required: true },
      { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/ },
      { validator: (value) => this.validateIdCard(value) || '请输入正确的身份证号' }
    ];
  }

  /**
   * 真实姓名验证规则
   */
  public static realNameRules(): ValidationRule[] {
    return [
      { required: true },
      { minLength: 2, maxLength: 20 },
      { pattern: /^[\u4e00-\u9fa5·]{2,20}$/ },
      { validator: (value) => /^[\u4e00-\u9fa5·]{2,20}$/.test(value) || '请输入正确的中文姓名' }
    ];
  }

  /**
   * 银行卡号验证规则
   */
  public static bankCardRules(): ValidationRule[] {
    return [
      { required: true },
      { minLength: 16, maxLength: 19 },
      { pattern: /^\d{16,19}$/ },
      { validator: (value) => /^\d{16,19}$/.test(value) || '请输入正确的银行卡号' }
    ];
  }

  /**
   * 金额验证规则
   */
  public static amountRules(min: number = 0.01, max: number = 999999.99): ValidationRule[] {
    return [
      { required: true },
      { 
        validator: (value) => {
          const amount = parseFloat(value);
          if (isNaN(amount)) return '请输入正确的金额';
          if (amount < min) return `金额不能少于${min}元`;
          if (amount > max) return `金额不能超过${max}元`;
          if (!/^\d+(\.\d{1,2})?$/.test(value)) return '金额最多保留两位小数';
          return true;
        }
      }
    ];
  }

  /**
   * 支付密码验证规则
   */
  public static payPasswordRules(): ValidationRule[] {
    return [
      { required: true },
      { minLength: 6, maxLength: 6 },
      { pattern: /^\d{6}$/ },
      { validator: (value) => /^\d{6}$/.test(value) || '支付密码必须是6位数字' }
    ];
  }

  /**
   * 身份证号校验算法
   */
  private static validateIdCard(idCard: string): boolean {
    if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(idCard)) {
      return false;
    }

    // 校验码验证
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    
    let sum = 0;
    for (let i = 0; i < 17; i++) {
      sum += parseInt(idCard[i]) * weights[i];
    }
    
    const checkCode = checkCodes[sum % 11];
    return checkCode === idCard[17].toUpperCase();
  }
}

/**
 * 导出单例实例
 */
export const formValidator = FormValidator.getInstance();
