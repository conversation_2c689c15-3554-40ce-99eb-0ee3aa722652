package com.icss.springbootbig.controller;

import com.icss.springbootbig.entity.Transaction;
import com.icss.springbootbig.result.R;
import com.icss.springbootbig.service.TransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
@CrossOrigin(origins = {"http://localhost:5173", "http://127.0.0.1:5173"})
@RestController
@RequestMapping("/api/transaction")
public class TransactionController {
    @Autowired
    private TransactionService transactionService;

    @GetMapping("/query")
    public R<List<Transaction>> queryTransactions(
            @RequestParam(required = false) Integer userId,
            @RequestParam(required = false) Integer type,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        return R.success("查询成功",
                transactionService.queryTransactions(userId, type, startDate, endDate));
    }

    @GetMapping("/recent/{userId}")
    public R<List<Transaction>> getRecentTransactions(
            @PathVariable Integer userId,
            @RequestParam(defaultValue = "5") Integer limit) {
        return R.success("查询成功",
                transactionService.getRecentTransactions(userId, limit));
    }

    @DeleteMapping("/{txnId}")
    public R<Integer> deleteTransaction(@PathVariable Integer txnId) {
        return R.success("删除成功",
                transactionService.deleteTransaction(txnId));
    }

    @PostMapping("/batch-delete")
    public R<Integer> batchDeleteTransactions(@RequestBody List<Integer> txnIds) {
        return R.success("批量删除成功",
                transactionService.batchDeleteTransactions(txnIds));
    }

    @GetMapping("/detail/{txnId}")
    public R<Transaction> getTransactionDetail(
            @PathVariable Integer txnId,
            @RequestParam Integer userId) {
        try {
            Transaction transaction = transactionService.getTransactionDetail(txnId, userId);
            return R.success("查询成功", transaction);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/stats/{userId}")
    public R<Map<String, Object>> getTransactionStats(
            @PathVariable Integer userId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        try {
            Map<String, Object> stats = transactionService.getTransactionStats(userId, startDate, endDate);
            return R.success("查询成功", stats);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    @PostMapping("/generate-receive-code")
    public R<Map<String, Object>> generateReceiveCode(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            BigDecimal amount = request.get("amount") != null ?
                new BigDecimal(request.get("amount").toString()) : null;
            String remark = (String) request.get("remark");

            Map<String, Object> codeInfo = transactionService.generateReceiveCode(userId, amount, remark);
            return R.success("生成成功", codeInfo);
        } catch (Exception e) {
            return R.failure("生成失败: " + e.getMessage());
        }
    }

    @PostMapping("/scan-code-receive")
    public R<String> scanCodeReceive(@RequestBody Map<String, Object> request) {
        try {
            String receiveCode = (String) request.get("receiveCode");
            Integer payerUserId = (Integer) request.get("payerUserId");
            String payPassword = (String) request.get("payPassword");

            transactionService.scanCodeReceive(receiveCode, payerUserId, payPassword);
            return R.success("收款成功");
        } catch (Exception e) {
            return R.failure("收款失败: " + e.getMessage());
        }
    }
}