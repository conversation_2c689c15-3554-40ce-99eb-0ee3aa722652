import router from '@ohos.router';
import { tempDataManager, TempDataKeys } from '../common/storage/TempDataManager';

@Entry
@Component
struct BankSelectorPage {
  @State selectedBank: string = '';

  private bankOptions: string[] = [
    '中国工商银行', '中国建设银行', '中国农业银行', '中国银行',
    '招商银行', '交通银行', '中信银行', '光大银行',
    '华夏银行', '民生银行', '广发银行', '平安银行'
  ];

  aboutToAppear() {
    // 获取传入的参数
    const params = router.getParams() as Record<string, string>;
    this.selectedBank = params?.selectedBank || '';
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button('取消')
          .fontSize(16)
          .fontColor('#1976D2')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            router.back();
          })

        Text('选择开户银行')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(60) // 占位，保持标题居中
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')

      // 银行列表
      Scroll() {
        Column() {
          ForEach(this.bankOptions, (bank: string, index: number) => {
            Row() {
              Column() {
                Text(bank)
                  .fontSize(16)
                  .fontColor('#333333')
                  .fontWeight(this.selectedBank === bank ? FontWeight.Medium : FontWeight.Normal)
                  .alignSelf(ItemAlign.Start)

                // 银行简介
                Text(this.getBankDescription(bank))
                  .fontSize(12)
                  .fontColor('#999999')
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 4 })
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Start)

              if (this.selectedBank === bank) {
                Text('✓')
                  .fontSize(20)
                  .fontColor('#1976D2')
                  .fontWeight(FontWeight.Bold)
              }
            }
            .width('100%')
            .height(70)
            .padding({ left: 20, right: 20 })
            .backgroundColor(this.selectedBank === bank ? '#F0F8FF' : '#FFFFFF')
            .border({
              width: { bottom: 1 },
              color: '#F0F0F0'
            })
            .onClick(() => {
              // 保存选中的银行到临时数据管理器
              tempDataManager.setData(TempDataKeys.SELECTED_BANK, bank);

              // 返回上一页
              router.back();
            })
          })
        }
      }
      .layoutWeight(1)
      .backgroundColor('#FFFFFF')
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  // 获取银行简介
  getBankDescription(bank: string): string {
    const descriptions: Record<string, string> = {
      '中国工商银行': '全球最大的商业银行',
      '中国建设银行': '基础设施建设银行',
      '中国农业银行': '服务三农的国有银行',
      '中国银行': '国际化程度最高的银行',
      '招商银行': '零售银行的佼佼者',
      '交通银行': '历史悠久的全国性银行',
      '中信银行': '改革开放的窗口银行',
      '光大银行': '综合金融服务集团',
      '华夏银行': '快速发展的股份制银行',
      '民生银行': '主要服务民营企业',
      '广发银行': '创新发展的股份制银行',
      '平安银行': '科技引领的智能银行'
    };
    return descriptions[bank] || '优质金融服务';
  }
}
