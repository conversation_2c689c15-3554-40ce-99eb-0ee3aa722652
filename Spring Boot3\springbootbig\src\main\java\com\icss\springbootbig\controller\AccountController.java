package com.icss.springbootbig.controller;

import com.icss.springbootbig.entity.Account;
import com.icss.springbootbig.result.R;
import com.icss.springbootbig.service.AccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.math.BigDecimal;
@CrossOrigin(origins = {"http://localhost:5173", "http://127.0.0.1:5173"})
@RestController
@RequestMapping("/api/account")
public class AccountController {
    @Autowired
    private AccountService accountService;

    @GetMapping("/{userId}")
    public R<Account> getAccountByUserId(@PathVariable Integer userId) {
        Account account = accountService.getAccountByUserId(userId);
        return R.success("查询成功", account);
    }

    @PutMapping("/balance")
    public R<String> updateBalance(
            @RequestParam Integer accountId,
            @RequestParam BigDecimal amount) {
        accountService.updateBalance(accountId, amount);
        return R.success("余额更新成功");
    }

    @PostMapping("/deposit")
    public R<String> deposit(
            @RequestParam Integer accountId,
            @RequestParam BigDecimal amount,
            @RequestParam Integer cardId) {
        accountService.deposit(accountId, amount, cardId);
        return R.success("充值成功");
    }

    @PostMapping("/withdraw")
    public R<String> withdraw(
            @RequestParam Integer accountId,
            @RequestParam BigDecimal amount,
            @RequestParam Integer cardId) {
        accountService.withdraw(accountId, amount, cardId);
        return R.success("提现成功");
    }
}