<template>
  <div class="bank-card-table-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>银行卡数据管理</h2>
        <p>支持表格内直接编辑银行卡信息</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="loadBankCards" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="success" @click="addNewCard">
          <el-icon><Plus /></el-icon>
          添加银行卡
        </el-button>
      </div>
    </div>

    <!-- 数据来源指示器 -->
    <div class="data-indicator">
      <el-tag :type="isUsingDatabaseData ? 'success' : 'warning'" size="large">
        {{ isUsingDatabaseData ? '数据库数据' : '模拟数据' }}
      </el-tag>
      <span class="data-count">共 {{ bankCards.length }} 条记录</span>
    </div>

    <!-- 银行卡表格 -->
    <el-table 
      :data="bankCards" 
      v-loading="loading"
      border
      stripe
      style="width: 100%"
      @cell-click="handleCellClick"
    >
      <!-- 卡片ID -->
      <el-table-column prop="cardId" label="卡片ID" width="80" align="center" />
      
      <!-- 用户ID -->
      <el-table-column prop="userId" label="用户ID" width="80" align="center" />
      
      <!-- 银行名称 - 可编辑 -->
      <el-table-column label="银行名称" width="120">
        <template #default="{ row, $index }">
          <div v-if="editingCell.rowIndex === $index && editingCell.field === 'bankName'">
            <el-select 
              v-model="row.bankName" 
              @change="saveField(row, 'bankName')"
              @blur="cancelEdit"
              size="small"
              style="width: 100%"
            >
              <el-option
                v-for="bank in bankList"
                :key="bank.bankId"
                :label="bank.bankName"
                :value="bank.bankName"
              />
            </el-select>
          </div>
          <div v-else class="editable-cell" @click="startEdit($index, 'bankName')">
            {{ row.bankName }}
            <el-icon class="edit-icon"><Edit /></el-icon>
          </div>
        </template>
      </el-table-column>
      
      <!-- 卡号 -->
      <el-table-column prop="cardNumber" label="卡号" width="180">
        <template #default="{ row }">
          {{ formatCardNumber(row.cardNumber) }}
        </template>
      </el-table-column>
      
      <!-- 卡片类型 - 可编辑 -->
      <el-table-column label="卡片类型" width="100">
        <template #default="{ row, $index }">
          <div v-if="editingCell.rowIndex === $index && editingCell.field === 'cardType'">
            <el-select 
              v-model="row.cardType" 
              @change="saveField(row, 'cardType')"
              @blur="cancelEdit"
              size="small"
              style="width: 100%"
            >
              <el-option :label="'储蓄卡'" :value="1" />
              <el-option :label="'信用卡'" :value="2" />
            </el-select>
          </div>
          <div v-else class="editable-cell" @click="startEdit($index, 'cardType')">
            {{ row.cardType === 1 ? '储蓄卡' : '信用卡' }}
            <el-icon class="edit-icon"><Edit /></el-icon>
          </div>
        </template>
      </el-table-column>
      
      <!-- 持卡人 - 可编辑 -->
      <el-table-column label="持卡人" width="120">
        <template #default="{ row, $index }">
          <div v-if="editingCell.rowIndex === $index && editingCell.field === 'cardHolder'">
            <el-input 
              v-model="row.cardHolder" 
              @blur="saveField(row, 'cardHolder')"
              @keyup.enter="saveField(row, 'cardHolder')"
              size="small"
              ref="editInput"
            />
          </div>
          <div v-else class="editable-cell" @click="startEdit($index, 'cardHolder')">
            {{ row.cardHolder }}
            <el-icon class="edit-icon"><Edit /></el-icon>
          </div>
        </template>
      </el-table-column>
      
      <!-- 手机号 - 可编辑 -->
      <el-table-column label="手机号" width="130">
        <template #default="{ row, $index }">
          <div v-if="editingCell.rowIndex === $index && editingCell.field === 'phone'">
            <el-input 
              v-model="row.phone" 
              @blur="saveField(row, 'phone')"
              @keyup.enter="saveField(row, 'phone')"
              size="small"
              maxlength="11"
            />
          </div>
          <div v-else class="editable-cell" @click="startEdit($index, 'phone')">
            {{ row.phone || '未设置' }}
            <el-icon class="edit-icon"><Edit /></el-icon>
          </div>
        </template>
      </el-table-column>
      
      <!-- 余额 -->
      <el-table-column prop="balance" label="余额" width="120" align="right">
        <template #default="{ row }">
          ¥{{ (row.balance || 0).toLocaleString() }}
        </template>
      </el-table-column>
      
      <!-- 状态 - 可编辑 -->
      <el-table-column label="状态" width="100" align="center">
        <template #default="{ row, $index }">
          <div v-if="editingCell.rowIndex === $index && editingCell.field === 'status'">
            <el-select 
              v-model="row.status" 
              @change="saveField(row, 'status')"
              @blur="cancelEdit"
              size="small"
              style="width: 100%"
            >
              <el-option :label="'正常'" :value="1" />
              <el-option :label="'冻结'" :value="0" />
            </el-select>
          </div>
          <div v-else class="editable-cell" @click="startEdit($index, 'status')">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'" size="small">
              {{ row.status === 1 ? '正常' : '冻结' }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      
      <!-- 默认卡 - 可编辑 -->
      <el-table-column label="默认卡" width="100" align="center">
        <template #default="{ row }">
          <el-switch 
            v-model="row.isDefault" 
            :active-value="1" 
            :inactive-value="0"
            @change="toggleDefault(row)"
          />
        </template>
      </el-table-column>
      
      <!-- 操作 -->
      <el-table-column label="操作" width="150" align="center" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="viewDetails(row)">
            详情
          </el-button>
          <el-button type="danger" size="small" @click="deleteCard(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 银行卡详情对话框 -->
    <el-dialog v-model="showDetailsDialog" title="银行卡详情" width="500px">
      <div v-if="selectedCard" class="card-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="卡片ID">{{ selectedCard.cardId }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ selectedCard.userId }}</el-descriptions-item>
          <el-descriptions-item label="银行名称">{{ selectedCard.bankName }}</el-descriptions-item>
          <el-descriptions-item label="卡号">{{ formatCardNumber(selectedCard.cardNumber) }}</el-descriptions-item>
          <el-descriptions-item label="卡片类型">{{ selectedCard.cardType === 1 ? '储蓄卡' : '信用卡' }}</el-descriptions-item>
          <el-descriptions-item label="持卡人">{{ selectedCard.cardHolder }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ selectedCard.phone || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="余额">¥{{ (selectedCard.balance || 0).toLocaleString() }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedCard.status === 1 ? 'success' : 'danger'">
              {{ selectedCard.status === 1 ? '正常' : '冻结' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="默认卡">{{ selectedCard.isDefault === 1 ? '是' : '否' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedCard.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDate(selectedCard.updatedAt) }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="showDetailsDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Edit } from '@element-plus/icons-vue'
import bankCardApi from '@/api/bankCard'
import { bankApi } from '@/api/bank'
import { getUserInfo } from '@/stores/user'

// 响应式数据
const loading = ref(false)
const bankCards = ref([])
const bankList = ref([])
const isUsingDatabaseData = ref(false)
const showDetailsDialog = ref(false)
const selectedCard = ref(null)

// 编辑状态
const editingCell = ref({
  rowIndex: -1,
  field: ''
})

// 当前用户ID
const currentUserId = ref(null)

// 初始化
onMounted(() => {
  const userInfo = getUserInfo()
  if (userInfo && userInfo.userId) {
    currentUserId.value = userInfo.userId
    loadBankCards()
    loadBankList()
  } else {
    ElMessage.error('请先登录')
  }
})

// 加载银行卡数据
const loadBankCards = async () => {
  loading.value = true
  try {
    console.log('从数据库加载银行卡数据...')
    const response = await bankCardApi.getAllCards() // 获取所有银行卡
    
    if (response && response.code === 0) {
      bankCards.value = response.data || []
      isUsingDatabaseData.value = true
      ElMessage.success(`加载成功，共 ${bankCards.value.length} 条记录`)
      console.log('银行卡数据:', bankCards.value)
    } else {
      console.warn('数据库返回错误:', response?.msg)
      bankCards.value = []
      isUsingDatabaseData.value = false
      ElMessage.warning('数据库暂无数据')
    }
  } catch (error) {
    console.error('加载银行卡失败:', error)
    bankCards.value = []
    isUsingDatabaseData.value = false
    ElMessage.error('网络连接异常')
  } finally {
    loading.value = false
  }
}

// 加载银行列表
const loadBankList = async () => {
  try {
    const response = await bankApi.getAllBanks()
    if (response && response.code === 0) {
      bankList.value = response.data || []
    }
  } catch (error) {
    console.error('加载银行列表失败:', error)
  }
}

// 开始编辑
const startEdit = (rowIndex, field) => {
  editingCell.value = { rowIndex, field }
  nextTick(() => {
    // 自动聚焦到输入框
    const input = document.querySelector('.el-input__inner')
    if (input) {
      input.focus()
    }
  })
}

// 取消编辑
const cancelEdit = () => {
  editingCell.value = { rowIndex: -1, field: '' }
}

// 保存字段
const saveField = async (row, field) => {
  try {
    console.log(`保存字段 ${field}:`, row[field])
    
    const updateData = {
      [field]: row[field]
    }
    
    const response = await bankCardApi.updateField(row.cardId, updateData)
    
    if (response && response.code === 0) {
      ElMessage.success('更新成功')
      cancelEdit()
      // 可选：重新加载数据以确保同步
      // await loadBankCards()
    } else {
      ElMessage.error(response?.msg || '更新失败')
      // 恢复原值
      await loadBankCards()
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('网络连接失败')
    // 恢复原值
    await loadBankCards()
  }
}

// 切换默认卡
const toggleDefault = async (row) => {
  try {
    const response = await bankCardApi.updateField(row.cardId, { isDefault: row.isDefault })
    
    if (response && response.code === 0) {
      ElMessage.success('默认卡设置已更新')
      // 如果设置为默认卡，需要取消其他卡的默认状态
      if (row.isDefault === 1) {
        bankCards.value.forEach(card => {
          if (card.cardId !== row.cardId) {
            card.isDefault = 0
          }
        })
      }
    } else {
      ElMessage.error('更新失败')
      // 恢复原值
      row.isDefault = row.isDefault === 1 ? 0 : 1
    }
  } catch (error) {
    console.error('更新失败:', error)
    ElMessage.error('网络连接失败')
    // 恢复原值
    row.isDefault = row.isDefault === 1 ? 0 : 1
  }
}

// 查看详情
const viewDetails = (row) => {
  selectedCard.value = row
  showDetailsDialog.value = true
}

// 删除银行卡
const deleteCard = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 ${row.bankName} 银行卡吗？删除后无法恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await bankCardApi.adminDeleteCard(row.cardId)
    
    if (response && response.code === 0) {
      ElMessage.success('删除成功')
      await loadBankCards()
    } else {
      ElMessage.error(response?.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 添加新银行卡
const addNewCard = () => {
  ElMessage.info('请使用银行卡管理页面添加新银行卡')
}

// 处理单元格点击
const handleCellClick = (row, column, cell, event) => {
  // 可以在这里添加额外的单元格点击逻辑
}

// 格式化卡号
const formatCardNumber = (cardNumber) => {
  if (!cardNumber) return ''
  return `**** **** **** ${cardNumber.slice(-4)}`
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style scoped>
.bank-card-table-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h2 {
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.header-left p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.data-indicator {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.data-count {
  color: #666;
  font-size: 14px;
}

.editable-cell {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  position: relative;
  min-height: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.editable-cell:hover {
  background-color: #f0f9ff;
}

.edit-icon {
  opacity: 0;
  transition: opacity 0.2s;
  font-size: 12px;
  color: #409eff;
}

.editable-cell:hover .edit-icon {
  opacity: 1;
}

.card-details {
  margin: 20px 0;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-input--small) {
  --el-input-height: 28px;
}

:deep(.el-select--small) {
  --el-select-height: 28px;
}
</style>
