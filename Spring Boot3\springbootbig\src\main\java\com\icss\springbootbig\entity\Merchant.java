package com.icss.springbootbig.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;

@Data
@TableName("merchants")
public class Merchant {
    @TableId(type = IdType.AUTO)
    private Integer merchantId;

    private String merchantName;
    private String merchantNo;
    private Integer category;
    private String contactPhone;
    private String address;
    private Integer status;

    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;
}