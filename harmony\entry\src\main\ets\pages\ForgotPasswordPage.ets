import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { SendSmsCodeRequest, VerifyResetCodeRequest, ResetPasswordRequest } from '../common/types/index';
import { httpClient } from '../common/http/HttpClient';

@Entry
@Component
struct ForgotPasswordPage {
  @State phone: string = '';
  @State code: string = '';
  @State newPassword: string = '';
  @State confirmPassword: string = '';
  @State currentStep: number = 1; // 1: 验证手机号, 2: 重置密码
  @State isLoading: boolean = false;
  @State countdown: number = 0;
  @State canSendCode: boolean = true;

  build() {
    Column() {
      // 顶部导航
      Row() {
        Button() {
          Image($r('app.media.ic_back'))
            .width(24)
            .height(24)
            .fillColor('#333333')
        }
        .width(40)
        .height(40)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          router.back();
        })

        Text('忘记密码')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .fontColor('#333333')
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        // 占位，保持标题居中
        Row().width(40).height(40)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .justifyContent(FlexAlign.SpaceBetween)

      // 步骤指示器
      Row() {
        // 步骤1
        Stack() {
          Circle({ width: 24, height: 24 })
            .fill(this.currentStep >= 1 ? '#1976D2' : '#E0E0E0')

          Text('1')
            .fontSize(12)
            .fontColor(Color.White)
        }
        .width(24)
        .height(24)

        // 连接线
        Divider()
          .width(60)
          .height(2)
          .color(this.currentStep >= 2 ? '#1976D2' : '#E0E0E0')
          .margin({ left: 12, right: 12 })

        // 步骤2
        Stack() {
          Circle({ width: 24, height: 24 })
            .fill(this.currentStep >= 2 ? '#1976D2' : '#E0E0E0')

          Text('2')
            .fontSize(12)
            .fontColor(Color.White)
        }
        .width(24)
        .height(24)
      }
      .width('100%')
      .justifyContent(FlexAlign.Center)
      .margin({ top: 30, bottom: 10 })

      // 步骤说明
      Row() {
        Text('验证手机号')
          .fontSize(12)
          .fontColor(this.currentStep >= 1 ? '#1976D2' : '#999999')
          .width(80)
          .textAlign(TextAlign.Center)

        Text('重置密码')
          .fontSize(12)
          .fontColor(this.currentStep >= 2 ? '#1976D2' : '#999999')
          .width(80)
          .textAlign(TextAlign.Center)
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceEvenly)
      .margin({ bottom: 40 })

      // 表单内容
      Column() {
        if (this.currentStep === 1) {
          this.buildStep1()
        } else {
          this.buildStep2()
        }
      }
      .width('100%')
      .padding({ left: 24, right: 24 })

      Blank()
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#FFFFFF')
  }

  @Builder
  buildStep1() {
    Column() {
      Text('验证手机号')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .margin({ bottom: 8 })

      Text('请输入您的注册手机号，我们将发送验证码')
        .fontSize(14)
        .fontColor('#666666')
        .margin({ bottom: 40 })

      // 手机号输入框
      Column() {
        Text('手机号')
          .fontSize(14)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        TextInput({ placeholder: '请输入手机号', text: this.phone })
          .type(InputType.PhoneNumber)
          .maxLength(11)
          .fontSize(16)
          .height(48)
          .borderRadius(8)
          .backgroundColor('#F8F9FA')
          .border({ width: 1, color: '#E0E0E0' })
          .onChange((value: string) => {
            this.phone = value;
          })
      }
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 20 })

      // 验证码输入框
      Column() {
        Text('验证码')
          .fontSize(14)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        Row() {
          TextInput({ placeholder: '请输入验证码', text: this.code })
            .type(InputType.Number)
            .maxLength(6)
            .fontSize(16)
            .height(48)
            .borderRadius(8)
            .backgroundColor('#F8F9FA')
            .border({ width: 1, color: '#E0E0E0' })
            .layoutWeight(1)
            .onChange((value: string) => {
              this.code = value;
            })

          Button(this.countdown > 0 ? `${this.countdown}s` : '获取验证码')
            .fontSize(12)
            .fontColor(this.canSendCode ? '#1976D2' : '#999999')
            .backgroundColor(this.canSendCode ? '#E3F2FD' : '#F5F5F5')
            .width(100)
            .height(48)
            .enabled(this.canSendCode && this.phone.length === 11)
            .onClick(() => {
              this.sendSmsCode();
            })
        }
      }
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 40 })

      // 下一步按钮
      Button('下一步')
        .width('100%')
        .height(48)
        .fontSize(16)
        .fontColor(Color.White)
        .backgroundColor('#1976D2')
        .borderRadius(8)
        .enabled(this.isStep1Valid())
        .opacity(this.isStep1Valid() ? 1 : 0.5)
        .onClick(() => {
          this.verifyCodeAndNext();
        })
    }
    .alignItems(HorizontalAlign.Start)
  }

  @Builder
  buildStep2() {
    Column() {
      Text('重置密码')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .fontColor('#333333')
        .margin({ bottom: 8 })

      Text('请设置新的登录密码')
        .fontSize(14)
        .fontColor('#666666')
        .margin({ bottom: 40 })

      // 新密码输入框
      Column() {
        Text('新密码')
          .fontSize(14)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        TextInput({ placeholder: '请输入新密码', text: this.newPassword })
          .type(InputType.Password)
          .fontSize(16)
          .height(48)
          .borderRadius(8)
          .backgroundColor('#F8F9FA')
          .border({ width: 1, color: '#E0E0E0' })
          .showPasswordIcon(true)
          .onChange((value: string) => {
            this.newPassword = value;
          })
      }
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 20 })

      // 确认密码输入框
      Column() {
        Text('确认密码')
          .fontSize(14)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        TextInput({ placeholder: '请再次输入新密码', text: this.confirmPassword })
          .type(InputType.Password)
          .fontSize(16)
          .height(48)
          .borderRadius(8)
          .backgroundColor('#F8F9FA')
          .border({ width: 1, color: '#E0E0E0' })
          .showPasswordIcon(true)
          .onChange((value: string) => {
            this.confirmPassword = value;
          })
      }
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 40 })

      // 完成按钮
      Button('完成重置')
        .width('100%')
        .height(48)
        .fontSize(16)
        .fontColor(Color.White)
        .backgroundColor('#1976D2')
        .borderRadius(8)
        .enabled(this.isStep2Valid())
        .opacity(this.isStep2Valid() ? 1 : 0.5)
        .onClick(() => {
          this.resetPassword();
        })
    }
    .alignItems(HorizontalAlign.Start)
  }

  /**
   * 判断步骤1是否有效
   */
  isStep1Valid(): boolean {
    return !!(this.phone && this.phone.length === 11 && 
              this.code && this.code.length === 6 && 
              !this.isLoading);
  }

  /**
   * 判断步骤2是否有效
   */
  isStep2Valid(): boolean {
    return !!(this.newPassword && this.newPassword.length >= 6 && 
              this.confirmPassword && this.confirmPassword === this.newPassword && 
              !this.isLoading);
  }

  /**
   * 发送短信验证码
   */
  async sendSmsCode() {
    if (!this.canSendCode || this.phone.length !== 11) {
      return;
    }

    try {
      const requestData: SendSmsCodeRequest = {
        phone: this.phone,
        type: 'reset'
      };
      
      await httpClient.post<void>('/user/send-sms-code', requestData);

      promptAction.showToast({ message: '验证码发送成功' });
      
      // 开始倒计时
      this.startCountdown();
      
    } catch (error) {
      console.error('发送验证码失败:', error);
      let errorMessage = '发送验证码失败';
      if (error instanceof Error) {
        errorMessage = `发送失败: ${error.message}`;
      }
      promptAction.showToast({ message: errorMessage });
    }
  }

  /**
   * 开始倒计时
   */
  startCountdown() {
    this.canSendCode = false;
    this.countdown = 60;
    
    const timer = setInterval(() => {
      this.countdown--;
      if (this.countdown <= 0) {
        clearInterval(timer);
        this.canSendCode = true;
        this.countdown = 0;
      }
    }, 1000);
  }

  /**
   * 验证验证码并进入下一步
   */
  async verifyCodeAndNext() {
    if (this.isLoading) return;

    this.isLoading = true;

    try {
      // 验证验证码
      const requestData: VerifyResetCodeRequest = {
        phone: this.phone,
        code: this.code
      };

      const response = await httpClient.post<void>('/user/verify-reset-code', requestData);

      promptAction.showToast({ message: '验证成功' });
      this.currentStep = 2;
      
    } catch (error) {
      console.error('验证失败:', error);
      let errorMessage = '验证码错误或已过期';
      if (error instanceof Error) {
        errorMessage = `验证失败: ${error.message}`;
      }
      promptAction.showToast({ message: errorMessage });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 重置密码
   */
  async resetPassword() {
    if (this.isLoading) return;

    this.isLoading = true;

    try {
      const requestData: ResetPasswordRequest = {
        phone: this.phone,
        code: this.code,
        newPassword: this.newPassword
      };

      await httpClient.post<void>('/user/reset-password', requestData);

      promptAction.showToast({ message: '密码重置成功，请使用新密码登录' });
      
      // 返回登录页
      router.replaceUrl({
        url: 'pages/LoginPage'
      });
      
    } catch (error) {
      console.error('重置密码失败:', error);
      let errorMessage = '重置密码失败';
      if (error instanceof Error) {
        errorMessage = `重置失败: ${error.message}`;
      }
      promptAction.showToast({ message: errorMessage });
    } finally {
      this.isLoading = false;
    }
  }
}
