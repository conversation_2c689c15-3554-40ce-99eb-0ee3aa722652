if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface HelpCenterPage_Params {
    faqData?: FaqItem[];
}
import router from "@ohos:router";
class HelpCenterPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.faqData = [
            {
                question: '如何注册E-Wallet账户？',
                answer: '1. 点击"注册"按钮\n2. 输入手机号码\n3. 设置登录密码\n4. 填写真实姓名和身份证号\n5. 完成注册'
            },
            {
                question: '忘记登录密码怎么办？',
                answer: '1. 在登录页面点击"忘记密码"\n2. 输入注册手机号\n3. 获取短信验证码\n4. 验证成功后设置新密码'
            },
            {
                question: '如何绑定银行卡？',
                answer: '1. 进入"银行卡"页面\n2. 点击"添加银行卡"\n3. 输入银行卡信息\n4. 持卡人姓名必须与实名认证一致\n5. 完成绑定'
            },
            {
                question: '支付密码有什么用？',
                answer: '支付密码用于保护您的资金安全，在进行转账、充值、提现等操作时需要输入。建议设置与登录密码不同的6位数字密码。'
            },
            {
                question: '如何充值到钱包？',
                answer: '1. 进入"钱包"页面\n2. 点击"充值"\n3. 选择银行卡\n4. 输入充值金额\n5. 输入支付密码完成充值'
            },
            {
                question: '提现多久到账？',
                answer: '提现申请提交后，一般1-3个工作日内到账。具体到账时间可能因银行处理速度而有所差异。'
            },
            {
                question: '转账有限额吗？',
                answer: '转账金额不能超过您设置的支付限额。您可以在"设置"中修改支付限额，默认为5000元。'
            },
            {
                question: '如何查看交易记录？',
                answer: '在"我的"页面点击"交易记录"，可以查看所有的充值、提现、转账等交易明细。'
            },
            {
                question: '账户安全如何保障？',
                answer: 'E-Wallet采用银行级安全加密技术，支付密码独立设置，所有交易都需要密码验证，确保您的资金安全。'
            },
            {
                question: '如何修改个人信息？',
                answer: '进入"我的"页面，点击"设置"，可以修改支付限额、支付密码等信息。身份信息一旦认证无法修改。'
            }
        ];
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: HelpCenterPage_Params) {
        if (params.faqData !== undefined) {
            this.faqData = params.faqData;
        }
    }
    updateStateVars(params: HelpCenterPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
    }
    aboutToBeDeleted() {
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    // 常见问题数据
    private faqData: FaqItem[];
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(53:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(55:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('返回');
            Button.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(56:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('帮助中心');
            Text.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(64:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(70:9)", "entry");
            Text.width(60);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        // 内容区域
        this.HelpContent.bind(this)();
        Column.pop();
    }
    HelpContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(88:5)", "entry");
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(89:7)", "entry");
            Column.padding(16);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('常见问题');
            Text.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(90:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const item = _item;
                this.FaqItem.bind(this)(item, index);
            };
            this.forEachUpdateFunction(elmtId, this.faqData, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 联系我们
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(102:9)", "entry");
            // 联系我们
            Column.width('100%');
            // 联系我们
            Column.padding(20);
            // 联系我们
            Column.borderRadius(12);
            // 联系我们
            Column.backgroundColor('#FFFFFF');
            // 联系我们
            Column.margin({ top: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('联系我们');
            Text.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(103:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('客服热线：400-888-8888');
            Text.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(110:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('客服邮箱：<EMAIL>');
            Text.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(116:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('工作时间：周一至周日 9:00-21:00');
            Text.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(122:11)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        // 联系我们
        Column.pop();
        Column.pop();
        Scroll.pop();
    }
    FaqItem(item: FaqItem, index: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(142:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.borderRadius(12);
            Column.backgroundColor('#FFFFFF');
            Column.margin({ bottom: 12 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(143:7)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`Q${index + 1}. ${item.question}`);
            Text.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(144:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(item.answer);
            Text.debugLine("entry/src/main/ets/pages/HelpCenterPage.ets(153:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.lineHeight(20);
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "HelpCenterPage";
    }
}
interface FaqItem {
    question: string;
    answer: string;
}
registerNamedRoute(() => new HelpCenterPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/HelpCenterPage", pageFullPath: "entry/src/main/ets/pages/HelpCenterPage", integratedHsp: "false", moduleType: "followWithHap" });
