import { ref, computed } from 'vue'
import { initializeUserData, getUserDataFromStorage, clearUserData } from '@/services/userDataService'

// 用户状态管理
const userInfo = ref(null)
const token = ref(localStorage.getItem('token') || '')
const userData = ref(null) // 用户的所有数据（银行卡、交易记录等）

// 计算属性
const isLoggedIn = computed(() => {
  return !!token.value && !!userInfo.value
})

// 设置用户信息并初始化用户数据
export async function setUserInfo(user) {
  userInfo.value = user
  localStorage.setItem('userInfo', JSON.stringify(user))

  // 初始化用户数据
  if (user && user.userId) {
    try {
      console.log('开始初始化用户数据...')
      const data = await initializeUserData(user)
      userData.value = data
      console.log('用户数据初始化完成')
    } catch (error) {
      console.error('用户数据初始化失败:', error)
      // 尝试从本地存储获取
      const cachedData = getUserDataFromStorage(user.userId)
      if (cachedData) {
        userData.value = cachedData
        console.log('使用缓存的用户数据')
      }
    }
  }
}

// 获取用户信息
export function getUserInfo() {
  if (!userInfo.value) {
    const storedUserInfo = localStorage.getItem('userInfo')
    if (storedUserInfo) {
      try {
        userInfo.value = JSON.parse(storedUserInfo)

        // 同时尝试加载用户数据
        if (userInfo.value && userInfo.value.userId && !userData.value) {
          const cachedData = getUserDataFromStorage(userInfo.value.userId)
          if (cachedData) {
            userData.value = cachedData
          }
        }
      } catch (error) {
        console.error('解析用户信息失败:', error)
        localStorage.removeItem('userInfo')
      }
    }
  }
  return userInfo.value
}

// 设置token
export function setToken(newToken) {
  token.value = newToken
  localStorage.setItem('token', newToken)
}

// 获取token
export function getToken() {
  return token.value
}

// 清除用户信息
export function clearUserInfo() {
  const userId = userInfo.value?.userId

  userInfo.value = null
  token.value = ''
  userData.value = null

  localStorage.removeItem('userInfo')
  localStorage.removeItem('token')

  // 清除用户数据
  if (userId) {
    clearUserData(userId)
  }
}

// 检查是否已登录
export function checkLogin() {
  return isLoggedIn.value
}

// 获取当前用户ID
export function getCurrentUserId() {
  const user = getUserInfo()
  return user ? user.userId : null
}

// 获取用户数据
export function getUserData() {
  return userData.value
}

// 获取用户银行卡
export function getUserBankCards() {
  return userData.value?.bankCards || []
}

// 获取用户账户信息
export function getUserAccountInfo() {
  return userData.value?.accountInfo || null
}

// 获取用户交易记录
export function getUserTransactionRecords() {
  return userData.value?.transactionRecords || []
}

// 获取银行列表
export function getBankList() {
  return userData.value?.bankList || []
}

// 刷新用户数据
export async function refreshUserData() {
  const user = getUserInfo()
  if (user && user.userId) {
    try {
      const data = await initializeUserData(user)
      userData.value = data
      return data
    } catch (error) {
      console.error('刷新用户数据失败:', error)
      throw error
    }
  }
  throw new Error('用户未登录')
}

// 导出响应式数据供组件使用
export { userInfo, token, isLoggedIn, userData }
