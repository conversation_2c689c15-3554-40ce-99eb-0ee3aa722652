import router from '@ohos.router';
import promptAction from '@ohos.promptAction';

@Entry
@Component
struct HelpCenterPage {

  // 常见问题数据
  private faqData: FaqItem[] = [
    {
      question: '如何注册E-Wallet账户？',
      answer: '1. 点击"注册"按钮\n2. 输入手机号码\n3. 设置登录密码\n4. 填写真实姓名和身份证号\n5. 完成注册'
    },
    {
      question: '忘记登录密码怎么办？',
      answer: '1. 在登录页面点击"忘记密码"\n2. 输入注册手机号\n3. 获取短信验证码\n4. 验证成功后设置新密码'
    },
    {
      question: '如何绑定银行卡？',
      answer: '1. 进入"银行卡"页面\n2. 点击"添加银行卡"\n3. 输入银行卡信息\n4. 持卡人姓名必须与实名认证一致\n5. 完成绑定'
    },
    {
      question: '支付密码有什么用？',
      answer: '支付密码用于保护您的资金安全，在进行转账、充值、提现等操作时需要输入。建议设置与登录密码不同的6位数字密码。'
    },
    {
      question: '如何充值到钱包？',
      answer: '1. 进入"钱包"页面\n2. 点击"充值"\n3. 选择银行卡\n4. 输入充值金额\n5. 输入支付密码完成充值'
    },
    {
      question: '提现多久到账？',
      answer: '提现申请提交后，一般1-3个工作日内到账。具体到账时间可能因银行处理速度而有所差异。'
    },
    {
      question: '转账有限额吗？',
      answer: '转账金额不能超过您设置的支付限额。您可以在"设置"中修改支付限额，默认为5000元。'
    },
    {
      question: '如何查看交易记录？',
      answer: '在"我的"页面点击"交易记录"，可以查看所有的充值、提现、转账等交易明细。'
    },
    {
      question: '账户安全如何保障？',
      answer: 'E-Wallet采用银行级安全加密技术，支付密码独立设置，所有交易都需要密码验证，确保您的资金安全。'
    },
    {
      question: '如何修改个人信息？',
      answer: '进入"我的"页面，点击"设置"，可以修改支付限额、支付密码等信息。身份信息一旦认证无法修改。'
    }
  ];

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button('返回')
          .fontSize(16)
          .fontColor('#1976D2')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            router.back();
          })

        Text('帮助中心')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(60) // 占位，保持标题居中
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')

      // 内容区域
      this.HelpContent()
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  HelpContent() {
    Scroll() {
      Column() {
        Text('常见问题')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 16 })

        ForEach(this.faqData, (item: FaqItem, index: number) => {
          this.FaqItem(item, index)
        })

        // 联系我们
        Column() {
          Text('联系我们')
            .fontSize(16)
            .fontColor('#333333')
            .fontWeight(FontWeight.Medium)
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 12 })

          Text('客服热线：400-888-8888')
            .fontSize(14)
            .fontColor('#666666')
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

          Text('客服邮箱：<EMAIL>')
            .fontSize(14)
            .fontColor('#666666')
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 })

          Text('工作时间：周一至周日 9:00-21:00')
            .fontSize(14)
            .fontColor('#666666')
            .alignSelf(ItemAlign.Start)
        }
        .width('100%')
        .padding(20)
        .borderRadius(12)
        .backgroundColor('#FFFFFF')
        .margin({ top: 16 })
      }
      .padding(16)
    }
    .layoutWeight(1)
  }



  @Builder
  FaqItem(item: FaqItem, index: number) {
    Column() {
      Row() {
        Text(`Q${index + 1}. ${item.question}`)
          .fontSize(16)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
      }
      .width('100%')
      .margin({ bottom: 12 })

      Text(item.answer)
        .fontSize(14)
        .fontColor('#666666')
        .lineHeight(20)
        .alignSelf(ItemAlign.Start)
    }
    .width('100%')
    .padding(20)
    .borderRadius(12)
    .backgroundColor('#FFFFFF')
    .margin({ bottom: 12 })
  }

}

interface FaqItem {
  question: string;
  answer: string;
}
