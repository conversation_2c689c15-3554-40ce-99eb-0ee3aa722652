package com.icss.springbootbig.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;

@Data
@TableName("banks")
public class Bank {
    @TableId(type = IdType.AUTO)
    private Integer bankId;
    
    private String bankCode;        // 银行代码
    private String bankName;        // 银行名称
    private String bankShortName;   // 银行简称
    private String bankLogo;        // 银行Logo URL
    private String contactPhone;    // 联系电话
    private String address;         // 总行地址
    private String website;         // 官方网站
    private String description;     // 银行描述
    private Integer status;         // 状态：1-正常，0-停用
    private Integer sortOrder;      // 排序顺序
    
    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;
}
