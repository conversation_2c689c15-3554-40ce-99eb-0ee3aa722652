<template>
  <div class="network-status">
    <el-alert
      v-if="!isOnline"
      title="网络连接异常"
      type="error"
      description="当前网络连接不稳定，部分功能可能无法正常使用。系统将使用模拟数据进行演示。"
      show-icon
      :closable="false"
    />
    
    <el-alert
      v-else-if="!backendConnected"
      title="后端服务连接异常"
      type="warning"
      description="无法连接到后端服务，系统将使用模拟数据进行演示。请检查后端服务是否正常运行。"
      show-icon
      :closable="false"
    />
    
    <el-alert
      v-else
      title="系统连接正常"
      type="success"
      description="网络连接和后端服务均正常，所有功能可正常使用。"
      show-icon
      :closable="true"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';

// 网络状态
const isOnline = ref(navigator.onLine);
const backendConnected = ref(false);

// 检查后端连接
const checkBackendConnection = async () => {
  try {
    const response = await fetch('http://localhost:8096/api/health', {
      method: 'GET',
      timeout: 5000
    });
    backendConnected.value = response.ok;
  } catch (error) {
    backendConnected.value = false;
    console.log('后端连接检查失败:', error);
  }
};

// 网络状态变化处理
const handleOnline = () => {
  isOnline.value = true;
  ElMessage.success('网络连接已恢复');
  checkBackendConnection();
};

const handleOffline = () => {
  isOnline.value = false;
  backendConnected.value = false;
  ElMessage.warning('网络连接已断开');
};

// 组件挂载时检查连接状态
onMounted(() => {
  checkBackendConnection();
  
  // 监听网络状态变化
  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
  
  // 定期检查后端连接
  const interval = setInterval(checkBackendConnection, 30000); // 每30秒检查一次
  
  // 清理定时器
  onUnmounted(() => {
    clearInterval(interval);
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  });
});
</script>

<style scoped>
.network-status {
  margin-bottom: 20px;
}

.network-status .el-alert {
  margin-bottom: 10px;
}
</style>
