{"deviceType": "phone,tablet,2in1", "buildMode": "debug", "note": "false", "logLevel": "3", "isPreview": "true", "port": "29925", "checkEntry": "true", "localPropertiesPath": "D:\\vue\\daxiangmuwallet\\wallet\\local.properties", "Path": "D:\\app\\devecostudio\\DevEco Studio\\tools\\node\\", "aceProfilePath": "D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile", "hapMode": "false", "img2bin": "true", "projectProfilePath": "D:\\vue\\daxiangmuwallet\\wallet\\build-profile.json5", "watchMode": "true", "appResource": "D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt", "aceBuildJson": "D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json", "aceModuleRoot": "D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\ets", "aceSoPath": "D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\cache\\nativeDependencies.txt", "cachePath": "D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\cache\\.default", "aceModuleBuild": "D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\assets\\default\\ets", "aceModuleJsonPath": "D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "stageRouterConfig": {"paths": ["D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json", "D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json"], "contents": ["{\"module\":{\"pages\":\"$profile:main_pages\",\"name\":\"entry\"}}", "{\"src\":[\"pages/Index\"]}"]}}