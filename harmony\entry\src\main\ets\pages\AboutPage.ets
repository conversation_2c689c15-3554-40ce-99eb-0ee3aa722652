import router from '@ohos.router';
import promptAction from '@ohos.promptAction';

@Entry
@Component
struct AboutPage {
  // 应用信息
  private appInfo: AppInfo = {
    name: 'E-Wallet',
    version: 'v1.2.0',
    buildNumber: '20250619',
    description: '安全便捷的数字钱包应用',
    email: '<EMAIL>',
    phone: '************'
  };

  // 功能特色
  private features: FeatureItem[] = [
    { icon: '🔒', title: '安全可靠', desc: '银行级安全加密，保护您的资金安全' },
    { icon: '⚡', title: '快速便捷', desc: '秒级转账，实时到账，操作简单' },
    { icon: '💳', title: '多卡管理', desc: '支持多张银行卡绑定，灵活管理' },
    { icon: '📊', title: '明细清晰', desc: '详细的交易记录，资金流向一目了然' }
  ];



  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button('返回')
          .fontSize(16)
          .fontColor('#1976D2')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            router.back();
          })

        Text('关于我们')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(60) // 占位，保持标题居中
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')

      Scroll() {
        Column() {
          // 应用Logo和基本信息
          Column() {
            // Logo
            Image($r('app.media.app_icon'))
              .width(80)
              .height(80)
              .borderRadius(16)
              .margin({ bottom: 16 })

            Text(this.appInfo.name)
              .fontSize(28)
              .fontColor('#333333')
              .fontWeight(FontWeight.Bold)
              .margin({ bottom: 8 })

            Text(this.appInfo.description)
              .fontSize(16)
              .fontColor('#666666')
              .margin({ bottom: 16 })

            Row() {
              Text(`版本 ${this.appInfo.version}`)
                .fontSize(14)
                .fontColor('#999999')
                .margin({ right: 16 })

              Text(`构建号 ${this.appInfo.buildNumber}`)
                .fontSize(14)
                .fontColor('#999999')
            }
          }
          .width('100%')
          .padding(40)
          .borderRadius(12)
          .backgroundColor('#FFFFFF')
          .margin({ bottom: 16 })

          // 功能特色
          Column() {
            Text('功能特色')
              .fontSize(18)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 16 })

            ForEach(this.features, (feature: FeatureItem) => {
              this.FeatureItem(feature)
            })
          }
          .width('100%')
          .padding(20)
          .borderRadius(12)
          .backgroundColor('#FFFFFF')
          .margin({ bottom: 16 })

          // 联系我们
          Column() {
            Text('联系我们')
              .fontSize(18)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 16 })

            this.InfoRow('客服邮箱', this.appInfo.email)
            this.InfoRow('客服热线', this.appInfo.phone)
          }
          .width('100%')
          .padding(20)
          .borderRadius(12)
          .backgroundColor('#FFFFFF')
          .margin({ bottom: 16 })

          // 版权信息
          Column() {
            Text('© 2024 E-Wallet Technology Co., Ltd.')
              .fontSize(12)
              .fontColor('#999999')
              .textAlign(TextAlign.Center)
              .margin({ bottom: 8 })

            Text('All rights reserved.')
              .fontSize(12)
              .fontColor('#999999')
              .textAlign(TextAlign.Center)
              .margin({ bottom: 8 })

            Text('本应用已通过国家信息安全等级保护认证')
              .fontSize(12)
              .fontColor('#999999')
              .textAlign(TextAlign.Center)
          }
          .width('100%')
          .padding(20)
          .borderRadius(12)
          .backgroundColor('#FFFFFF')
          .margin({ bottom: 20 })
        }
        .padding(16)
      }
      .layoutWeight(1)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  FeatureItem(feature: FeatureItem) {
    Row() {
      Text(feature.icon)
        .fontSize(24)
        .width(40)
        .textAlign(TextAlign.Center)

      Column() {
        Text(feature.title)
          .fontSize(16)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .alignSelf(ItemAlign.Start)

        Text(feature.desc)
          .fontSize(14)
          .fontColor('#666666')
          .alignSelf(ItemAlign.Start)
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)
      .margin({ left: 12 })
    }
    .width('100%')
    .padding({ top: 12, bottom: 12 })
  }

  @Builder
  InfoRow(label: string, value: string) {
    Row() {
      Text(label)
        .fontSize(14)
        .fontColor('#666666')
        .width(80)

      Text(value)
        .fontSize(14)
        .fontColor('#333333')
        .layoutWeight(1)
    }
    .width('100%')
    .margin({ bottom: 12 })
  }




}

interface AppInfo {
  name: string;
  version: string;
  buildNumber: string;
  description: string;
  email: string;
  phone: string;
}

interface FeatureItem {
  icon: string;
  title: string;
  desc: string;
}
