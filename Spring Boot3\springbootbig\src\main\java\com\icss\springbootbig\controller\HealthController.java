package com.icss.springbootbig.controller;

import com.icss.springbootbig.result.R;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = {"http://localhost:5173", "http://127.0.0.1:5173"})
public class HealthController {

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public R<String> health() {
        return R.success("服务正常运行");
    }
}
