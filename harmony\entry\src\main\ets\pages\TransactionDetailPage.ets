import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { TransactionApi } from '../api/TransactionApi';
import { Transaction } from '../common/types/index';

@Entry
@Component
struct TransactionDetailPage {
  @State transaction: Transaction | null = null;
  @State isLoading: boolean = true;
  private transactionId: number = 0;

  aboutToAppear() {
    const params = router.getParams() as Record<string, Object>;
    if (params && params.transactionId) {
      this.transactionId = params.transactionId as number;
      this.loadTransactionDetail();
    } else {
      promptAction.showToast({ message: '参数错误' });
      router.back();
    }
  }

  async loadTransactionDetail() {
    try {
      this.transaction = await TransactionApi.getTransactionDetail(this.transactionId);
    } catch (error) {
      console.error('获取交易详情失败:', error);
      promptAction.showToast({ message: '获取交易详情失败' });
      router.back();
    } finally {
      this.isLoading = false;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button('返回')
          .fontSize(16)
          .fontColor('#1976D2')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            router.back();
          })

        Text('交易详情')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(60) // 占位，保持标题居中
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')

      if (this.isLoading) {
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
            .color('#1976D2')
          
          Text('加载中...')
            .fontSize(14)
            .fontColor('#999999')
            .margin({ top: 16 })
        }
        .width('100%')
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
      } else if (this.transaction) {
        Scroll() {
          Column() {
            // 交易状态卡片
            Column() {
              Text(this.getTransactionIcon(this.transaction.transactionType))
                .fontSize(48)
                .margin({ bottom: 16 })

              Text(this.transaction.transactionType)
                .fontSize(20)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)
                .margin({ bottom: 8 })

              Text(this.formatAmount(this.transaction))
                .fontSize(32)
                .fontColor(this.getAmountColor(this.transaction))
                .fontWeight(FontWeight.Bold)
                .margin({ bottom: 16 })

              Row() {
                Text(this.transaction.status)
                  .fontSize(16)
                  .fontColor(this.getStatusColor(this.transaction.status))
                  .fontWeight(FontWeight.Medium)
                  .padding({ left: 12, right: 12, top: 6, bottom: 6 })
                  .backgroundColor(this.getStatusBackgroundColor(this.transaction.status))
                  .borderRadius(12)
              }
            }
            .width('100%')
            .padding(24)
            .margin({ top: 16 })
            .borderRadius(12)
            .backgroundColor('#FFFFFF')
            .alignItems(HorizontalAlign.Center)

            // 交易详情
            Column() {
              Text('交易详情')
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 16 })

              this.DetailItem('交易单号', this.transaction.transactionNo)
              this.DetailItem('交易时间', this.formatDateTime(this.transaction.createTime))
              this.DetailItem('交易金额', `¥${this.transaction.amount.toFixed(2)}`)
              this.DetailItem('交易类型', this.transaction.transactionType)
              this.DetailItem('支付方式', this.transaction.paymentMethod)
              
              if (this.transaction.description) {
                this.DetailItem('交易说明', this.transaction.description)
              }
              
              if (this.transaction.cardNo) {
                this.DetailItem('相关银行卡', this.maskBankCard(this.transaction.cardNo))
              }
            }
            .width('100%')
            .padding(20)
            .margin({ top: 16 })
            .borderRadius(12)
            .backgroundColor('#FFFFFF')

            // 操作按钮
            if (this.transaction.status === '成功') {
              Column() {
                Button('联系客服')
                  .width('100%')
                  .height(48)
                  .fontSize(16)
                  .fontColor('#1976D2')
                  .backgroundColor('#E3F2FD')
                  .borderRadius(8)
                  .margin({ bottom: 12 })
                  .onClick(() => {
                    promptAction.showToast({ message: '客服功能开发中' });
                  })

                Button('举报问题')
                  .width('100%')
                  .height(48)
                  .fontSize(16)
                  .fontColor('#666666')
                  .backgroundColor('#F8F9FA')
                  .borderRadius(8)
                  .onClick(() => {
                    promptAction.showToast({ message: '举报功能开发中' });
                  })
              }
              .width('100%')
              .padding(20)
              .margin({ top: 16 })
              .borderRadius(12)
              .backgroundColor('#FFFFFF')
            }
          }
          .padding({ left: 16, right: 16, bottom: 20 })
        }
        .layoutWeight(1)
        .backgroundColor('#F5F5F5')
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  DetailItem(label: string, value: string) {
    Row() {
      Text(label)
        .fontSize(14)
        .fontColor('#666666')
        .width(80)

      Text(value)
        .fontSize(14)
        .fontColor('#333333')
        .layoutWeight(1)
        .textAlign(TextAlign.End)
    }
    .width('100%')
    .height(44)
    .justifyContent(FlexAlign.SpaceBetween)
    .alignItems(VerticalAlign.Center)
  }

  getTransactionIcon(type: string): string {
    switch (type) {
      case '充值': return '⬇️';
      case '提现': return '⬆️';
      case '转账': return '💸';
      case '支付': return '💳';
      case '退款': return '↩️';
      default: return '📄';
    }
  }

  formatAmount(transaction: Transaction): string {
    const prefix = transaction.transactionType === '充值' || transaction.transactionType === '退款' ? '+' : '-';
    return `${prefix}¥${transaction.amount.toFixed(2)}`;
  }

  getAmountColor(transaction: Transaction): string {
    return transaction.transactionType === '充值' || transaction.transactionType === '退款' ? '#4CAF50' : '#333333';
  }

  getStatusColor(status: string): string {
    switch (status) {
      case '成功': return '#4CAF50';
      case '失败': return '#F44336';
      case '处理中': return '#FF9800';
      default: return '#666666';
    }
  }

  getStatusBackgroundColor(status: string): string {
    switch (status) {
      case '成功': return '#E8F5E8';
      case '失败': return '#FFEBEE';
      case '处理中': return '#FFF3E0';
      default: return '#F5F5F5';
    }
  }

  formatDateTime(dateTime: string): string {
    const date = new Date(dateTime);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  }

  maskBankCard(cardNo: string): string {
    if (!cardNo || cardNo.length < 8) {
      return cardNo;
    }
    
    // 显示前4位和后4位
    const start = cardNo.substring(0, 4);
    const end = cardNo.substring(cardNo.length - 4);
    const middle = '*'.repeat(cardNo.length - 8);
    
    return `${start}${middle}${end}`;
  }
}
