{"modulePathMap": {"entry": "D:\\vue\\daxiangmuwallet\\wallet\\entry"}, "compileMode": "esmodule", "projectRootPath": "D:\\vue\\daxiangmuwallet\\wallet", "nodeModulesPath": "D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\node_modules", "byteCodeHarInfo": {}, "declarationEntry": [], "moduleName": "entry", "hspNameOhmMap": {}, "harNameOhmMap": {}, "packageManagerType": "ohpm", "compileEntry": [], "otherCompileFiles": [], "dynamicImportLibInfo": {}, "routerMap": [], "hspResourcesMap": {}, "updateVersionInfo": {}, "anBuildOutPut": "D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\an\\arm64-v8a", "anBuildMode": "type", "buildConfigPath": ".preview\\config\\buildConfig.json"}