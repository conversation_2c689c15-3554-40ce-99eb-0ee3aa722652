if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface BankCardDemoPage_Params {
    demoCards?: DemoCard[];
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
class BankCardDemoPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__demoCards = new ObservedPropertyObjectPU([
            {
                cardId: 1,
                bankName: '中国工商银行',
                cardNumber: '****************',
                cardType: '储蓄卡',
                holderName: '张三',
                isDefault: true,
                status: '已绑定'
            },
            {
                cardId: 2,
                bankName: '招商银行',
                cardNumber: '****************',
                cardType: '信用卡',
                holderName: '张三',
                isDefault: false,
                status: '已绑定'
            },
            {
                cardId: 3,
                bankName: '中国建设银行',
                cardNumber: '****************',
                cardType: '储蓄卡',
                holderName: '张三',
                isDefault: false,
                status: '未绑定'
            }
        ], this, "demoCards");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: BankCardDemoPage_Params) {
        if (params.demoCards !== undefined) {
            this.demoCards = params.demoCards;
        }
    }
    updateStateVars(params: BankCardDemoPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__demoCards.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__demoCards.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __demoCards: ObservedPropertyObjectPU<DemoCard[]>;
    get demoCards() {
        return this.__demoCards.get();
    }
    set demoCards(newValue: DemoCard[]) {
        this.__demoCards.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(38:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(40:7)", "entry");
            // 顶部导航
            Row.width('100%');
            // 顶部导航
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(41:9)", "entry");
            Button.width(40);
            Button.height(40);
            Button.borderRadius(20);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(42:11)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#333333');
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('银行卡管理演示');
            Text.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(55:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(62:9)", "entry");
            Button.width(40);
            Button.height(40);
            Button.borderRadius(20);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                promptAction.showToast({ message: '添加银行卡功能' });
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(63:11)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#1976D2');
        }, Image);
        Button.pop();
        // 顶部导航
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡列表
            List.create();
            List.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(80:7)", "entry");
            // 银行卡列表
            List.layoutWeight(1);
            // 银行卡列表
            List.scrollBar(BarState.Off);
            // 银行卡列表
            List.padding({ left: 16, right: 16, top: 16, bottom: 20 });
            // 银行卡列表
            List.divider({ strokeWidth: 0 });
        }, List);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const card = _item;
                {
                    const itemCreation = (elmtId, isInitialRender) => {
                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                        itemCreation2(elmtId, isInitialRender);
                        if (!isInitialRender) {
                            ListItem.pop();
                        }
                        ViewStackProcessor.StopGetAccessRecording();
                    };
                    const itemCreation2 = (elmtId, isInitialRender) => {
                        ListItem.create(deepRenderFunction, true);
                        ListItem.swipeAction({
                            end: this.SwipeDeleteButton.bind(this, card)
                        });
                        ListItem.margin({ bottom: 12 });
                        ListItem.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(82:11)", "entry");
                    };
                    const deepRenderFunction = (elmtId, isInitialRender) => {
                        itemCreation(elmtId, isInitialRender);
                        this.BankCardContent.bind(this)(card);
                        ListItem.pop();
                    };
                    this.observeComponentCreation2(itemCreation2, ListItem);
                    ListItem.pop();
                }
            };
            this.forEachUpdateFunction(elmtId, this.demoCards, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        // 银行卡列表
        List.pop();
        Column.pop();
    }
    BankCardContent(card: DemoCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 真实银行卡设计
            Stack.create({ alignContent: Alignment.TopStart });
            Stack.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(104:5)", "entry");
            // 真实银行卡设计
            Stack.width('100%');
            // 真实银行卡设计
            Stack.onClick(() => {
                promptAction.showToast({ message: `点击了${card.bankName}银行卡` });
            });
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡背景
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(106:7)", "entry");
            // 银行卡背景
            Column.width('100%');
            // 银行卡背景
            Column.height(180);
            // 银行卡背景
            Column.padding(20);
            // 银行卡背景
            Column.borderRadius(12);
            // 银行卡背景
            Column.linearGradient({
                direction: GradientDirection.Right,
                colors: this.getBankCardGradient(card.bankName)
            });
            // 银行卡背景
            Column.shadow({
                radius: 8,
                color: 'rgba(0,0,0,0.15)',
                offsetX: 0,
                offsetY: 4
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡顶部信息
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(108:9)", "entry");
            // 银行卡顶部信息
            Row.width('100%');
            // 银行卡顶部信息
            Row.margin({ bottom: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(109:11)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.bankName);
            Text.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(110:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.cardType);
            Text.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(117:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#E0E0E0');
            Text.margin({ top: 2 });
        }, Text);
        Text.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(125:11)", "entry");
            Column.alignItems(HorizontalAlign.End);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 绑定状态标识
            Text.create(card.status);
            Text.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(127:13)", "entry");
            // 绑定状态标识
            Text.fontSize(10);
            // 绑定状态标识
            Text.fontColor('#FFFFFF');
            // 绑定状态标识
            Text.backgroundColor(card.status === '已绑定' ? '#4CAF50' : '#FF5722');
            // 绑定状态标识
            Text.borderRadius(8);
            // 绑定状态标识
            Text.padding({ left: 6, right: 6, top: 2, bottom: 2 });
            // 绑定状态标识
            Text.margin({ bottom: 4 });
        }, Text);
        // 绑定状态标识
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 默认卡标识
            if (card.isDefault) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('默认');
                        Text.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(137:15)", "entry");
                        Text.fontSize(10);
                        Text.fontColor('#FFFFFF');
                        Text.backgroundColor('#FF9800');
                        Text.borderRadius(8);
                        Text.padding({ left: 6, right: 6, top: 2, bottom: 2 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
        // 银行卡顶部信息
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡号 - 格式化显示
            Text.create(this.formatCardNumber(card.cardNumber));
            Text.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(151:9)", "entry");
            // 银行卡号 - 格式化显示
            Text.fontSize(18);
            // 银行卡号 - 格式化显示
            Text.fontColor('#FFFFFF');
            // 银行卡号 - 格式化显示
            Text.fontWeight(FontWeight.Medium);
            // 银行卡号 - 格式化显示
            Text.letterSpacing(2);
            // 银行卡号 - 格式化显示
            Text.margin({ bottom: 16 });
            // 银行卡号 - 格式化显示
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        // 银行卡号 - 格式化显示
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 持卡人信息
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(160:9)", "entry");
            // 持卡人信息
            Row.width('100%');
            // 持卡人信息
            Row.alignItems(VerticalAlign.Bottom);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(161:11)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('持卡人');
            Text.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(162:13)", "entry");
            Text.fontSize(10);
            Text.fontColor('#E0E0E0');
            Text.margin({ bottom: 2 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.holderName);
            Text.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(167:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Medium);
            Text.maxLines(1);
            Text.textOverflow({ overflow: TextOverflow.Ellipsis });
        }, Text);
        Text.pop();
        Column.pop();
        // 持卡人信息
        Row.pop();
        // 银行卡背景
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡装饰图案
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(196:7)", "entry");
            // 银行卡装饰图案
            Image.width(80);
            // 银行卡装饰图案
            Image.height(80);
            // 银行卡装饰图案
            Image.fillColor('rgba(255,255,255,0.1)');
            // 银行卡装饰图案
            Image.position({ x: '70%', y: '15%' });
        }, Image);
        // 真实银行卡设计
        Stack.pop();
    }
    SwipeDeleteButton(card: DemoCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(210:5)", "entry");
            Button.width(80);
            Button.height(180);
            Button.backgroundColor('#F44336');
            Button.borderRadius(12);
            Button.onClick(() => {
                promptAction.showToast({ message: `删除${card.bankName}银行卡` });
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(211:7)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('🗑️');
            Text.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(212:9)", "entry");
            Text.fontSize(24);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('删除');
            Text.debugLine("entry/src/main/ets/pages/BankCardDemoPage.ets(216:9)", "entry");
            Text.fontSize(12);
            Text.fontColor('#FFFFFF');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        Button.pop();
    }
    /**
     * 格式化银行卡号显示（带空格分隔）
     */
    private formatCardNumber(cardNo: string): string {
        if (!cardNo)
            return '';
        // 脱敏处理
        const maskedCardNo = cardNo.replace(/(\d{4})\d*(\d{4})/, '$1****$2');
        // 添加空格分隔，每4位一组
        return maskedCardNo.replace(/(.{4})/g, '$1 ').trim();
    }
    /**
     * 获取银行卡渐变色
     */
    private getBankCardGradient(bankName: string): Array<[
        string,
        number
    ]> {
        const gradients: Record<string, Array<[
            string,
            number
        ]>> = {
            '中国工商银行': [['#C41E3A', 0], ['#8B0000', 1]],
            '中国建设银行': [['#003DA5', 0], ['#001F5C', 1]],
            '中国农业银行': [['#00A651', 0], ['#006B35', 1]],
            '中国银行': [['#B8860B', 0], ['#8B6914', 1]],
            '招商银行': [['#DC143C', 0], ['#8B0000', 1]],
            '交通银行': [['#0066CC', 0], ['#003D7A', 1]],
            '中信银行': [['#FF6B35', 0], ['#CC4125', 1]],
            '光大银行': [['#9932CC', 0], ['#6A1B9A', 1]],
            '华夏银行': [['#FF1744', 0], ['#C62828', 1]],
            '民生银行': [['#00BCD4', 0], ['#0097A7', 1]],
            '广发银行': [['#FF5722', 0], ['#D84315', 1]],
            '平安银行': [['#FF9800', 0], ['#F57C00', 1]]
        };
        return gradients[bankName] || [['#1976D2', 0], ['#0D47A1', 1]];
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "BankCardDemoPage";
    }
}
interface DemoCard {
    cardId: number;
    bankName: string;
    cardNumber: string;
    cardType: string;
    holderName: string;
    isDefault: boolean;
    status: string;
}
registerNamedRoute(() => new BankCardDemoPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/BankCardDemoPage", pageFullPath: "entry/src/main/ets/pages/BankCardDemoPage", integratedHsp: "false", moduleType: "followWithHap" });
