{"app": {"bundleName": "com.ohos.myapplication", "debug": true, "versionCode": 1000000, "versionName": "1.0.0", "minAPIVersion": 11, "targetAPIVersion": 11, "apiReleaseType": "Release", "compileSdkVersion": "4.1.7.5", "compileSdkType": "OpenHarmony", "appEnvironments": [], "bundleType": "app"}, "module": {"name": "hypium", "type": "har", "deviceTypes": ["default", "tablet", "tv", "wearable", "car"], "packageName": "@ohos/hypium", "installationFree": false, "virtualMachine": "ark11.0.2.0", "compileMode": "esmodule", "dependencies": []}}