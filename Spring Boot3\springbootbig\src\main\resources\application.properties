spring.application.name=springbootbig
server.port=8096



spring.datasource.url=*******************************************************************************************************
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=root

# MyBatis配置
logging.level.org.mybatis=WARN
logging.level.org.apache.ibatis=WARN
logging.level.root=INFO
logging.level.com.icss.springbootbig=DEBUG
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.type-aliases-package=com.icss.springbootbig.entity
mybatis-plus.mapper-locations=classpath:mapper/*.xml

# 数据库连接池配置
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000

# 短信服务配置
sms.provider=mock
sms.enabled=true
sms.sign-name=电子钱包
sms.rate-limit-seconds=60
sms.daily-limit=10
sms.expire-minutes=5
sms.code-length=6

# 文件上传配置
spring.servlet.multipart.max-file-size=5MB
spring.servlet.multipart.max-request-size=5MB

# 跨域配置
spring.web.cors.allowed-origins=http://localhost:5173,http://127.0.0.1:5173
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true