<template>
    <div class="header">
      <div class="search-box">
        <el-input 
          v-model="searchQuery"
          placeholder="请输入搜索内容"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="user-area">
        <span class="welcome-message">欢迎回来，{{ username }}</span>
        <el-dropdown @command="handleCommand">
          <span class="el-dropdown-link">
            <el-avatar size="small" :src="avatarUrl"></el-avatar>
            <span class="username">{{ username }}</span>
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                <span>个人信息</span>
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>
                <span>设置</span>
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed } from 'vue'
  import { useRouter } from 'vue-router'
import {
  Search,
  ArrowDown,
  User,
  Setting,
  SwitchButton
} from '@element-plus/icons-vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { getUserInfo, clearUserInfo } from '@/stores/user'

  const router = useRouter()
  const searchQuery = ref('')

  // 从用户状态管理获取用户信息
  const currentUser = computed(() => getUserInfo())
  const username = computed(() => {
    if (currentUser.value && currentUser.value.phone) {
      // 显示手机号的前3位和后4位，中间用*代替
      const phone = currentUser.value.phone
      return phone.substring(0, 3) + '****' + phone.substring(7)
    }
    return '用户'
  })
  const avatarUrl = ref('https://picsum.photos/id/64/200/200')
  
  const handleSearch = () => {
    if (searchQuery.value.trim()) {
      ElMessage.info(`搜索: ${searchQuery.value}`)
      // 这里可以添加实际的搜索逻辑
    }
  }
  
  const handleCommand = (command) => {
    switch (command) {
      case 'profile':
        router.push('/user/info')
        break
      case 'settings':
        router.push('/user/settings')
        break
      case 'logout':
        ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          clearUserInfo() // 清除用户状态
          ElMessage.success('已退出登录')
          router.push('/login')
        }).catch(() => {
          // 用户取消了操作
        })
        break
    }
  }
  </script>
  
  <style scoped>
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    background-color: #fff;
    border-bottom: 1px solid #e4e7ed;
    padding: 0 20px;
  }
  
  .search-box {
    width: 300px;
  }
  
  .user-area {
    display: flex;
    align-items: center;
    gap: 20px;
  }
  
  .welcome-message {
    color: #606266;
    font-size: 14px;
  }
  
  .username {
    margin: 0 5px;
  }
  </style>