package com.icss.springbootbig.controller;

import com.icss.springbootbig.result.R;
import com.icss.springbootbig.service.PayPasswordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 支付密码管理控制器
 */
@RestController
@RequestMapping("/api/pay-password")
@CrossOrigin(origins = {"http://localhost:5173", "http://127.0.0.1:5173"})
public class PayPasswordController {

    @Autowired
    private PayPasswordService payPasswordService;

    /**
     * 设置默认银行卡支付密码
     */
    @PostMapping("/set-default")
    public R<String> setDefaultCardPayPassword(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            String payPassword = (String) request.get("payPassword");
            String loginPassword = (String) request.get("loginPassword");
            
            payPasswordService.setDefaultCardPayPassword(userId, payPassword, loginPassword);
            return R.success("默认银行卡支付密码设置成功");
        } catch (Exception e) {
            return R.failure("设置失败: " + e.getMessage());
        }
    }

    /**
     * 设置指定银行卡支付密码
     */
    @PostMapping("/set-card")
    public R<String> setCardPayPassword(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            Integer cardId = (Integer) request.get("cardId");
            String payPassword = (String) request.get("payPassword");
            String loginPassword = (String) request.get("loginPassword");
            
            payPasswordService.setCardPayPassword(userId, cardId, payPassword, loginPassword);
            return R.success("银行卡支付密码设置成功");
        } catch (Exception e) {
            return R.failure("设置失败: " + e.getMessage());
        }
    }

    /**
     * 修改银行卡支付密码
     */
    @PutMapping("/change-card")
    public R<String> changeCardPayPassword(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            Integer cardId = (Integer) request.get("cardId");
            String oldPayPassword = (String) request.get("oldPayPassword");
            String newPayPassword = (String) request.get("newPayPassword");
            
            payPasswordService.changeCardPayPassword(userId, cardId, oldPayPassword, newPayPassword);
            return R.success("银行卡支付密码修改成功");
        } catch (Exception e) {
            return R.failure("修改失败: " + e.getMessage());
        }
    }

    /**
     * 重置银行卡支付密码
     */
    @PutMapping("/reset-card")
    public R<String> resetCardPayPassword(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            Integer cardId = (Integer) request.get("cardId");
            String loginPassword = (String) request.get("loginPassword");
            String newPayPassword = (String) request.get("newPayPassword");
            
            payPasswordService.resetCardPayPassword(userId, cardId, loginPassword, newPayPassword);
            return R.success("银行卡支付密码重置成功");
        } catch (Exception e) {
            return R.failure("重置失败: " + e.getMessage());
        }
    }

    /**
     * 验证支付密码
     */
    @PostMapping("/verify")
    public R<Boolean> verifyPayPassword(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            String payPassword = (String) request.get("payPassword");
            
            boolean isValid = payPasswordService.verifyPayPassword(userId, payPassword);
            return R.success("验证完成", isValid);
        } catch (Exception e) {
            return R.failure("验证失败: " + e.getMessage());
        }
    }

    /**
     * 验证指定银行卡支付密码
     */
    @PostMapping("/verify-card")
    public R<Boolean> verifyCardPayPassword(@RequestBody Map<String, Object> request) {
        try {
            Integer cardId = (Integer) request.get("cardId");
            String payPassword = (String) request.get("payPassword");
            
            boolean isValid = payPasswordService.verifyCardPayPassword(cardId, payPassword);
            return R.success("验证完成", isValid);
        } catch (Exception e) {
            return R.failure("验证失败: " + e.getMessage());
        }
    }

    /**
     * 获取支付密码设置状态
     */
    @GetMapping("/status/{userId}")
    public R<Map<String, Object>> getPayPasswordStatus(@PathVariable Integer userId) {
        try {
            Map<String, Object> status = payPasswordService.getPayPasswordStatus(userId);
            return R.success("查询成功", status);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 批量设置银行卡支付密码
     */
    @PostMapping("/set-batch")
    public R<String> setBatchCardPayPassword(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            String payPassword = (String) request.get("payPassword");
            String loginPassword = (String) request.get("loginPassword");
            
            payPasswordService.setBatchCardPayPassword(userId, payPassword, loginPassword);
            return R.success("批量设置支付密码成功");
        } catch (Exception e) {
            return R.failure("设置失败: " + e.getMessage());
        }
    }

    /**
     * 测试支付密码（用于调试）
     */
    @PostMapping("/test")
    public R<Map<String, Object>> testPayPassword(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            String payPassword = (String) request.get("payPassword");
            
            // 验证支付密码
            boolean isValid = payPasswordService.verifyPayPassword(userId, payPassword);
            
            // 获取状态信息
            Map<String, Object> status = payPasswordService.getPayPasswordStatus(userId);
            status.put("passwordValid", isValid);
            status.put("testResult", isValid ? "支付密码正确" : "支付密码错误");
            
            return R.success("测试完成", status);
        } catch (Exception e) {
            return R.failure("测试失败: " + e.getMessage());
        }
    }
}
