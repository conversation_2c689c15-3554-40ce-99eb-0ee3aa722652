package com.icss.springbootbig.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 短信服务配置类
 * 用于配置短信服务提供商的相关参数
 */
@Configuration
@ConfigurationProperties(prefix = "sms")
public class SmsConfig {
    
    /**
     * 短信服务提供商类型
     * aliyun: 阿里云短信
     * tencent: 腾讯云短信
     * huawei: 华为云短信
     */
    private String provider = "mock";
    
    /**
     * 是否启用短信服务
     */
    private boolean enabled = false;
    
    /**
     * 访问密钥ID
     */
    private String accessKeyId;
    
    /**
     * 访问密钥Secret
     */
    private String accessKeySecret;
    
    /**
     * 短信签名
     */
    private String signName = "电子钱包";
    
    /**
     * 登录验证码模板ID
     */
    private String loginTemplateId;
    
    /**
     * 操作验证码模板ID
     */
    private String operationTemplateId;
    
    /**
     * 短信发送频率限制（秒）
     */
    private int rateLimitSeconds = 60;
    
    /**
     * 每日发送次数限制
     */
    private int dailyLimit = 10;
    
    /**
     * 验证码有效期（分钟）
     */
    private int expireMinutes = 5;
    
    /**
     * 验证码长度
     */
    private int codeLength = 6;

    // Getters and Setters
    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    public String getSignName() {
        return signName;
    }

    public void setSignName(String signName) {
        this.signName = signName;
    }

    public String getLoginTemplateId() {
        return loginTemplateId;
    }

    public void setLoginTemplateId(String loginTemplateId) {
        this.loginTemplateId = loginTemplateId;
    }

    public String getOperationTemplateId() {
        return operationTemplateId;
    }

    public void setOperationTemplateId(String operationTemplateId) {
        this.operationTemplateId = operationTemplateId;
    }

    public int getRateLimitSeconds() {
        return rateLimitSeconds;
    }

    public void setRateLimitSeconds(int rateLimitSeconds) {
        this.rateLimitSeconds = rateLimitSeconds;
    }

    public int getDailyLimit() {
        return dailyLimit;
    }

    public void setDailyLimit(int dailyLimit) {
        this.dailyLimit = dailyLimit;
    }

    public int getExpireMinutes() {
        return expireMinutes;
    }

    public void setExpireMinutes(int expireMinutes) {
        this.expireMinutes = expireMinutes;
    }

    public int getCodeLength() {
        return codeLength;
    }

    public void setCodeLength(int codeLength) {
        this.codeLength = codeLength;
    }
}
