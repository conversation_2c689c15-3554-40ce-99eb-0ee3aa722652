if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface BankSelectorPage_Params {
    selectedBank?: string;
    bankOptions?: string[];
}
import router from "@ohos:router";
import { tempDataManager, TempDataKeys } from "@normalized:N&&&entry/src/main/ets/common/storage/TempDataManager&";
class BankSelectorPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__selectedBank = new ObservedPropertySimplePU('', this, "selectedBank");
        this.bankOptions = [
            '中国工商银行', '中国建设银行', '中国农业银行', '中国银行',
            '招商银行', '交通银行', '中信银行', '光大银行',
            '华夏银行', '民生银行', '广发银行', '平安银行'
        ];
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: BankSelectorPage_Params) {
        if (params.selectedBank !== undefined) {
            this.selectedBank = params.selectedBank;
        }
        if (params.bankOptions !== undefined) {
            this.bankOptions = params.bankOptions;
        }
    }
    updateStateVars(params: BankSelectorPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__selectedBank.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__selectedBank.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __selectedBank: ObservedPropertySimplePU<string>;
    get selectedBank() {
        return this.__selectedBank.get();
    }
    set selectedBank(newValue: string) {
        this.__selectedBank.set(newValue);
    }
    private bankOptions: string[];
    aboutToAppear() {
        // 获取传入的参数
        const params = router.getParams() as Record<string, string>;
        this.selectedBank = params?.selectedBank || '';
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankSelectorPage.ets(22:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/BankSelectorPage.ets(24:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/BankSelectorPage.ets(25:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择开户银行');
            Text.debugLine("entry/src/main/ets/pages/BankSelectorPage.ets(33:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/BankSelectorPage.ets(39:9)", "entry");
            Text.width(60);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行列表
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/BankSelectorPage.ets(48:7)", "entry");
            // 银行列表
            Scroll.layoutWeight(1);
            // 银行列表
            Scroll.backgroundColor('#FFFFFF');
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/BankSelectorPage.ets(49:9)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const bank = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.debugLine("entry/src/main/ets/pages/BankSelectorPage.ets(51:13)", "entry");
                    Row.width('100%');
                    Row.height(70);
                    Row.padding({ left: 20, right: 20 });
                    Row.backgroundColor(this.selectedBank === bank ? '#F0F8FF' : '#FFFFFF');
                    Row.border({
                        width: { bottom: 1 },
                        color: '#F0F0F0'
                    });
                    Row.onClick(() => {
                        // 保存选中的银行到临时数据管理器
                        tempDataManager.setData(TempDataKeys.SELECTED_BANK, bank);
                        // 返回上一页
                        router.back();
                    });
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/BankSelectorPage.ets(52:15)", "entry");
                    Column.layoutWeight(1);
                    Column.alignItems(HorizontalAlign.Start);
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(bank);
                    Text.debugLine("entry/src/main/ets/pages/BankSelectorPage.ets(53:17)", "entry");
                    Text.fontSize(16);
                    Text.fontColor('#333333');
                    Text.fontWeight(this.selectedBank === bank ? FontWeight.Medium : FontWeight.Normal);
                    Text.alignSelf(ItemAlign.Start);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    // 银行简介
                    Text.create(this.getBankDescription(bank));
                    Text.debugLine("entry/src/main/ets/pages/BankSelectorPage.ets(60:17)", "entry");
                    // 银行简介
                    Text.fontSize(12);
                    // 银行简介
                    Text.fontColor('#999999');
                    // 银行简介
                    Text.alignSelf(ItemAlign.Start);
                    // 银行简介
                    Text.margin({ top: 4 });
                }, Text);
                // 银行简介
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    If.create();
                    if (this.selectedBank === bank) {
                        this.ifElseBranchUpdateFunction(0, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create('✓');
                                Text.debugLine("entry/src/main/ets/pages/BankSelectorPage.ets(70:17)", "entry");
                                Text.fontSize(20);
                                Text.fontColor('#1976D2');
                                Text.fontWeight(FontWeight.Bold);
                            }, Text);
                            Text.pop();
                        });
                    }
                    else {
                        this.ifElseBranchUpdateFunction(1, () => {
                        });
                    }
                }, If);
                If.pop();
                Row.pop();
            };
            this.forEachUpdateFunction(elmtId, this.bankOptions, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        Column.pop();
        // 银行列表
        Scroll.pop();
        Column.pop();
    }
    // 获取银行简介
    getBankDescription(bank: string): string {
        const descriptions: Record<string, string> = {
            '中国工商银行': '全球最大的商业银行',
            '中国建设银行': '基础设施建设银行',
            '中国农业银行': '服务三农的国有银行',
            '中国银行': '国际化程度最高的银行',
            '招商银行': '零售银行的佼佼者',
            '交通银行': '历史悠久的全国性银行',
            '中信银行': '改革开放的窗口银行',
            '光大银行': '综合金融服务集团',
            '华夏银行': '快速发展的股份制银行',
            '民生银行': '主要服务民营企业',
            '广发银行': '创新发展的股份制银行',
            '平安银行': '科技引领的智能银行'
        };
        return descriptions[bank] || '优质金融服务';
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "BankSelectorPage";
    }
}
registerNamedRoute(() => new BankSelectorPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/BankSelectorPage", pageFullPath: "entry/src/main/ets/pages/BankSelectorPage", integratedHsp: "false", moduleType: "followWithHap" });
