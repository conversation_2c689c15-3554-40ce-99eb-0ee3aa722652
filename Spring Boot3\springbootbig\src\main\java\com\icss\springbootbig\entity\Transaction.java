package com.icss.springbootbig.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("transactions")
public class Transaction {
    @TableId(value = "txn_id", type = IdType.AUTO)
    private Integer txnId;

    @TableField("txn_no")
    private String txnNo; // 交易流水号

    @TableField("user_id")
    private Integer userId;

    @TableField("account_id")
    private Integer accountId;

    private Integer type; // 1-充值 2-转账 3-收款 4-消费 5-提现
    private BigDecimal amount;
    private BigDecimal balance; // 交易后余额
    private String counterparty; // 交易对方

    @TableField("counterparty_phone")
    private String counterpartyPhone; // 交易对方手机号

    @TableField("merchant_id")
    private Integer merchantId;

    @TableField("card_id")
    private Integer cardId;

    @TableField("payment_method")
    private Integer paymentMethod; // 1-钱包 2-银行卡

    @TableField("payment_channel")
    private Integer paymentChannel; // 1-商户付款 2-扫码付款 3-NFC支付

    private Integer status; // 0-处理中 1-成功 2-失败
    private String remark;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;
}