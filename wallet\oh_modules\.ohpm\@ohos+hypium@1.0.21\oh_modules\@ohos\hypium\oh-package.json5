{"name": "@ohos/hypium", "version": "1.0.21", "description": "A unit test framework for OpenHarmony application", "main": "index.js", "keywords": ["测试框架", "except", "mock"], "author": "hua<PERSON>", "license": "Apache-2.0", "repository": "https://gitee.com/openharmony/testfwk_arkxtest", "homepage": "https://gitee.com/openharmony/testfwk_arkxtest", "dependencies": {}, "metadata": {"sourceRoots": ["./src/main"], "debug": true}, "compatibleSdkVersion": 11, "compatibleSdkType": "OpenHarmony", "obfuscated": false}