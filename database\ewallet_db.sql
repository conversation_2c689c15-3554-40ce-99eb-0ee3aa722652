-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        5.6.35 - MySQL Community Server (GPL)
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.10.0.7000
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


-- 导出 ewallet_db 的数据库结构
CREATE DATABASE IF NOT EXISTS `ewallet_db` /*!40100 DEFAULT CHARACTER SET utf8 */;
USE `ewallet_db`;

-- 导出  表 ewallet_db.accounts 结构
CREATE TABLE IF NOT EXISTS `accounts` (
  `account_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` int(11) NOT NULL COMMENT '关联用户ID',
  `balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '账户余额',
  `daily_limit` decimal(15,2) DEFAULT '50000.00' COMMENT '每日限额',
  `single_limit` decimal(15,2) DEFAULT '5000.00' COMMENT '单笔限额',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `monthly_limit` decimal(15,2) DEFAULT '200000.00' COMMENT '月限额',
  `status` tinyint(4) DEFAULT '1' COMMENT '账户状态：1-正常，0-冻结，-1-注销',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `idx_user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='电子钱包账户表';

-- 正在导出表  ewallet_db.accounts 的数据：~3 rows (大约)
INSERT INTO `accounts` (`account_id`, `user_id`, `balance`, `daily_limit`, `single_limit`, `created_at`, `updated_at`, `monthly_limit`, `status`) VALUES
	(1, 1, 10000.00, 50000.00, 10000.00, '2025-06-22 10:37:41', '2025-06-22 10:37:41', 200000.00, 1),
	(2, 2, 5000.00, 30000.00, 5000.00, '2025-06-22 10:37:41', '2025-06-22 10:37:41', 100000.00, 1),
	(3, 3, 20000.00, 100000.00, 20000.00, '2025-06-22 10:37:41', '2025-06-22 10:37:41', 500000.00, 1);

-- 导出  表 ewallet_db.banks 结构
CREATE TABLE IF NOT EXISTS `banks` (
  `bank_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '银行ID',
  `bank_code` varchar(20) NOT NULL COMMENT '银行代码',
  `bank_name` varchar(100) NOT NULL COMMENT '银行名称',
  `bank_short_name` varchar(50) NOT NULL COMMENT '银行简称',
  `bank_logo` varchar(255) DEFAULT NULL COMMENT '银行Logo URL',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `address` varchar(255) DEFAULT NULL COMMENT '总行地址',
  `website` varchar(255) DEFAULT NULL COMMENT '官方网站',
  `description` text COMMENT '银行描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-停用',
  `sort_order` int(11) DEFAULT '999' COMMENT '排序顺序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`bank_id`),
  UNIQUE KEY `idx_bank_code` (`bank_code`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='银行信息表';

-- 正在导出表  ewallet_db.banks 的数据：~3 rows (大约)
INSERT INTO `banks` (`bank_id`, `bank_code`, `bank_name`, `bank_short_name`, `bank_logo`, `contact_phone`, `address`, `website`, `description`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES
	(1, 'ICBC', '中国工商银行', '工商银行', NULL, '95588', NULL, NULL, NULL, 1, 1, '2025-06-22 10:37:41', '2025-06-22 10:37:41'),
	(2, 'CCB', '中国建设银行', '建设银行', NULL, '95533', NULL, NULL, NULL, 1, 2, '2025-06-22 10:37:41', '2025-06-22 10:37:41'),
	(3, 'BOC', '中国银行', '中国银行', NULL, '95566', NULL, NULL, NULL, 1, 3, '2025-06-22 10:37:41', '2025-06-22 10:37:41');

-- 导出  表 ewallet_db.bank_cards 结构
CREATE TABLE IF NOT EXISTS `bank_cards` (
  `card_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '银行卡ID',
  `user_id` int(11) NOT NULL COMMENT '关联用户ID',
  `bank_name` varchar(50) NOT NULL COMMENT '银行名称',
  `card_number` varchar(20) NOT NULL COMMENT '卡号',
  `card_type` tinyint(1) NOT NULL COMMENT '卡类型(1-借记卡 2-信用卡)',
  `card_holder` varchar(50) NOT NULL COMMENT '持卡人姓名',
  `balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '银行卡余额',
  `expiry_date` varchar(10) DEFAULT NULL COMMENT '有效期(信用卡专用)',
  `cvv` varchar(10) DEFAULT NULL COMMENT '安全码(信用卡专用)',
  `pay_password` varchar(255) DEFAULT NULL COMMENT '支付密码',
  `password` varchar(255) DEFAULT NULL COMMENT '银行卡查询密码',
  `phone` varchar(20) DEFAULT NULL COMMENT '预留手机号',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认(0-否 1-是)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1-正常 0-冻结)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`card_id`) USING BTREE,
  KEY `idx_user` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='用户银行卡表';

-- 正在导出表  ewallet_db.bank_cards 的数据：~3 rows (大约)
INSERT INTO `bank_cards` (`card_id`, `user_id`, `bank_name`, `card_number`, `card_type`, `card_holder`, `balance`, `expiry_date`, `cvv`, `pay_password`, `password`, `phone`, `is_default`, `status`, `created_at`, `updated_at`) VALUES
	(1, 1, '中国工商银行', '6222021101000000001', 1, '测试用户', 0.00, NULL, NULL, '123456', '123456', '+*************', 1, 1, '2025-06-22 10:37:41', '2025-06-22 10:47:19'),
	(2, 2, '中国建设银行', '6227003000000000002', 1, '支付用户', 0.00, NULL, NULL, '123456', '123456', '+*************', 1, 1, '2025-06-22 10:37:41', '2025-06-22 10:47:22'),
	(3, 3, '中国银行', '6217850100000000003', 1, '系统管理员', 0.00, NULL, NULL, '123456', '123456', '+*************', 1, 1, '2025-06-22 10:37:41', '2025-06-22 10:47:25');

-- 导出  表 ewallet_db.merchants 结构
CREATE TABLE IF NOT EXISTS `merchants` (
  `merchant_id` int(11) NOT NULL AUTO_INCREMENT,
  `merchant_name` varchar(50) NOT NULL,
  `merchant_no` varchar(20) NOT NULL COMMENT '商户编号',
  `category` tinyint(2) NOT NULL COMMENT '商户类别',
  `contact_phone` varchar(20) NOT NULL,
  `address` varchar(100) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1-正常 0-禁用)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`merchant_id`),
  UNIQUE KEY `idx_merchant_no` (`merchant_no`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='商户信息表';

-- 正在导出表  ewallet_db.merchants 的数据：~3 rows (大约)
INSERT INTO `merchants` (`merchant_id`, `merchant_name`, `merchant_no`, `category`, `contact_phone`, `address`, `status`, `created_at`, `updated_at`) VALUES
	(1, '京东商城', 'M001', 1, '************', '北京市朝阳区', 1, '2025-06-22 10:37:41', '2025-06-22 10:37:41'),
	(2, '天猫超市', 'M002', 1, '************', '浙江省杭州市', 1, '2025-06-22 10:37:41', '2025-06-22 10:37:41'),
	(3, '星巴克', 'M003', 2, '************', '上海市静安区', 1, '2025-06-22 10:37:41', '2025-06-22 10:37:41');

-- 导出  表 ewallet_db.transactions 结构
CREATE TABLE IF NOT EXISTS `transactions` (
  `txn_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '交易ID',
  `txn_no` varchar(32) NOT NULL COMMENT '交易流水号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `account_id` int(11) NOT NULL COMMENT '账户ID',
  `type` tinyint(2) NOT NULL COMMENT '类型(1-充值 2-转账 3-收款 4-消费)',
  `amount` decimal(15,2) NOT NULL COMMENT '交易金额',
  `balance` decimal(15,2) NOT NULL COMMENT '交易后余额',
  `counterparty` varchar(50) DEFAULT NULL COMMENT '交易对方(用户ID/商户ID)',
  `counterparty_phone` varchar(20) DEFAULT NULL COMMENT '交易对方手机号',
  `merchant_id` int(11) DEFAULT NULL COMMENT '商户ID',
  `card_id` int(11) DEFAULT NULL COMMENT '银行卡ID',
  `payment_method` tinyint(2) DEFAULT NULL COMMENT '支付方式(1-钱包 2-银行卡)',
  `payment_channel` tinyint(2) DEFAULT NULL COMMENT '支付渠道(1-商户付款 2-扫码付款 3-NFC支付)',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态(0-处理中 1-成功 2-失败)',
  `remark` varchar(255) DEFAULT NULL COMMENT '交易备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`txn_id`),
  UNIQUE KEY `idx_txn_no` (`txn_no`),
  KEY `idx_user` (`user_id`),
  KEY `idx_account` (`account_id`),
  KEY `idx_created` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='交易记录表';

-- 正在导出表  ewallet_db.transactions 的数据：~8 rows (大约)
INSERT INTO `transactions` (`txn_id`, `txn_no`, `user_id`, `account_id`, `type`, `amount`, `balance`, `counterparty`, `counterparty_phone`, `merchant_id`, `card_id`, `payment_method`, `payment_channel`, `status`, `remark`, `created_at`, `updated_at`) VALUES
	(1, 'TXN20250622001', 1, 1, 1, 5000.00, 5000.00, 'ICBC', NULL, NULL, NULL, NULL, NULL, 1, '银行卡充值', '2025-06-22 10:37:41', '2025-06-22 10:37:41'),
	(2, 'TXN20250622002', 1, 1, 1, 5000.00, 10000.00, 'ICBC', NULL, NULL, NULL, NULL, NULL, 1, '银行卡充值', '2025-06-22 10:37:41', '2025-06-22 10:37:41'),
	(3, 'TXN20250622003', 2, 2, 1, 3000.00, 3000.00, 'CCB', NULL, NULL, NULL, NULL, NULL, 1, '银行卡充值', '2025-06-22 10:37:41', '2025-06-22 10:37:41'),
	(4, 'TXN20250622004', 2, 2, 1, 2000.00, 5000.00, 'CCB', NULL, NULL, NULL, NULL, NULL, 1, '银行卡充值', '2025-06-22 10:37:41', '2025-06-22 10:37:41'),
	(5, 'TXN20250622005', 1, 1, 4, -1000.00, 9000.00, 'M001', NULL, NULL, NULL, NULL, NULL, 1, '京东购物', '2025-06-22 10:37:41', '2025-06-22 10:37:41'),
	(6, 'TXN20250622006', 1, 1, 4, -500.00, 8500.00, 'M003', NULL, NULL, NULL, NULL, NULL, 1, '星巴克消费', '2025-06-22 10:37:41', '2025-06-22 10:37:41'),
	(7, 'TXN20250622007', 1, 1, 2, -2000.00, 6500.00, '2', NULL, NULL, NULL, NULL, NULL, 1, '转账给好友', '2025-06-22 10:37:41', '2025-06-22 10:37:41'),
	(8, 'TXN20250622008', 2, 2, 3, 2000.00, 7000.00, '1', NULL, NULL, NULL, NULL, NULL, 1, '收到转账', '2025-06-22 10:37:41', '2025-06-22 10:37:41');

-- 导出  表 ewallet_db.users 结构
CREATE TABLE IF NOT EXISTS `users` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户唯一ID',
  `phone` varchar(20) NOT NULL COMMENT '手机号(带国际区号如+86)',
  `password` varchar(255) DEFAULT NULL COMMENT '明文密码',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态(1-正常 0-冻结)',
  `last_login` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `username` varchar(255) DEFAULT NULL COMMENT '用户名',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `idx_phone` (`phone`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='用户账户表';

-- 正在导出表  ewallet_db.users 的数据：~3 rows (大约)
INSERT INTO `users` (`user_id`, `phone`, `password`, `status`, `last_login`, `created_at`, `updated_at`, `username`) VALUES
	(1, '13800138000', 'test1234', 1, '2025-06-22 10:22:35', '2025-06-22 10:37:41', '2025-06-22 10:38:09', '测试用户'),
	(2, '13900002222', 'pass456', 1, '2025-06-20 19:27:32', '2025-06-22 10:37:41', '2025-06-22 10:38:04', '支付用户'),
	(3, '15000003333', 'admin789', 1, '2023-10-03 11:00:00', '2025-06-22 10:37:41', '2025-06-22 10:38:00', '系统管理员');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
