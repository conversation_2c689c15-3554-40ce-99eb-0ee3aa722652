if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ForgotPasswordPage_Params {
    phone?: string;
    code?: string;
    newPassword?: string;
    confirmPassword?: string;
    currentStep?: number;
    isLoading?: boolean;
    countdown?: number;
    canSendCode?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import type { SendSmsCodeRequest, VerifyResetCodeRequest, ResetPasswordRequest } from '../common/types/index';
import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
class ForgotPasswordPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__phone = new ObservedPropertySimplePU('', this, "phone");
        this.__code = new ObservedPropertySimplePU('', this, "code");
        this.__newPassword = new ObservedPropertySimplePU('', this, "newPassword");
        this.__confirmPassword = new ObservedPropertySimplePU('', this, "confirmPassword");
        this.__currentStep = new ObservedPropertySimplePU(1, this, "currentStep");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__countdown = new ObservedPropertySimplePU(0, this, "countdown");
        this.__canSendCode = new ObservedPropertySimplePU(true, this, "canSendCode");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ForgotPasswordPage_Params) {
        if (params.phone !== undefined) {
            this.phone = params.phone;
        }
        if (params.code !== undefined) {
            this.code = params.code;
        }
        if (params.newPassword !== undefined) {
            this.newPassword = params.newPassword;
        }
        if (params.confirmPassword !== undefined) {
            this.confirmPassword = params.confirmPassword;
        }
        if (params.currentStep !== undefined) {
            this.currentStep = params.currentStep;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.countdown !== undefined) {
            this.countdown = params.countdown;
        }
        if (params.canSendCode !== undefined) {
            this.canSendCode = params.canSendCode;
        }
    }
    updateStateVars(params: ForgotPasswordPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__phone.purgeDependencyOnElmtId(rmElmtId);
        this.__code.purgeDependencyOnElmtId(rmElmtId);
        this.__newPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__confirmPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__currentStep.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__countdown.purgeDependencyOnElmtId(rmElmtId);
        this.__canSendCode.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__phone.aboutToBeDeleted();
        this.__code.aboutToBeDeleted();
        this.__newPassword.aboutToBeDeleted();
        this.__confirmPassword.aboutToBeDeleted();
        this.__currentStep.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__countdown.aboutToBeDeleted();
        this.__canSendCode.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __phone: ObservedPropertySimplePU<string>;
    get phone() {
        return this.__phone.get();
    }
    set phone(newValue: string) {
        this.__phone.set(newValue);
    }
    private __code: ObservedPropertySimplePU<string>;
    get code() {
        return this.__code.get();
    }
    set code(newValue: string) {
        this.__code.set(newValue);
    }
    private __newPassword: ObservedPropertySimplePU<string>;
    get newPassword() {
        return this.__newPassword.get();
    }
    set newPassword(newValue: string) {
        this.__newPassword.set(newValue);
    }
    private __confirmPassword: ObservedPropertySimplePU<string>;
    get confirmPassword() {
        return this.__confirmPassword.get();
    }
    set confirmPassword(newValue: string) {
        this.__confirmPassword.set(newValue);
    }
    private __currentStep: ObservedPropertySimplePU<number>; // 1: 验证手机号, 2: 重置密码
    get currentStep() {
        return this.__currentStep.get();
    }
    set currentStep(newValue: number) {
        this.__currentStep.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __countdown: ObservedPropertySimplePU<number>;
    get countdown() {
        return this.__countdown.get();
    }
    set countdown(newValue: number) {
        this.__countdown.set(newValue);
    }
    private __canSendCode: ObservedPropertySimplePU<boolean>;
    get canSendCode() {
        return this.__canSendCode.get();
    }
    set canSendCode(newValue: boolean) {
        this.__canSendCode.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(19:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(21:7)", "entry");
            // 顶部导航
            Row.width('100%');
            // 顶部导航
            Row.height(56);
            // 顶部导航
            Row.padding({ left: 16, right: 16 });
            // 顶部导航
            Row.justifyContent(FlexAlign.SpaceBetween);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(22:9)", "entry");
            Button.width(40);
            Button.height(40);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777238, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(23:11)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#333333');
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('忘记密码');
            Text.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(35:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 占位，保持标题居中
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(43:9)", "entry");
            // 占位，保持标题居中
            Row.width(40);
            // 占位，保持标题居中
            Row.height(40);
        }, Row);
        // 占位，保持标题居中
        Row.pop();
        // 顶部导航
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 步骤指示器
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(51:7)", "entry");
            // 步骤指示器
            Row.width('100%');
            // 步骤指示器
            Row.justifyContent(FlexAlign.Center);
            // 步骤指示器
            Row.margin({ top: 30, bottom: 10 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 步骤1
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(53:9)", "entry");
            // 步骤1
            Stack.width(24);
            // 步骤1
            Stack.height(24);
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create({ width: 24, height: 24 });
            Circle.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(54:11)", "entry");
            Circle.fill(this.currentStep >= 1 ? '#1976D2' : '#E0E0E0');
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('1');
            Text.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(57:11)", "entry");
            Text.fontSize(12);
            Text.fontColor(Color.White);
        }, Text);
        Text.pop();
        // 步骤1
        Stack.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 连接线
            Divider.create();
            Divider.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(65:9)", "entry");
            // 连接线
            Divider.width(60);
            // 连接线
            Divider.height(2);
            // 连接线
            Divider.color(this.currentStep >= 2 ? '#1976D2' : '#E0E0E0');
            // 连接线
            Divider.margin({ left: 12, right: 12 });
        }, Divider);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 步骤2
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(72:9)", "entry");
            // 步骤2
            Stack.width(24);
            // 步骤2
            Stack.height(24);
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create({ width: 24, height: 24 });
            Circle.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(73:11)", "entry");
            Circle.fill(this.currentStep >= 2 ? '#1976D2' : '#E0E0E0');
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('2');
            Text.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(76:11)", "entry");
            Text.fontSize(12);
            Text.fontColor(Color.White);
        }, Text);
        Text.pop();
        // 步骤2
        Stack.pop();
        // 步骤指示器
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 步骤说明
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(88:7)", "entry");
            // 步骤说明
            Row.width('100%');
            // 步骤说明
            Row.justifyContent(FlexAlign.SpaceEvenly);
            // 步骤说明
            Row.margin({ bottom: 40 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('验证手机号');
            Text.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(89:9)", "entry");
            Text.fontSize(12);
            Text.fontColor(this.currentStep >= 1 ? '#1976D2' : '#999999');
            Text.width(80);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('重置密码');
            Text.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(95:9)", "entry");
            Text.fontSize(12);
            Text.fontColor(this.currentStep >= 2 ? '#1976D2' : '#999999');
            Text.width(80);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        // 步骤说明
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 表单内容
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(106:7)", "entry");
            // 表单内容
            Column.width('100%');
            // 表单内容
            Column.padding({ left: 24, right: 24 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.currentStep === 1) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildStep1.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.buildStep2.bind(this)();
                });
            }
        }, If);
        If.pop();
        // 表单内容
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
            Blank.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(116:7)", "entry");
        }, Blank);
        Blank.pop();
        Column.pop();
    }
    buildStep1(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(125:5)", "entry");
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('验证手机号');
            Text.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(126:7)", "entry");
            Text.fontSize(24);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('请输入您的注册手机号，我们将发送验证码');
            Text.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(132:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ bottom: 40 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 手机号输入框
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(138:7)", "entry");
            // 手机号输入框
            Column.alignItems(HorizontalAlign.Start);
            // 手机号输入框
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('手机号');
            Text.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(139:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入手机号', text: this.phone });
            TextInput.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(145:9)", "entry");
            TextInput.type(InputType.PhoneNumber);
            TextInput.maxLength(11);
            TextInput.fontSize(16);
            TextInput.height(48);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.onChange((value: string) => {
                this.phone = value;
            });
        }, TextInput);
        // 手机号输入框
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 验证码输入框
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(161:7)", "entry");
            // 验证码输入框
            Column.alignItems(HorizontalAlign.Start);
            // 验证码输入框
            Column.margin({ bottom: 40 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('验证码');
            Text.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(162:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(168:9)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入验证码', text: this.code });
            TextInput.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(169:11)", "entry");
            TextInput.type(InputType.Number);
            TextInput.maxLength(6);
            TextInput.fontSize(16);
            TextInput.height(48);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.layoutWeight(1);
            TextInput.onChange((value: string) => {
                this.code = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel(this.countdown > 0 ? `${this.countdown}s` : '获取验证码');
            Button.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(182:11)", "entry");
            Button.fontSize(12);
            Button.fontColor(this.canSendCode ? '#1976D2' : '#999999');
            Button.backgroundColor(this.canSendCode ? '#E3F2FD' : '#F5F5F5');
            Button.width(100);
            Button.height(48);
            Button.enabled(this.canSendCode && this.phone.length === 11);
            Button.onClick(() => {
                this.sendSmsCode();
            });
        }, Button);
        Button.pop();
        Row.pop();
        // 验证码输入框
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 下一步按钮
            Button.createWithLabel('下一步');
            Button.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(198:7)", "entry");
            // 下一步按钮
            Button.width('100%');
            // 下一步按钮
            Button.height(48);
            // 下一步按钮
            Button.fontSize(16);
            // 下一步按钮
            Button.fontColor(Color.White);
            // 下一步按钮
            Button.backgroundColor('#1976D2');
            // 下一步按钮
            Button.borderRadius(8);
            // 下一步按钮
            Button.enabled(this.isStep1Valid());
            // 下一步按钮
            Button.opacity(this.isStep1Valid() ? 1 : 0.5);
            // 下一步按钮
            Button.onClick(() => {
                this.verifyCodeAndNext();
            });
        }, Button);
        // 下一步按钮
        Button.pop();
        Column.pop();
    }
    buildStep2(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(216:5)", "entry");
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('重置密码');
            Text.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(217:7)", "entry");
            Text.fontSize(24);
            Text.fontWeight(FontWeight.Bold);
            Text.fontColor('#333333');
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('请设置新的登录密码');
            Text.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(223:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ bottom: 40 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 新密码输入框
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(229:7)", "entry");
            // 新密码输入框
            Column.alignItems(HorizontalAlign.Start);
            // 新密码输入框
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('新密码');
            Text.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(230:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入新密码', text: this.newPassword });
            TextInput.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(236:9)", "entry");
            TextInput.type(InputType.Password);
            TextInput.fontSize(16);
            TextInput.height(48);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.showPasswordIcon(true);
            TextInput.onChange((value: string) => {
                this.newPassword = value;
            });
        }, TextInput);
        // 新密码输入框
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 确认密码输入框
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(252:7)", "entry");
            // 确认密码输入框
            Column.alignItems(HorizontalAlign.Start);
            // 确认密码输入框
            Column.margin({ bottom: 40 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('确认密码');
            Text.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(253:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请再次输入新密码', text: this.confirmPassword });
            TextInput.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(259:9)", "entry");
            TextInput.type(InputType.Password);
            TextInput.fontSize(16);
            TextInput.height(48);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.showPasswordIcon(true);
            TextInput.onChange((value: string) => {
                this.confirmPassword = value;
            });
        }, TextInput);
        // 确认密码输入框
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 完成按钮
            Button.createWithLabel('完成重置');
            Button.debugLine("entry/src/main/ets/pages/ForgotPasswordPage.ets(275:7)", "entry");
            // 完成按钮
            Button.width('100%');
            // 完成按钮
            Button.height(48);
            // 完成按钮
            Button.fontSize(16);
            // 完成按钮
            Button.fontColor(Color.White);
            // 完成按钮
            Button.backgroundColor('#1976D2');
            // 完成按钮
            Button.borderRadius(8);
            // 完成按钮
            Button.enabled(this.isStep2Valid());
            // 完成按钮
            Button.opacity(this.isStep2Valid() ? 1 : 0.5);
            // 完成按钮
            Button.onClick(() => {
                this.resetPassword();
            });
        }, Button);
        // 完成按钮
        Button.pop();
        Column.pop();
    }
    /**
     * 判断步骤1是否有效
     */
    isStep1Valid(): boolean {
        return !!(this.phone && this.phone.length === 11 &&
            this.code && this.code.length === 6 &&
            !this.isLoading);
    }
    /**
     * 判断步骤2是否有效
     */
    isStep2Valid(): boolean {
        return !!(this.newPassword && this.newPassword.length >= 6 &&
            this.confirmPassword && this.confirmPassword === this.newPassword &&
            !this.isLoading);
    }
    /**
     * 发送短信验证码
     */
    async sendSmsCode() {
        if (!this.canSendCode || this.phone.length !== 11) {
            return;
        }
        try {
            const requestData: SendSmsCodeRequest = {
                phone: this.phone,
                type: 'reset'
            };
            await httpClient.post<void>('/user/send-sms-code', requestData);
            promptAction.showToast({ message: '验证码发送成功' });
            // 开始倒计时
            this.startCountdown();
        }
        catch (error) {
            console.error('发送验证码失败:', error);
            let errorMessage = '发送验证码失败';
            if (error instanceof Error) {
                errorMessage = `发送失败: ${error.message}`;
            }
            promptAction.showToast({ message: errorMessage });
        }
    }
    /**
     * 开始倒计时
     */
    startCountdown() {
        this.canSendCode = false;
        this.countdown = 60;
        const timer = setInterval(() => {
            this.countdown--;
            if (this.countdown <= 0) {
                clearInterval(timer);
                this.canSendCode = true;
                this.countdown = 0;
            }
        }, 1000);
    }
    /**
     * 验证验证码并进入下一步
     */
    async verifyCodeAndNext() {
        if (this.isLoading)
            return;
        this.isLoading = true;
        try {
            // 验证验证码
            const requestData: VerifyResetCodeRequest = {
                phone: this.phone,
                code: this.code
            };
            const response = await httpClient.post<void>('/user/verify-reset-code', requestData);
            promptAction.showToast({ message: '验证成功' });
            this.currentStep = 2;
        }
        catch (error) {
            console.error('验证失败:', error);
            let errorMessage = '验证码错误或已过期';
            if (error instanceof Error) {
                errorMessage = `验证失败: ${error.message}`;
            }
            promptAction.showToast({ message: errorMessage });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 重置密码
     */
    async resetPassword() {
        if (this.isLoading)
            return;
        this.isLoading = true;
        try {
            const requestData: ResetPasswordRequest = {
                phone: this.phone,
                code: this.code,
                newPassword: this.newPassword
            };
            await httpClient.post<void>('/user/reset-password', requestData);
            promptAction.showToast({ message: '密码重置成功，请使用新密码登录' });
            // 返回登录页
            router.replaceUrl({
                url: 'pages/LoginPage'
            });
        }
        catch (error) {
            console.error('重置密码失败:', error);
            let errorMessage = '重置密码失败';
            if (error instanceof Error) {
                errorMessage = `重置失败: ${error.message}`;
            }
            promptAction.showToast({ message: errorMessage });
        }
        finally {
            this.isLoading = false;
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "ForgotPasswordPage";
    }
}
registerNamedRoute(() => new ForgotPasswordPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/ForgotPasswordPage", pageFullPath: "entry/src/main/ets/pages/ForgotPasswordPage", integratedHsp: "false", moduleType: "followWithHap" });
