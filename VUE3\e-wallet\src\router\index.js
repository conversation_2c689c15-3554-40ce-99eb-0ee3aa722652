import { createRouter, createWebHistory } from 'vue-router';
import Layout from '../views/Layout.vue';
import Login from '../views/Login.vue';
import Register from '../views/Register.vue';
import Dashboard from '../views/Dashboard.vue';
import BankCard from '../views/BankCard.vue';
import TransactionRecord from '../views/TransactionRecord.vue';
import Wallet from '../views/Wallet.vue';
import PersonalSettings from '../views/PersonalSettings.vue';
import Payment from '../views/Payment.vue';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { title: '用户登录' }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { title: '用户注册' }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: { title: '首页', requiresAuth: true }
      },
      {
        path: 'wallet',
        name: 'Wallet',
        component: Wallet,
        meta: { title: '钱包管理', requiresAuth: true }
      },
      {
        path: 'bankcard',
        name: 'BankCard',
        component: BankCard,
        meta: { title: '银行卡管理', requiresAuth: true }
      },
      {
        path: 'bankcard-table',
        name: 'BankCardTable',
        component: () => import('@/views/BankCardTable.vue'),
        meta: { title: '银行卡数据管理', requiresAuth: true }
      },
      {
        path: 'pay-password',
        name: 'PayPasswordManager',
        component: () => import('@/views/PayPasswordManager.vue'),
        meta: { title: '支付密码管理', requiresAuth: true }
      },
      {
        path: 'transaction',
        name: 'TransactionRecord',
        component: TransactionRecord,
        meta: { title: '交易记录', requiresAuth: true }
      },
      {
        path: 'payment',
        name: 'Payment',
        component: Payment,
        meta: { title: '支付管理', requiresAuth: true }
      },
      {
        path: 'settings',
        name: 'PersonalSettings',
        component: PersonalSettings,
        meta: { title: '个人设置', requiresAuth: true }
      },
      // 管理员功能
      {
        path: 'admin/users',
        name: 'UserManagement',
        component: () => import('@/views/user/UserList.vue'),
        meta: { title: '用户管理', requiresAuth: true }
      },
      // 银行管理（管理员功能）
      {
        path: 'admin/banks',
        name: 'BankManagement',
        component: () => import('@/views/admin/BankManagement.vue'),
        meta: { title: '银行管理', requiresAuth: true }
      },
      // API测试页面
      {
        path: 'api-test',
        name: 'ApiTest',
        component: () => import('@/views/ApiTest.vue'),
        meta: { title: 'API测试', requiresAuth: false }
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 导入用户状态管理
import { checkLogin } from '@/stores/user'

// 路由守卫
router.beforeEach((to, from, next) => {
  const isLoggedIn = checkLogin();

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - E-Wallet`;
  }

  // 如果路由需要认证
  if (to.meta.requiresAuth) {
    if (isLoggedIn) {
      next(); // 已登录，允许访问
    } else {
      next('/login'); // 未登录，跳转到登录页
    }
  } else {
    // 如果是登录或注册页面且已经登录，跳转到dashboard
    if ((to.path === '/login' || to.path === '/register') && isLoggedIn) {
      next('/dashboard');
    } else {
      next(); // 其他情况正常访问
    }
  }
});

export default router;