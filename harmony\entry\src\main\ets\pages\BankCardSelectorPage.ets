import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { BankCard, BankCardType, BankCardStatus, SpringBootBankCardResponse } from '../common/types/index';
import { httpClient } from '../common/http/HttpClient';
import { storageManager } from '../common/storage/StorageManager';

@Entry
@Component
struct BankCardSelectorPage {
  @State bankCards: BankCard[] = [];
  @State selectedCardId: number = 0;
  @State isLoading: boolean = false;
  @State showAddCard: boolean = false;
  private callbackUrl: string = '';

  aboutToAppear() {
    // 检查路由参数
    const params = router.getParams() as Record<string, Object>;
    if (params?.callbackUrl) {
      this.callbackUrl = params.callbackUrl as string;
    }
    
    this.loadBankCards();
  }

  async loadBankCards() {
    try {
      this.isLoading = true;
      
      // 从本地存储获取当前用户ID
      const cachedUserInfo = await storageManager.getUserInfo();
      if (!cachedUserInfo || !cachedUserInfo.userId) {
        console.error('无法获取用户ID');
        return;
      }

      // 调用SpringBoot3后端API获取银行卡列表
      const response = await httpClient.get<SpringBootBankCardResponse[]>(`/bank-card/user/${cachedUserInfo.userId}`);
      const bankCardList: SpringBootBankCardResponse[] = response.data;

      // 转换为本地格式，只显示已绑定的银行卡
      this.bankCards = this.convertSpringBootBankCardsToLocal(bankCardList)
        .filter(card => card.isBound === BankCardStatus.BOUND);
      
      console.log('银行卡列表加载完成，数量:', this.bankCards.length);
    } catch (error) {
      console.error('加载银行卡列表失败:', error);
      promptAction.showToast({ message: '加载银行卡列表失败' });
    } finally {
      this.isLoading = false;
    }
  }

  build() {
    Column() {
      // 顶部导航
      Row() {
        Button() {
          Image($r('app.media.ic_back'))
            .width(24)
            .height(24)
            .fillColor('#333333')
        }
        .width(40)
        .height(40)
        .borderRadius(20)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          router.back();
        })

        Text('选择银行卡')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(40) // 占位
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })

      if (this.isLoading) {
        // 加载状态
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
            .color('#1976D2')
            .margin({ bottom: 16 })
          
          Text('加载中...')
            .fontSize(14)
            .fontColor('#666666')
        }
        .width('100%')
        .height(200)
        .justifyContent(FlexAlign.Center)
      } else if (this.bankCards.length === 0) {
        // 空状态
        Column() {
          Image($r('app.media.ic_bank_card'))
            .width(80)
            .height(80)
            .fillColor('#CCCCCC')
            .margin({ bottom: 16 })

          Text('暂无可用银行卡')
            .fontSize(16)
            .fontColor('#999999')
            .margin({ bottom: 8 })

          Text('请先添加银行卡后再进行操作')
            .fontSize(14)
            .fontColor('#CCCCCC')
            .margin({ bottom: 24 })

          Button('去我的银行卡添加')
            .fontSize(14)
            .fontColor('#1976D2')
            .backgroundColor('#E3F2FD')
            .borderRadius(8)
            .padding({ left: 16, right: 16, top: 8, bottom: 8 })
            .onClick(() => {
              router.pushUrl({ url: 'pages/BankCardPage' });
            })
        }
        .width('100%')
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
        .padding(40)
      } else {
        // 银行卡列表
        Scroll() {
          Column() {
            ForEach(this.bankCards, (card: BankCard) => {
              this.BankCardItem(card)
            })
          }
          .padding({ left: 16, right: 16, top: 16, bottom: 20 })
        }
        .layoutWeight(1)
        .scrollable(ScrollDirection.Vertical)
        .scrollBar(BarState.Off)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  BankCardItem(card: BankCard) {
    Column() {
      Row() {
        // 银行卡缩略图
        Column() {
          Text(card.bankName.substring(0, 2))
            .fontSize(16)
            .fontColor('#FFFFFF')
            .fontWeight(FontWeight.Bold)
        }
        .width(48)
        .height(32)
        .borderRadius(4)
        .justifyContent(FlexAlign.Center)
        .linearGradient({
          direction: GradientDirection.Right,
          colors: this.getBankCardGradient(card.bankName)
        })
        .margin({ right: 12 })

        // 银行卡信息
        Column() {
          Row() {
            Text(card.bankName)
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .layoutWeight(1)

            if (card.isDefault) {
              Text('默认')
                .fontSize(10)
                .fontColor('#FFFFFF')
                .backgroundColor('#FF9800')
                .borderRadius(8)
                .padding({ left: 6, right: 6, top: 2, bottom: 2 })
            }
          }
          .width('100%')
          .alignItems(VerticalAlign.Center)

          Text(`${this.getCardTypeText(card.cardType)} • ${this.formatCardNumber(card.cardNo)}`)
            .fontSize(14)
            .fontColor('#666666')
            .margin({ top: 4 })
            .alignSelf(ItemAlign.Start)
        }
        .alignItems(HorizontalAlign.Start)
        .layoutWeight(1)

        // 选择状态
        if (this.selectedCardId === card.cardId) {
          Text('✓')
            .fontSize(20)
            .fontColor('#4CAF50')
            .fontWeight(FontWeight.Bold)
        } else {
          Text('○')
            .fontSize(20)
            .fontColor('#CCCCCC')
        }
      }
      .width('100%')
      .padding(16)
      .backgroundColor(this.selectedCardId === card.cardId ? '#E8F5E8' : '#FFFFFF')
      .borderRadius(8)
      .border({
        width: this.selectedCardId === card.cardId ? 2 : 1,
        color: this.selectedCardId === card.cardId ? '#4CAF50' : '#E0E0E0'
      })
      .onClick(() => {
        this.selectCard(card);
      })
    }
    .width('100%')
    .margin({ bottom: 12 })
  }

  private selectCard(card: BankCard) {
    this.selectedCardId = card.cardId;
    
    // 延迟一下再跳转，让用户看到选中效果
    setTimeout(() => {
      if (this.callbackUrl) {
        router.pushUrl({
          url: this.callbackUrl,
          params: {
            selectedCard: {
              cardId: card.cardId,
              bankName: card.bankName,
              cardNo: card.cardNo,
              cardType: card.cardType,
              holderName: card.holderName,
              maskedCardNo: card.maskedCardNo,
              isDefault: card.isDefault
            }
          }
        });
      } else {
        router.back();
      }
    }, 300);
  }

  private convertSpringBootBankCardsToLocal(bankCardList: SpringBootBankCardResponse[]): BankCard[] {
    return bankCardList.map((card: SpringBootBankCardResponse): BankCard => ({
      cardId: card.cardId || 0,
      userId: card.userId || 0,
      cardNo: card.cardNumber || '',
      cardType: this.mapBankCardType(card.cardType),
      bankName: card.bankName || '',
      holderName: card.cardHolder || '',
      isBound: card.status === 1 ? BankCardStatus.BOUND : BankCardStatus.UNBOUND,
      createTime: card.createdAt || card.createTime || '',
      updateTime: card.updatedAt || card.updateTime || '',
      maskedCardNo: card.cardNumber ? this.maskCardNo(card.cardNumber) : undefined,
      isDefault: card.isDefault === 1 || card.isDefault === true
    }));
  }

  private mapBankCardType(cardType: string | number | undefined): BankCardType {
    if (typeof cardType === 'number') {
      return cardType === 2 ? BankCardType.CREDIT : BankCardType.DEBIT;
    } else if (typeof cardType === 'string') {
      if (cardType === '信用卡' || cardType === 'CREDIT') {
        return BankCardType.CREDIT;
      }
    }
    return BankCardType.DEBIT;
  }

  private maskCardNo(cardNo: string): string {
    if (!cardNo || cardNo.length < 8) return cardNo;
    return cardNo.replace(/(\d{4})\d*(\d{4})/, '$1****$2');
  }

  private getCardTypeText(cardType: BankCardType): string {
    switch (cardType) {
      case BankCardType.CREDIT:
        return '信用卡';
      case BankCardType.DEBIT:
        return '储蓄卡';
      default:
        return '储蓄卡';
    }
  }

  private formatCardNumber(cardNo: string): string {
    if (!cardNo) return '';
    return `**** **** **** ${cardNo.slice(-4)}`;
  }

  private getBankCardGradient(bankName: string): Array<[string, number]> {
    const gradients: Record<string, Array<[string, number]>> = {
      '中国工商银行': [['#C41E3A', 0], ['#8B0000', 1]],
      '中国建设银行': [['#003DA5', 0], ['#001F5C', 1]],
      '中国农业银行': [['#00A651', 0], ['#006B35', 1]],
      '中国银行': [['#B8860B', 0], ['#8B6914', 1]],
      '招商银行': [['#DC143C', 0], ['#8B0000', 1]],
      '交通银行': [['#0066CC', 0], ['#003D7A', 1]],
      '中信银行': [['#FF6B35', 0], ['#CC4125', 1]],
      '光大银行': [['#9932CC', 0], ['#6A1B9A', 1]],
      '华夏银行': [['#FF1744', 0], ['#C62828', 1]],
      '民生银行': [['#00BCD4', 0], ['#0097A7', 1]],
      '广发银行': [['#FF5722', 0], ['#D84315', 1]],
      '平安银行': [['#FF9800', 0], ['#F57C00', 1]]
    };

    return gradients[bankName] || [['#1976D2', 0], ['#0D47A1', 1]];
  }
}
