<template>
  <div class="transaction-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>交易记录</h2>
        <p>查看您的所有交易记录，包括转账、收款、充值、提现等操作</p>
      </div>
      <div class="header-actions">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="default"
          @change="handleDateChange"
        />
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="exportRecords">
          <el-icon><Download /></el-icon>
          导出记录
        </el-button>
        <el-dropdown @command="handleQuickFilter">
          <el-button>
            快速筛选<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="today">今天</el-dropdown-item>
              <el-dropdown-item command="week">本周</el-dropdown-item>
              <el-dropdown-item command="month">本月</el-dropdown-item>
              <el-dropdown-item command="quarter">本季度</el-dropdown-item>
              <el-dropdown-item command="year">本年</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 交易统计 -->
    <el-row :gutter="20" class="transaction-stats">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card income">
          <div class="stat-content">
            <div class="stat-icon">💰</div>
            <div class="stat-info">
              <div class="stat-number">¥{{ totalIncome.toLocaleString() }}</div>
              <div class="stat-label">总收入</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="stat-card expense">
          <div class="stat-content">
            <div class="stat-icon">💸</div>
            <div class="stat-info">
              <div class="stat-number">¥{{ totalExpense.toLocaleString() }}</div>
              <div class="stat-label">总支出</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="stat-card balance">
          <div class="stat-content">
            <div class="stat-icon">📊</div>
            <div class="stat-info">
              <div class="stat-number">¥{{ (totalIncome - totalExpense).toLocaleString() }}</div>
              <div class="stat-label">净收支</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="stat-card count">
          <div class="stat-content">
            <div class="stat-icon">📝</div>
            <div class="stat-info">
              <div class="stat-number">{{ filteredTransactions.length }}</div>
              <div class="stat-label">交易笔数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 筛选和搜索 -->
    <el-card class="filter-card" shadow="hover">
      <div class="filter-content">
        <div class="filter-tabs">
          <el-button-group>
            <el-button
              :type="activeFilter === 'all' ? 'primary' : ''"
              @click="setFilter('all')"
            >
              全部交易
            </el-button>
            <el-button
              :type="activeFilter === 'recharge' ? 'primary' : ''"
              @click="setFilter('recharge')"
            >
              💰 充值
            </el-button>
            <el-button
              :type="activeFilter === 'withdraw' ? 'primary' : ''"
              @click="setFilter('withdraw')"
            >
              💎 提现
            </el-button>
            <el-button
              :type="activeFilter === 'transfer' ? 'primary' : ''"
              @click="setFilter('transfer')"
            >
              💸 转账
            </el-button>
            <el-button
              :type="activeFilter === 'receive' ? 'primary' : ''"
              @click="setFilter('receive')"
            >
              💰 收款
            </el-button>
            <el-button
              :type="activeFilter === 'payment' ? 'primary' : ''"
              @click="setFilter('payment')"
            >
              📱 消费
            </el-button>
          </el-button-group>
        </div>

        <div class="filter-status">
          <el-select v-model="statusFilter" placeholder="交易状态" size="small" style="width: 120px;">
            <el-option label="全部状态" value="all" />
            <el-option label="成功" value="成功" />
            <el-option label="处理中" value="处理中" />
            <el-option label="失败" value="失败" />
            <el-option label="已取消" value="已取消" />
          </el-select>
        </div>

        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索交易记录..."
            prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
      </div>
    </el-card>

    <!-- 交易记录列表 -->
    <el-card class="transaction-list" shadow="hover">
      <template #header>
        <div class="list-header">
          <span class="list-title">交易明细</span>
          <div class="list-actions">
            <el-button size="small" @click="refreshData">刷新</el-button>
          </div>
        </div>
      </template>

      <div class="transactions">
        <div
          v-for="transaction in paginatedTransactions"
          :key="transaction.id"
          class="transaction-item"
          @click="viewTransactionDetail(transaction)"
        >
          <div class="transaction-icon" :class="transaction.category">
            {{ getCategoryIcon(transaction.category) }}
          </div>

          <div class="transaction-info">
            <div class="transaction-main">
              <h4>{{ transaction.title }}</h4>
              <p class="transaction-desc">{{ transaction.description }}</p>
              <div class="transaction-extra">
                <span class="transaction-id">流水号：{{ transaction.transactionId }}</span>
                <span v-if="transaction.counterparty" class="counterparty">
                  {{ getCounterpartyLabel(transaction.category) }}：{{ transaction.counterparty }}
                </span>
              </div>
            </div>
            <div class="transaction-meta">
              <span class="transaction-time">{{ formatTime(transaction.time) }}</span>
              <el-tag :type="getStatusType(transaction.status)" size="small">
                {{ transaction.status }}
              </el-tag>
            </div>
          </div>

          <div class="transaction-amount" :class="transaction.type">
            <div class="amount-value">
              {{ transaction.type === 'income' ? '+' : '-' }}¥{{ transaction.amount.toLocaleString() }}
            </div>
            <div class="amount-desc">
              {{ getAmountDescription(transaction.category, transaction.type) }}
            </div>
          </div>

          <div class="transaction-actions">
            <el-button
              type="danger"
              size="small"
              @click.stop="deleteTransaction(transaction)"
              :loading="transaction.deleting"
            >
              删除
            </el-button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredTransactions.length === 0" class="empty-state">
          <div class="empty-icon">📝</div>
          <p>暂无交易记录</p>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="filteredTransactions.length > 0" class="pagination">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredTransactions.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 交易详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="交易详情" width="500px">
      <div v-if="selectedTransaction" class="transaction-detail">
        <div class="detail-header">
          <div class="detail-icon" :class="selectedTransaction.type">
            {{ getTransactionIcon(selectedTransaction.type) }}
          </div>
          <div class="detail-title">
            <h3>{{ selectedTransaction.title }}</h3>
            <p>{{ selectedTransaction.description }}</p>
          </div>
        </div>

        <div class="detail-info">
          <div class="info-row">
            <label>交易金额：</label>
            <span class="amount" :class="selectedTransaction.type">
              {{ selectedTransaction.type === 'income' ? '+' : '-' }}¥{{ selectedTransaction.amount.toLocaleString() }}
            </span>
          </div>
          <div class="info-row">
            <label>交易时间：</label>
            <span>{{ selectedTransaction.time }}</span>
          </div>
          <div class="info-row">
            <label>交易状态：</label>
            <el-tag :type="getStatusType(selectedTransaction.status)" size="small">
              {{ selectedTransaction.status }}
            </el-tag>
          </div>
          <div class="info-row">
            <label>交易单号：</label>
            <span>{{ selectedTransaction.transactionId }}</span>
          </div>
          <div v-if="selectedTransaction.remark" class="info-row">
            <label>备注：</label>
            <span>{{ selectedTransaction.remark }}</span>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button
            type="danger"
            @click="deleteTransaction(selectedTransaction)"
            :loading="selectedTransaction?.deleting"
          >
            删除交易
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Refresh, Download, ArrowDown } from '@element-plus/icons-vue';
import { transactionApi } from '@/api/transaction';
import { getCurrentUserId } from '@/stores/user';

// 响应式数据
const dateRange = ref([]);
const activeFilter = ref('all');
const statusFilter = ref('all');
const searchKeyword = ref('');
const currentPage = ref(1);
const pageSize = ref(20);
const showDetailDialog = ref(false);
const selectedTransaction = ref(null);
const loading = ref(false);

// 用户信息
const currentUserId = getCurrentUserId();
console.log('当前用户ID:', currentUserId);

// 交易记录数据
const transactions = ref([]);

// 计算属性
const totalIncome = computed(() => {
  return filteredTransactions.value
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);
});

const totalExpense = computed(() => {
  return filteredTransactions.value
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);
});

const filteredTransactions = computed(() => {
  let filtered = transactions.value;

  // 按类型筛选
  if (activeFilter.value !== 'all') {
    filtered = filtered.filter(t => t.category === activeFilter.value);
  }

  // 按状态筛选
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(t => t.status === statusFilter.value);
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(t =>
      t.title.toLowerCase().includes(keyword) ||
      t.description.toLowerCase().includes(keyword) ||
      t.transactionId.toLowerCase().includes(keyword) ||
      (t.counterparty && t.counterparty.toLowerCase().includes(keyword)) ||
      (t.remark && t.remark.toLowerCase().includes(keyword))
    );
  }

  // 按日期范围筛选
  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value;
    filtered = filtered.filter(t => {
      const transactionDate = new Date(t.time);
      return transactionDate >= startDate && transactionDate <= endDate;
    });
  }

  return filtered.sort((a, b) => new Date(b.time) - new Date(a.time));
});

const paginatedTransactions = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredTransactions.value.slice(start, end);
});

// 方法
const setFilter = (filter) => {
  activeFilter.value = filter;
  currentPage.value = 1;
};

// 处理类型变化
const handleTypeChange = (type) => {
  activeFilter.value = type || 'all';
  currentPage.value = 1;

  // 显示相应的消息
  const typeMessages = {
    'payment': '已筛选支付记录',
    'recharge': '已筛选充值记录',
    'withdraw': '已筛选提现记录',
    'transfer': '已筛选转账记录',
    'receive': '已筛选收钱记录',
    'all': '显示全部交易记录'
  };

  ElMessage.info(typeMessages[type || 'all']);
};

const handleSearch = () => {
  currentPage.value = 1;
};

const handleDateChange = () => {
  currentPage.value = 1;
};

const handleSizeChange = (newSize) => {
  pageSize.value = newSize;
  currentPage.value = 1;
};

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage;
};

// 快速筛选处理
const handleQuickFilter = (command) => {
  const now = new Date();
  let startDate, endDate;

  switch (command) {
    case 'today':
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
      break;
    case 'week':
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - now.getDay());
      startDate = new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate());
      endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
      break;
    case 'month':
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
      break;
    case 'quarter':
      const quarterStart = Math.floor(now.getMonth() / 3) * 3;
      startDate = new Date(now.getFullYear(), quarterStart, 1);
      endDate = new Date(now.getFullYear(), quarterStart + 3, 0, 23, 59, 59);
      break;
    case 'year':
      startDate = new Date(now.getFullYear(), 0, 1);
      endDate = new Date(now.getFullYear(), 11, 31, 23, 59, 59);
      break;
  }

  if (startDate && endDate) {
    dateRange.value = [startDate, endDate];
    currentPage.value = 1;
  }
};

// 获取交易类型图标
const getCategoryIcon = (category) => {
  const icons = {
    recharge: '💰',
    withdraw: '💸',
    transfer: '🔄',
    receive: '📥',
    payment: '🛒'
  };
  return icons[category] || '📝';
};

// 获取对方信息标签
const getCounterpartyLabel = (category) => {
  const labels = {
    recharge: '来源',
    withdraw: '提现到',
    transfer: '转账给',
    receive: '来自',
    payment: '支付给'
  };
  return labels[category] || '对方';
};

// 获取金额描述
const getAmountDescription = (category, type) => {
  const descriptions = {
    recharge: '充值入账',
    withdraw: '提现支出',
    transfer: type === 'income' ? '转账收入' : '转账支出',
    receive: '收款入账',
    payment: '消费支出'
  };
  return descriptions[category] || '';
};

// 格式化时间
const formatTime = (timeStr) => {
  const date = new Date(timeStr);
  const now = new Date();
  const diff = now - date;
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (days === 0) {
    return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (days === 1) {
    return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (days < 7) {
    return `${days}天前 ` + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
};

const getTransactionIcon = (type) => {
  return type === 'income' ? '💰' : '💸';
};

const getStatusType = (status) => {
  const statusMap = {
    '成功': 'success',
    '失败': 'danger',
    '处理中': 'warning',
    '已取消': 'info'
  };
  return statusMap[status] || 'info';
};

const viewTransactionDetail = (transaction) => {
  selectedTransaction.value = transaction;
  showDetailDialog.value = true;
};

// 加载交易数据
const loadTransactions = async () => {
  const userId = currentUserId || 3; // 如果没有登录用户，使用默认用户ID 3
  console.log('加载用户交易记录，用户ID:', userId);

  loading.value = true;
  try {
    const response = await transactionApi.getTransactionsByPage({
      userId: userId
    });

    if (response && response.code === 0) {
      const rawTransactions = response.data || [];
      console.log(`成功加载 ${rawTransactions.length} 条交易记录`);

      // 转换数据格式
      transactions.value = rawTransactions.map(transaction => ({
        id: transaction.txnId,
        title: getTransactionTitle(transaction),
        description: getTransactionDescription(transaction),
        type: getTransactionType(transaction),
        category: getTransactionCategory(transaction),
        amount: parseFloat(transaction.amount),
        time: formatTransactionTime(transaction.createdAt),
        status: getTransactionStatus(transaction.status),
        transactionId: transaction.txnNo,
        remark: transaction.remark || '',
        counterparty: transaction.counterparty || '',
        fromAccount: getFromAccount(transaction),
        toAccount: getToAccount(transaction)
      }));

      ElMessage.success(`交易记录加载成功 (${transactions.value.length}条)`);
    } else {
      console.warn('获取交易记录失败:', response?.msg);
      ElMessage.error(response?.msg || '获取交易记录失败');
    }
  } catch (error) {
    console.error('加载交易记录失败:', error);
    ElMessage.error('网络连接失败，无法获取交易记录');
  } finally {
    loading.value = false;
  }
};

// 数据转换辅助方法
const getTransactionTitle = (transaction) => {
  const typeMap = {
    1: '充值',
    2: '转账',
    3: '收款',
    4: '支付',
    5: '提现'
  };
  return typeMap[transaction.type] || '交易';
};

const getTransactionDescription = (transaction) => {
  const typeMap = {
    1: '钱包充值',
    2: '资金转账',
    3: '收到转账',
    4: '消费支付',
    5: '余额提现'
  };
  return typeMap[transaction.type] || '交易记录';
};

const getTransactionType = (transaction) => {
  // 1-充值, 3-收款 为收入，2-转账, 4-支付, 5-提现 为支出
  return [1, 3].includes(transaction.type) ? 'income' : 'expense';
};

const getTransactionCategory = (transaction) => {
  const categoryMap = {
    1: 'recharge',
    2: 'transfer',
    3: 'receive',
    4: 'payment',
    5: 'withdraw'
  };
  return categoryMap[transaction.type] || 'payment';
};

const getTransactionStatus = (status) => {
  const statusMap = {
    0: '处理中',
    1: '成功',
    2: '失败',
    3: '已取消'
  };
  return statusMap[status] || '未知';
};

const getFromAccount = (transaction) => {
  if (transaction.paymentMethod === 1) {
    return '钱包余额';
  } else if (transaction.paymentMethod === 2) {
    return '银行卡';
  }
  return '未知账户';
};

const getToAccount = (transaction) => {
  if (transaction.type === 1) { // 充值
    return '钱包余额';
  } else if (transaction.type === 5) { // 提现
    return '银行卡';
  } else if (transaction.type === 4) { // 支付
    return '商户账户';
  }
  return '对方账户';
};

const formatTransactionTime = (timeStr) => {
  if (!timeStr) return '';
  const date = new Date(timeStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 删除交易记录
const deleteTransaction = async (transaction) => {
  if (!transaction) return;

  try {
    await ElMessageBox.confirm(
      `确定要删除这条交易记录吗？\n交易金额：¥${transaction.amount}\n交易时间：${transaction.time}`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    );

    // 设置删除状态
    transaction.deleting = true;

    // 调用删除API
    const response = await transactionApi.deleteTransaction(transaction.id);

    if (response && response.code === 0) {
      ElMessage.success('交易记录删除成功');

      // 从本地数组中移除
      const index = transactions.value.findIndex(t => t.id === transaction.id);
      if (index > -1) {
        transactions.value.splice(index, 1);
      }

      // 如果是在详情对话框中删除，关闭对话框
      if (showDetailDialog.value && selectedTransaction.value?.id === transaction.id) {
        showDetailDialog.value = false;
      }

    } else {
      throw new Error(response?.msg || '删除失败');
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除交易记录失败:', error);
      ElMessage.error(error.message || '删除交易记录失败');
    }
  } finally {
    transaction.deleting = false;
  }
};

const refreshData = async () => {
  await loadTransactions();
  ElMessage.success('数据已刷新');
};

const exportRecords = () => {
  ElMessage.info('导出功能开发中...');
  // 这里可以添加实际的导出逻辑
};

// 生命周期
onMounted(() => {
  loadTransactions();
});
</script>

<style scoped>
.transaction-container {
  padding: 20px;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
}

.header-left h2 {
  font-size: 28px;
  color: #2c3e50;
  margin: 0 0 5px 0;
}

.header-left p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

/* 统计卡片 */
.transaction-stats {
  margin-bottom: 30px;
}

.stat-card {
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-card.income {
  background: linear-gradient(135deg, #56ab2f, #a8e6cf);
  color: white;
}

.stat-card.expense {
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
  color: white;
}

.stat-card.balance {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.stat-card.count {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  font-size: 48px;
  margin-right: 20px;
  opacity: 0.9;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 筛选卡片 */
.filter-card {
  margin-bottom: 30px;
  border-radius: 12px;
  border: none;
}

.filter-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.filter-tabs .el-button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.search-box {
  min-width: 250px;
}

/* 交易列表 */
.transaction-list {
  border-radius: 12px;
  border: none;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.transactions {
  padding: 0;
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: all 0.2s ease;
}

.transaction-item:hover {
  background: #f8f9fa;
  margin: 0 -20px;
  padding: 20px;
  border-radius: 8px;
}

.transaction-item:hover .transaction-actions {
  opacity: 1;
  visibility: visible;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-actions {
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  margin-left: 15px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transaction-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 15px;
}

/* 不同交易类型的图标样式 */
.transaction-icon.recharge {
  background: linear-gradient(135deg, #56ab2f, #a8e6cf);
  color: white;
}

.transaction-icon.withdraw {
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
  color: white;
}

.transaction-icon.transfer {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.transaction-icon.receive {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
}

.transaction-icon.payment {
  background: linear-gradient(135deg, #fa709a, #fee140);
  color: white;
}

.transaction-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.transaction-main h4 {
  font-size: 16px;
  color: #2c3e50;
  margin: 0;
  font-weight: 600;
}

.transaction-desc {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.transaction-extra {
  display: flex;
  flex-direction: column;
  gap: 3px;
  margin-top: 5px;
}

.transaction-id {
  font-size: 11px;
  color: #bdc3c7;
  font-family: 'Courier New', monospace;
}

.counterparty {
  font-size: 12px;
  color: #95a5a6;
}

.transaction-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.transaction-time {
  font-size: 12px;
  color: #95a5a6;
}

.transaction-amount {
  min-width: 140px;
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 3px;
}

.amount-value {
  font-size: 18px;
  font-weight: 700;
}

.amount-desc {
  font-size: 11px;
  color: #95a5a6;
}

.transaction-amount.income .amount-value {
  color: #27ae60;
}

.transaction-amount.expense .amount-value {
  color: #e74c3c;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}

/* 分页 */
.pagination {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

/* 交易详情对话框 */
.transaction-detail {
  padding: 10px 0;
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f1f3f4;
}

.detail-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-right: 20px;
}

.detail-icon.income {
  background: linear-gradient(135deg, #56ab2f, #a8e6cf);
  color: white;
}

.detail-icon.expense {
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
  color: white;
}

.detail-title h3 {
  font-size: 20px;
  color: #2c3e50;
  margin: 0 0 5px 0;
}

.detail-title p {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.detail-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.info-row label {
  font-weight: 600;
  color: #2c3e50;
  min-width: 100px;
}

.info-row span {
  color: #7f8c8d;
  text-align: right;
}

.info-row .amount {
  font-size: 18px;
  font-weight: 700;
}

.info-row .amount.income {
  color: #27ae60;
}

.info-row .amount.expense {
  color: #e74c3c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: space-between;
  }

  .filter-content {
    flex-direction: column;
    gap: 15px;
  }

  .filter-tabs .el-button-group {
    justify-content: center;
  }

  .search-box {
    min-width: auto;
    width: 100%;
  }

  .transaction-stats .el-col {
    margin-bottom: 15px;
  }

  .transaction-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .transaction-amount {
    align-self: flex-end;
  }

  .detail-header {
    flex-direction: column;
    text-align: center;
  }

  .detail-icon {
    margin: 0 auto 15px;
  }
}
</style>