import { BankCard, BankCardType, BankCardStatus } from '../common/types/index';

@Component
export struct BankCardSelector {
  @State cardList: BankCard[] = [];
  @State selectedCardId: number = 0;
  @State isVisible: boolean = false;
  
  // 回调函数
  onCardSelected?: (card: BankCard) => void;
  onCancel?: () => void;

  build() {
    if (this.isVisible) {
      Stack({ alignContent: Alignment.Bottom }) {
        // 遮罩层
        Column()
          .width('100%')
          .height('100%')
          .backgroundColor('rgba(0,0,0,0.5)')
          .onClick(() => {
            this.hide();
          })

        // 银行卡选择面板
        Column() {
          // 顶部标题栏
          Row() {
            Button('取消')
              .fontSize(16)
              .fontColor('#666666')
              .backgroundColor(Color.Transparent)
              .onClick(() => {
                this.hide();
              })

            Text('选择银行卡')
              .fontSize(18)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .layoutWeight(1)
              .textAlign(TextAlign.Center)

            Button('确定')
              .fontSize(16)
              .fontColor('#1976D2')
              .backgroundColor(Color.Transparent)
              .enabled(this.selectedCardId > 0)
              .opacity(this.selectedCardId > 0 ? 1 : 0.5)
              .onClick(() => {
                this.confirmSelection();
              })
          }
          .width('100%')
          .height(56)
          .padding({ left: 16, right: 16 })
          .border({
            width: { bottom: 1 },
            color: '#E0E0E0'
          })

          // 银行卡列表
          if (this.cardList.length > 0) {
            List() {
              ForEach(this.cardList, (card: BankCard) => {
                ListItem() {
                  this.BankCardItem(card)
                }
              })
            }
            .height(300)
            .scrollBar(BarState.Off)
            .padding({ left: 16, right: 16, top: 16, bottom: 16 })
          } else {
            Column() {
              Text('暂无可用银行卡')
                .fontSize(16)
                .fontColor('#999999')
                .margin({ top: 40, bottom: 40 })
            }
            .width('100%')
            .justifyContent(FlexAlign.Center)
          }
        }
        .width('100%')
        .backgroundColor('#FFFFFF')
        .borderRadius({ topLeft: 16, topRight: 16 })
        .animation({
          duration: 300,
          curve: Curve.EaseInOut
        })
      }
      .width('100%')
      .height('100%')
      .position({ x: 0, y: 0 })
      .zIndex(1000)
    }
  }

  @Builder
  BankCardItem(card: BankCard) {
    Row() {
      // 银行卡缩略图
      Column() {
        Text(card.bankName.substring(0, 2))
          .fontSize(16)
          .fontColor('#FFFFFF')
          .fontWeight(FontWeight.Bold)
      }
      .width(48)
      .height(32)
      .borderRadius(4)
      .justifyContent(FlexAlign.Center)
      .linearGradient({
        direction: GradientDirection.Right,
        colors: this.getBankCardGradient(card.bankName)
      })
      .margin({ right: 12 })

      // 银行卡信息
      Column() {
        Row() {
          Text(card.bankName)
            .fontSize(16)
            .fontColor('#333333')
            .fontWeight(FontWeight.Medium)
            .layoutWeight(1)

          if (card.isDefault) {
            Text('默认')
              .fontSize(10)
              .fontColor('#FFFFFF')
              .backgroundColor('#FF9800')
              .borderRadius(8)
              .padding({ left: 6, right: 6, top: 2, bottom: 2 })
          }
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)

        Text(`${this.getCardTypeText(card.cardType)} • ${this.formatCardNumber(card.cardNo)}`)
          .fontSize(14)
          .fontColor('#666666')
          .margin({ top: 4 })
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)

      // 选择状态
      Radio({ value: card.cardId.toString(), group: 'bankCardSelect' })
        .checked(this.selectedCardId === card.cardId)
        .width(20)
        .height(20)
        .onChange((isChecked: boolean) => {
          if (isChecked) {
            this.selectedCardId = card.cardId;
          }
        })
    }
    .width('100%')
    .padding(16)
    .backgroundColor(this.selectedCardId === card.cardId ? '#E8F5E8' : '#FFFFFF')
    .borderRadius(8)
    .border({
      width: this.selectedCardId === card.cardId ? 2 : 1,
      color: this.selectedCardId === card.cardId ? '#4CAF50' : '#E0E0E0'
    })
    .margin({ bottom: 12 })
    .onClick(() => {
      this.selectedCardId = card.cardId;
    })
  }

  // 公共方法
  show(cardList: BankCard[], selectedCardId?: number) {
    this.cardList = cardList;
    this.selectedCardId = selectedCardId || 0;
    this.isVisible = true;
  }

  hide() {
    this.isVisible = false;
    if (this.onCancel) {
      this.onCancel();
    }
  }

  confirmSelection() {
    const selectedCard = this.cardList.find(card => card.cardId === this.selectedCardId);
    if (selectedCard && this.onCardSelected) {
      this.onCardSelected(selectedCard);
    }
    this.hide();
  }

  // 辅助方法
  private getCardTypeText(cardType: BankCardType): string {
    switch (cardType) {
      case BankCardType.CREDIT:
        return '信用卡';
      case BankCardType.DEBIT:
        return '储蓄卡';
      default:
        return '储蓄卡';
    }
  }

  private formatCardNumber(cardNo: string): string {
    if (!cardNo) return '';
    return `**** **** **** ${cardNo.slice(-4)}`;
  }

  private getBankCardGradient(bankName: string): Array<[string, number]> {
    const gradients: Record<string, Array<[string, number]>> = {
      '中国工商银行': [['#C41E3A', 0], ['#8B0000', 1]],
      '中国建设银行': [['#003DA5', 0], ['#001F5C', 1]],
      '中国农业银行': [['#00A651', 0], ['#006B35', 1]],
      '中国银行': [['#B8860B', 0], ['#8B6914', 1]],
      '招商银行': [['#DC143C', 0], ['#8B0000', 1]],
      '交通银行': [['#0066CC', 0], ['#003D7A', 1]],
      '中信银行': [['#FF6B35', 0], ['#CC4125', 1]],
      '光大银行': [['#9932CC', 0], ['#6A1B9A', 1]],
      '华夏银行': [['#FF1744', 0], ['#C62828', 1]],
      '民生银行': [['#00BCD4', 0], ['#0097A7', 1]],
      '广发银行': [['#FF5722', 0], ['#D84315', 1]],
      '平安银行': [['#FF9800', 0], ['#F57C00', 1]]
    };

    return gradients[bankName] || [['#1976D2', 0], ['#0D47A1', 1]];
  }
}
