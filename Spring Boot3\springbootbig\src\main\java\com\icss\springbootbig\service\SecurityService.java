package com.icss.springbootbig.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icss.springbootbig.entity.User;
import com.icss.springbootbig.entity.Transaction;
import com.icss.springbootbig.exception.ApiException;
import com.icss.springbootbig.mapper.UserMapper;
import com.icss.springbootbig.mapper.TransactionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
public class SecurityService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private TransactionMapper transactionMapper;

    /**
     * 修改登录密码
     */
    @Transactional
    public void changeLoginPassword(Integer userId, String oldPassword, String newPassword) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }
        
        // 验证原密码
        if (!oldPassword.equals(user.getPassword())) {
            throw new ApiException("原密码错误");
        }
        
        // 验证新密码强度
        if (!isPasswordStrong(newPassword)) {
            throw new ApiException("新密码强度不够，请包含大小写字母、数字和特殊字符，长度至少8位");
        }
        
        // 更新密码
        user.setPassword(newPassword);
        user.setUpdatedAt(new Date());
        userMapper.updateById(user);
    }

    /**
     * 设置支付密码 - 已废弃，请使用PayPasswordService
     */
    @Deprecated
    @Transactional
    public void setPayPassword(Integer userId, String payPassword, String loginPassword) {
        // 这个方法已经废弃，支付密码现在存储在银行卡表中
        // 请使用 PayPasswordService.setDefaultCardPayPassword 方法
        throw new ApiException("此方法已废弃，请使用PayPasswordService设置支付密码");
    }

    /**
     * 修改支付密码 - 已废弃，请使用PayPasswordService
     */
    @Deprecated
    @Transactional
    public void changePayPassword(Integer userId, String oldPayPassword, String newPayPassword) {
        // 这个方法已经废弃，支付密码现在存储在银行卡表中
        // 请使用 PayPasswordService.changeDefaultCardPayPassword 方法
        throw new ApiException("此方法已废弃，请使用PayPasswordService修改支付密码");
    }

    /**
     * 重置支付密码（通过登录密码）
     */
    @Transactional
    public void resetPayPassword(Integer userId, String loginPassword, String newPayPassword) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }
        
        // 验证登录密码
        if (!loginPassword.equals(user.getPassword())) {
            throw new ApiException("登录密码错误");
        }
        
        // 验证新支付密码格式
        if (!isValidPayPassword(newPayPassword)) {
            throw new ApiException("新支付密码必须是6位数字");
        }
        
        // 这个方法已经废弃，支付密码现在存储在银行卡表中
        // 请使用 PayPasswordService.changeDefaultCardPayPassword 方法
        throw new ApiException("此方法已废弃，请使用PayPasswordService修改支付密码");
    }

    /**
     * 获取安全设置信息
     */
    public Map<String, Object> getSecuritySettings(Integer userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 检查默认银行卡的支付密码设置状态
        boolean hasPayPassword = false;
        try {
            // 这里可以调用PayPasswordService来检查支付密码状态
            // 暂时设为false，需要集成PayPasswordService
            hasPayPassword = false;
        } catch (Exception e) {
            hasPayPassword = false;
        }

        Map<String, Object> settings = new HashMap<>();
        settings.put("hasLoginPassword", user.getPassword() != null && !user.getPassword().isEmpty());
        settings.put("hasPayPassword", hasPayPassword);
        settings.put("fingerprintEnabled", false); // 用户表中没有这个字段
        settings.put("lastLoginTime", user.getLastLogin());
        settings.put("loginAttempts", 0); // 用户表中没有这个字段

        return settings;
    }

    /**
     * 启用/禁用指纹登录
     */
    @Transactional
    public void setFingerprintEnabled(Integer userId, Boolean enabled) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }
        
        // 用户表中没有fingerprintEnabled字段，这个功能暂时不可用
        throw new ApiException("指纹登录功能暂时不可用");
    }

    /**
     * 获取登录设备记录（模拟）
     */
    public List<Map<String, Object>> getLoginDevices(Integer userId) {
        // 这里应该从实际的设备记录表中查询
        // 为了演示，返回模拟数据
        List<Map<String, Object>> devices = new ArrayList<>();
        
        Map<String, Object> device1 = new HashMap<>();
        device1.put("deviceId", "DEVICE001");
        device1.put("deviceName", "iPhone 13");
        device1.put("deviceType", "mobile");
        device1.put("lastLoginTime", new Date());
        device1.put("location", "北京市");
        device1.put("isCurrent", true);
        devices.add(device1);
        
        Map<String, Object> device2 = new HashMap<>();
        device2.put("deviceId", "DEVICE002");
        device2.put("deviceName", "Chrome浏览器");
        device2.put("deviceType", "web");
        device2.put("lastLoginTime", new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000));
        device2.put("location", "上海市");
        device2.put("isCurrent", false);
        devices.add(device2);
        
        return devices;
    }

    /**
     * 获取风险提醒
     */
    public List<Map<String, Object>> getRiskAlerts(Integer userId) {
        List<Map<String, Object>> alerts = new ArrayList<>();
        
        // 检查异常交易
        List<Transaction> recentTransactions = transactionMapper.selectList(
            new QueryWrapper<Transaction>()
                .eq("user_id", userId)
                .ge("created_at", new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000))
                .orderByDesc("created_at")
        );
        
        // 检查大额交易
        for (Transaction transaction : recentTransactions) {
            if (transaction.getAmount().compareTo(new BigDecimal("10000")) > 0) {
                Map<String, Object> alert = new HashMap<>();
                alert.put("type", "large_transaction");
                alert.put("message", "检测到大额交易：" + transaction.getAmount() + "元");
                alert.put("time", transaction.getCreatedAt());
                alert.put("level", "medium");
                alerts.add(alert);
            }
        }
        
        // 检查频繁交易
        if (recentTransactions.size() > 10) {
            Map<String, Object> alert = new HashMap<>();
            alert.put("type", "frequent_transaction");
            alert.put("message", "24小时内交易次数较多，请注意账户安全");
            alert.put("time", new Date());
            alert.put("level", "low");
            alerts.add(alert);
        }
        
        // 检查登录尝试次数（用户表中没有loginAttempts字段，暂时跳过）
        // User user = userMapper.selectById(userId);
        // if (user.getLoginAttempts() > 3) {
        //     Map<String, Object> alert = new HashMap<>();
        //     alert.put("type", "login_attempts");
        //     alert.put("message", "登录失败次数较多，建议修改密码");
        //     alert.put("time", new Date());
        //     alert.put("level", "high");
        //     alerts.add(alert);
        // }
        
        return alerts;
    }

    /**
     * 验证密码强度
     */
    private boolean isPasswordStrong(String password) {
        if (password == null || password.length() < 8) {
            return false;
        }
        
        boolean hasUpper = password.chars().anyMatch(Character::isUpperCase);
        boolean hasLower = password.chars().anyMatch(Character::isLowerCase);
        boolean hasDigit = password.chars().anyMatch(Character::isDigit);
        boolean hasSpecial = password.chars().anyMatch(ch -> "!@#$%^&*()_+-=[]{}|;:,.<>?".indexOf(ch) >= 0);
        
        return hasUpper && hasLower && hasDigit && hasSpecial;
    }

    /**
     * 验证支付密码格式
     */
    private boolean isValidPayPassword(String payPassword) {
        return payPassword != null && payPassword.matches("\\d{6}");
    }

    /**
     * 账户安全检查
     */
    public Map<String, Object> securityCheck(Integer userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }
        
        Map<String, Object> checkResult = new HashMap<>();
        int securityScore = 100;
        List<String> suggestions = new ArrayList<>();
        
        // 检查登录密码强度
        if (user.getPassword() == null || !isPasswordStrong(user.getPassword())) {
            securityScore -= 20;
            suggestions.add("建议设置更强的登录密码");
        }
        
        // 检查是否设置支付密码（现在存储在银行卡表中，暂时跳过）
        // if (user.getPayPassword() == null || user.getPayPassword().isEmpty()) {
        //     securityScore -= 30;
        //     suggestions.add("建议设置支付密码");
        // }

        // 检查是否启用指纹登录（用户表中没有此字段，暂时跳过）
        // if (user.getFingerprintEnabled() != 1) {
        //     securityScore -= 10;
        //     suggestions.add("建议启用指纹登录");
        // }

        // 检查登录失败次数（用户表中没有此字段，暂时跳过）
        // if (user.getLoginAttempts() > 3) {
        //     securityScore -= 20;
        //     suggestions.add("登录失败次数较多，建议修改密码");
        // }
        
        checkResult.put("securityScore", Math.max(securityScore, 0));
        checkResult.put("suggestions", suggestions);
        checkResult.put("checkTime", new Date());
        
        return checkResult;
    }
}
