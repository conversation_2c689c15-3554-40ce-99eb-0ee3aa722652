package com.icss.springbootbig.service;

/**
 * 短信服务接口
 */
public interface SmsService {
    
    /**
     * 发送验证码短信
     * @param phone 手机号
     * @param code 验证码
     * @param type 验证码类型 1-登录 2-操作
     * @return 是否发送成功
     */
    boolean sendVerificationCode(String phone, String code, Integer type);
    
    /**
     * 发送通知短信
     * @param phone 手机号
     * @param message 消息内容
     * @return 是否发送成功
     */
    boolean sendNotification(String phone, String message);
}
