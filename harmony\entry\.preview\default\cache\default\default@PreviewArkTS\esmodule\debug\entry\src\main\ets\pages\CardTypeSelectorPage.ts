if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface CardTypeSelectorPage_Params {
    selectedType?: string;
    cardTypeOptions?: CardTypeOption[];
}
import router from "@ohos:router";
import { tempDataManager, TempDataKeys } from "@normalized:N&&&entry/src/main/ets/common/storage/TempDataManager&";
interface CardTypeOption {
    type: string;
    description: string;
    features: string[];
}
class CardTypeSelectorPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__selectedType = new ObservedPropertySimplePU('', this, "selectedType");
        this.cardTypeOptions = [
            {
                type: '储蓄卡',
                description: '借记卡，先存款后消费',
                features: ['无透支功能', '资金安全', '适合日常消费', '无年费']
            },
            {
                type: '信用卡',
                description: '贷记卡，先消费后还款',
                features: ['可透支消费', '免息期还款', '积分奖励', '需要审核']
            }
        ];
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: CardTypeSelectorPage_Params) {
        if (params.selectedType !== undefined) {
            this.selectedType = params.selectedType;
        }
        if (params.cardTypeOptions !== undefined) {
            this.cardTypeOptions = params.cardTypeOptions;
        }
    }
    updateStateVars(params: CardTypeSelectorPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__selectedType.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__selectedType.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __selectedType: ObservedPropertySimplePU<string>;
    get selectedType() {
        return this.__selectedType.get();
    }
    set selectedType(newValue: string) {
        this.__selectedType.set(newValue);
    }
    private cardTypeOptions: CardTypeOption[];
    aboutToAppear() {
        // 获取传入的参数
        const params = router.getParams() as Record<string, string>;
        this.selectedType = params?.selectedType || '';
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(35:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(37:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(38:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择卡片类型');
            Text.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(46:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(52:9)", "entry");
            Text.width(60);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 卡片类型列表
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(61:7)", "entry");
            // 卡片类型列表
            Column.width('100%');
            // 卡片类型列表
            Column.padding(20);
            // 卡片类型列表
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const option = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(63:11)", "entry");
                    Column.width('100%');
                    Column.backgroundColor(this.selectedType === option.type ? '#F0F8FF' : '#FFFFFF');
                    Column.borderRadius(12);
                    Column.border({
                        width: this.selectedType === option.type ? 2 : 1,
                        color: this.selectedType === option.type ? '#1976D2' : '#E0E0E0'
                    });
                    Column.margin({ bottom: 16 });
                    Column.onClick(() => {
                        // 保存选中的卡片类型到临时数据管理器
                        tempDataManager.setData(TempDataKeys.SELECTED_CARD_TYPE, option.type);
                        // 返回上一页
                        router.back();
                    });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Row.create();
                    Row.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(64:13)", "entry");
                    Row.width('100%');
                    Row.padding({ left: 20, right: 20, top: 16, bottom: 12 });
                }, Row);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(65:15)", "entry");
                    Column.layoutWeight(1);
                    Column.alignItems(HorizontalAlign.Start);
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(option.type);
                    Text.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(66:17)", "entry");
                    Text.fontSize(18);
                    Text.fontColor('#333333');
                    Text.fontWeight(FontWeight.Medium);
                    Text.alignSelf(ItemAlign.Start);
                }, Text);
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(option.description);
                    Text.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(72:17)", "entry");
                    Text.fontSize(14);
                    Text.fontColor('#666666');
                    Text.alignSelf(ItemAlign.Start);
                    Text.margin({ top: 4 });
                }, Text);
                Text.pop();
                Column.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    If.create();
                    if (this.selectedType === option.type) {
                        this.ifElseBranchUpdateFunction(0, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Text.create('✓');
                                Text.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(82:17)", "entry");
                                Text.fontSize(24);
                                Text.fontColor('#1976D2');
                                Text.fontWeight(FontWeight.Bold);
                            }, Text);
                            Text.pop();
                        });
                    }
                    else {
                        this.ifElseBranchUpdateFunction(1, () => {
                        });
                    }
                }, If);
                If.pop();
                Row.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    // 特性列表
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(92:13)", "entry");
                    // 特性列表
                    Column.width('100%');
                    // 特性列表
                    Column.padding({ left: 32, right: 20, bottom: 16 });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    ForEach.create();
                    const forEachItemGenFunction = _item => {
                        const feature = _item;
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Row.create();
                            Row.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(94:17)", "entry");
                            Row.width('100%');
                            Row.margin({ bottom: 4 });
                        }, Row);
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create('•');
                            Text.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(95:19)", "entry");
                            Text.fontSize(12);
                            Text.fontColor('#999999');
                            Text.margin({ right: 8 });
                        }, Text);
                        Text.pop();
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            Text.create(feature);
                            Text.debugLine("entry/src/main/ets/pages/CardTypeSelectorPage.ets(100:19)", "entry");
                            Text.fontSize(12);
                            Text.fontColor('#999999');
                            Text.layoutWeight(1);
                        }, Text);
                        Text.pop();
                        Row.pop();
                    };
                    this.forEachUpdateFunction(elmtId, option.features, forEachItemGenFunction);
                }, ForEach);
                ForEach.pop();
                // 特性列表
                Column.pop();
                Column.pop();
            };
            this.forEachUpdateFunction(elmtId, this.cardTypeOptions, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        // 卡片类型列表
        Column.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "CardTypeSelectorPage";
    }
}
registerNamedRoute(() => new CardTypeSelectorPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/CardTypeSelectorPage", pageFullPath: "entry/src/main/ets/pages/CardTypeSelectorPage", integratedHsp: "false", moduleType: "followWithHap" });
