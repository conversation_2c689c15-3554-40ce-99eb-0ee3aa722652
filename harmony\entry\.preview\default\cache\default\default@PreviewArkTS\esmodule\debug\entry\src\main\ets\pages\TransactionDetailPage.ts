if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface TransactionDetailPage_Params {
    transaction?: Transaction | null;
    isLoading?: boolean;
    transactionId?: number;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { TransactionApi } from "@normalized:N&&&entry/src/main/ets/api/TransactionApi&";
import type { Transaction } from '../common/types/index';
class TransactionDetailPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__transaction = new ObservedPropertyObjectPU(null, this, "transaction");
        this.__isLoading = new ObservedPropertySimplePU(true, this, "isLoading");
        this.transactionId = 0;
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: TransactionDetailPage_Params) {
        if (params.transaction !== undefined) {
            this.transaction = params.transaction;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.transactionId !== undefined) {
            this.transactionId = params.transactionId;
        }
    }
    updateStateVars(params: TransactionDetailPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__transaction.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__transaction.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __transaction: ObservedPropertyObjectPU<Transaction | null>;
    get transaction() {
        return this.__transaction.get();
    }
    set transaction(newValue: Transaction | null) {
        this.__transaction.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private transactionId: number;
    aboutToAppear() {
        const params = router.getParams() as Record<string, Object>;
        if (params && params.transactionId) {
            this.transactionId = params.transactionId as number;
            this.loadTransactionDetail();
        }
        else {
            promptAction.showToast({ message: '参数错误' });
            router.back();
        }
    }
    async loadTransactionDetail() {
        try {
            this.transaction = await TransactionApi.getTransactionDetail(this.transactionId);
        }
        catch (error) {
            console.error('获取交易详情失败:', error);
            promptAction.showToast({ message: '获取交易详情失败' });
            router.back();
        }
        finally {
            this.isLoading = false;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(37:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(39:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('返回');
            Button.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(40:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易详情');
            Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(48:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(54:9)", "entry");
            Text.width(60);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(63:9)", "entry");
                        Column.width('100%');
                        Column.layoutWeight(1);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(64:11)", "entry");
                        LoadingProgress.width(40);
                        LoadingProgress.height(40);
                        LoadingProgress.color('#1976D2');
                    }, LoadingProgress);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('加载中...');
                        Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(69:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ top: 16 });
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
            else if (this.transaction) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Scroll.create();
                        Scroll.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(79:9)", "entry");
                        Scroll.layoutWeight(1);
                        Scroll.backgroundColor('#F5F5F5');
                    }, Scroll);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(80:11)", "entry");
                        Column.padding({ left: 16, right: 16, bottom: 20 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 交易状态卡片
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(82:13)", "entry");
                        // 交易状态卡片
                        Column.width('100%');
                        // 交易状态卡片
                        Column.padding(24);
                        // 交易状态卡片
                        Column.margin({ top: 16 });
                        // 交易状态卡片
                        Column.borderRadius(12);
                        // 交易状态卡片
                        Column.backgroundColor('#FFFFFF');
                        // 交易状态卡片
                        Column.alignItems(HorizontalAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.getTransactionIcon(this.transaction.transactionType));
                        Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(83:15)", "entry");
                        Text.fontSize(48);
                        Text.margin({ bottom: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.transaction.transactionType);
                        Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(87:15)", "entry");
                        Text.fontSize(20);
                        Text.fontColor('#333333');
                        Text.fontWeight(FontWeight.Medium);
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.formatAmount(ObservedObject.GetRawObject(this.transaction)));
                        Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(93:15)", "entry");
                        Text.fontSize(32);
                        Text.fontColor(this.getAmountColor(ObservedObject.GetRawObject(this.transaction)));
                        Text.fontWeight(FontWeight.Bold);
                        Text.margin({ bottom: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(99:15)", "entry");
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.transaction.status);
                        Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(100:17)", "entry");
                        Text.fontSize(16);
                        Text.fontColor(this.getStatusColor(this.transaction.status));
                        Text.fontWeight(FontWeight.Medium);
                        Text.padding({ left: 12, right: 12, top: 6, bottom: 6 });
                        Text.backgroundColor(this.getStatusBackgroundColor(this.transaction.status));
                        Text.borderRadius(12);
                    }, Text);
                    Text.pop();
                    Row.pop();
                    // 交易状态卡片
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 交易详情
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(117:13)", "entry");
                        // 交易详情
                        Column.width('100%');
                        // 交易详情
                        Column.padding(20);
                        // 交易详情
                        Column.margin({ top: 16 });
                        // 交易详情
                        Column.borderRadius(12);
                        // 交易详情
                        Column.backgroundColor('#FFFFFF');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('交易详情');
                        Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(118:15)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#333333');
                        Text.fontWeight(FontWeight.Medium);
                        Text.alignSelf(ItemAlign.Start);
                        Text.margin({ bottom: 16 });
                    }, Text);
                    Text.pop();
                    this.DetailItem.bind(this)('交易单号', this.transaction.transactionNo);
                    this.DetailItem.bind(this)('交易时间', this.formatDateTime(this.transaction.createTime));
                    this.DetailItem.bind(this)('交易金额', `¥${this.transaction.amount.toFixed(2)}`);
                    this.DetailItem.bind(this)('交易类型', this.transaction.transactionType);
                    this.DetailItem.bind(this)('支付方式', this.transaction.paymentMethod);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.transaction.description) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.DetailItem.bind(this)('交易说明', this.transaction.description);
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.transaction.cardNo) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.DetailItem.bind(this)('相关银行卡', this.maskBankCard(this.transaction.cardNo));
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    // 交易详情
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 操作按钮
                        if (this.transaction.status === '成功') {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Column.create();
                                    Column.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(147:15)", "entry");
                                    Column.width('100%');
                                    Column.padding(20);
                                    Column.margin({ top: 16 });
                                    Column.borderRadius(12);
                                    Column.backgroundColor('#FFFFFF');
                                }, Column);
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Button.createWithLabel('联系客服');
                                    Button.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(148:17)", "entry");
                                    Button.width('100%');
                                    Button.height(48);
                                    Button.fontSize(16);
                                    Button.fontColor('#1976D2');
                                    Button.backgroundColor('#E3F2FD');
                                    Button.borderRadius(8);
                                    Button.margin({ bottom: 12 });
                                    Button.onClick(() => {
                                        promptAction.showToast({ message: '客服功能开发中' });
                                    });
                                }, Button);
                                Button.pop();
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    Button.createWithLabel('举报问题');
                                    Button.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(160:17)", "entry");
                                    Button.width('100%');
                                    Button.height(48);
                                    Button.fontSize(16);
                                    Button.fontColor('#666666');
                                    Button.backgroundColor('#F8F9FA');
                                    Button.borderRadius(8);
                                    Button.onClick(() => {
                                        promptAction.showToast({ message: '举报功能开发中' });
                                    });
                                }, Button);
                                Button.pop();
                                Column.pop();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    Column.pop();
                    Scroll.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    DetailItem(label: string, value: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(191:5)", "entry");
            Row.width('100%');
            Row.height(44);
            Row.justifyContent(FlexAlign.SpaceBetween);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(192:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(value);
            Text.debugLine("entry/src/main/ets/pages/TransactionDetailPage.ets(197:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.End);
        }, Text);
        Text.pop();
        Row.pop();
    }
    getTransactionIcon(type: string): string {
        switch (type) {
            case '充值': return '⬇️';
            case '提现': return '⬆️';
            case '转账': return '💸';
            case '支付': return '💳';
            case '退款': return '↩️';
            default: return '📄';
        }
    }
    formatAmount(transaction: Transaction): string {
        const prefix = transaction.transactionType === '充值' || transaction.transactionType === '退款' ? '+' : '-';
        return `${prefix}¥${transaction.amount.toFixed(2)}`;
    }
    getAmountColor(transaction: Transaction): string {
        return transaction.transactionType === '充值' || transaction.transactionType === '退款' ? '#4CAF50' : '#333333';
    }
    getStatusColor(status: string): string {
        switch (status) {
            case '成功': return '#4CAF50';
            case '失败': return '#F44336';
            case '处理中': return '#FF9800';
            default: return '#666666';
        }
    }
    getStatusBackgroundColor(status: string): string {
        switch (status) {
            case '成功': return '#E8F5E8';
            case '失败': return '#FFEBEE';
            case '处理中': return '#FFF3E0';
            default: return '#F5F5F5';
        }
    }
    formatDateTime(dateTime: string): string {
        const date = new Date(dateTime);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
    }
    maskBankCard(cardNo: string): string {
        if (!cardNo || cardNo.length < 8) {
            return cardNo;
        }
        // 显示前4位和后4位
        const start = cardNo.substring(0, 4);
        const end = cardNo.substring(cardNo.length - 4);
        const middle = '*'.repeat(cardNo.length - 8);
        return `${start}${middle}${end}`;
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "TransactionDetailPage";
    }
}
registerNamedRoute(() => new TransactionDetailPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/TransactionDetailPage", pageFullPath: "entry/src/main/ets/pages/TransactionDetailPage", integratedHsp: "false", moduleType: "followWithHap" });
