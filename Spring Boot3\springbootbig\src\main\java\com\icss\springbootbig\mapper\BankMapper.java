package com.icss.springbootbig.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icss.springbootbig.entity.Bank;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BankMapper extends BaseMapper<Bank> {
    
    @Select("SELECT * FROM banks WHERE bank_code = #{bankCode}")
    Bank selectByBankCode(String bankCode);
    
    @Select("SELECT * FROM banks WHERE status = 1 ORDER BY sort_order ASC, bank_name ASC")
    List<Bank> selectActiveBanks();
    
    @Select("SELECT * FROM banks ORDER BY sort_order ASC, bank_name ASC")
    List<Bank> selectAllBanks();
}
