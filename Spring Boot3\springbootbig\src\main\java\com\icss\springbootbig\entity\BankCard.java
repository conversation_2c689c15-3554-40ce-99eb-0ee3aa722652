package com.icss.springbootbig.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("bank_cards")
public class BankCard {
    @TableId(type = IdType.AUTO)
    private Integer cardId;

    @TableField("user_id")
    private Integer userId;

    @TableField("bank_name")
    private String bankName;

    @TableField("card_number")
    private String cardNumber;

    @TableField("card_type")
    private Integer cardType; // 1-借记卡 2-信用卡

    @TableField("card_holder")
    private String cardHolder;

    private BigDecimal balance; // 银行卡余额

    @TableField("expiry_date")
    private String expiryDate; // 信用卡专用

    private String cvv;       // 信用卡专用

    @TableField("pay_password")
    private String payPassword; // 支付密码

    // 银行卡查询密码现在使用用户登录密码，不再单独存储
    @TableField(exist = false)
    private String password;  // 银行卡查询密码（从users表获取）

    private String phone;

    @TableField("is_default")
    private Integer isDefault;

    private Integer status;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;
}