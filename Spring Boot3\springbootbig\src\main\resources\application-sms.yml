# 短信服务配置示例
sms:
  # 短信服务提供商 (mock/aliyun/tencent/huawei)
  provider: mock
  
  # 是否启用短信服务
  enabled: true
  
  # 访问密钥配置（生产环境请使用环境变量）
  access-key-id: ${SMS_ACCESS_KEY_ID:your-access-key-id}
  access-key-secret: ${SMS_ACCESS_KEY_SECRET:your-access-key-secret}
  
  # 短信签名
  sign-name: 电子钱包
  
  # 模板ID配置
  login-template-id: SMS_123456789  # 登录验证码模板
  operation-template-id: SMS_987654321  # 操作验证码模板
  
  # 发送限制配置
  rate-limit-seconds: 60  # 发送频率限制（秒）
  daily-limit: 10  # 每日发送次数限制
  
  # 验证码配置
  expire-minutes: 5  # 验证码有效期（分钟）
  code-length: 6  # 验证码长度

# 阿里云短信配置示例
#sms:
#  provider: aliyun
#  enabled: true
#  access-key-id: ${ALIYUN_ACCESS_KEY_ID}
#  access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET}
#  sign-name: 电子钱包
#  login-template-id: SMS_123456789
#  operation-template-id: SMS_987654321

# 腾讯云短信配置示例
#sms:
#  provider: tencent
#  enabled: true
#  access-key-id: ${TENCENT_SECRET_ID}
#  access-key-secret: ${TENCENT_SECRET_KEY}
#  sign-name: 电子钱包
#  login-template-id: 123456
#  operation-template-id: 654321
