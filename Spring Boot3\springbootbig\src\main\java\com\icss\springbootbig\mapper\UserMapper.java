package com.icss.springbootbig.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icss.springbootbig.entity.User;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface UserMapper extends BaseMapper<User> {
    @Select("SELECT * FROM users WHERE phone = #{phone}")
    User findByPhone(@Param("phone") String phone);
}