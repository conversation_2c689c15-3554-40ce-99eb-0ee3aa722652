package com.icss.springbootbig.controller;

import com.icss.springbootbig.result.R;
import com.icss.springbootbig.service.WalletService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;

@CrossOrigin(origins = {"http://localhost:5173", "http://127.0.0.1:5173"})
@RestController
@RequestMapping("/api/wallet")
public class WalletController {
    
    @Autowired
    private WalletService walletService;

    /**
     * 获取钱包余额信息
     */
    @GetMapping("/balance/{userId}")
    public R<Map<String, Object>> getWalletBalance(@PathVariable Integer userId) {
        try {
            Map<String, Object> balanceInfo = walletService.getWalletBalance(userId);
            return R.success("查询成功", balanceInfo);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 钱包充值
     */
    @PostMapping("/recharge")
    public R<String> recharge(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            BigDecimal amount = new BigDecimal(request.get("amount").toString());
            Integer cardId = (Integer) request.get("cardId");
            String payPassword = (String) request.get("payPassword");
            
            walletService.recharge(userId, amount, cardId, payPassword);
            return R.success("充值成功");
        } catch (Exception e) {
            return R.failure("充值失败: " + e.getMessage());
        }
    }

    /**
     * 钱包提现
     */
    @PostMapping("/withdraw")
    public R<String> withdraw(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            BigDecimal amount = new BigDecimal(request.get("amount").toString());
            Integer cardId = (Integer) request.get("cardId");
            String payPassword = (String) request.get("payPassword");
            
            walletService.withdraw(userId, amount, cardId, payPassword);
            return R.success("提现成功");
        } catch (Exception e) {
            return R.failure("提现失败: " + e.getMessage());
        }
    }

    /**
     * 设置钱包限额
     */
    @PutMapping("/limits/{userId}")
    public R<String> setWalletLimits(@PathVariable Integer userId, @RequestBody Map<String, Object> limits) {
        try {
            walletService.setWalletLimits(userId, limits);
            return R.success("限额设置成功");
        } catch (Exception e) {
            return R.failure("限额设置失败: " + e.getMessage());
        }
    }

    /**
     * 银行卡转账
     */
    @PostMapping("/bank-transfer")
    public R<String> bankTransfer(@RequestBody Map<String, Object> request) {
        try {
            Integer fromUserId = (Integer) request.get("fromUserId");
            Integer fromCardId = (Integer) request.get("fromCardId");
            String toPhone = (String) request.get("toPhone");
            Integer toCardId = (Integer) request.get("toCardId");
            BigDecimal amount = new BigDecimal(request.get("amount").toString());
            String payPassword = (String) request.get("payPassword");
            String remark = (String) request.get("remark");

            walletService.bankTransfer(fromUserId, fromCardId, toPhone, toCardId, amount, payPassword, remark);
            return R.success("银行卡转账成功");
        } catch (Exception e) {
            return R.failure("银行卡转账失败: " + e.getMessage());
        }
    }

    /**
     * 钱包转账（支持银行账号）
     */
    @PostMapping("/wallet-transfer")
    public R<String> walletTransfer(@RequestBody Map<String, Object> request) {
        try {
            Integer fromUserId = (Integer) request.get("fromUserId");
            String toAccount = (String) request.get("toAccount"); // 收款银行账号
            String toPhone = (String) request.get("toPhone"); // 收款手机号（兼容旧版本）
            BigDecimal amount = new BigDecimal(request.get("amount").toString());
            String payPassword = (String) request.get("payPassword");
            String remark = (String) request.get("remark");

            // 优先使用银行账号，如果没有则使用手机号（向后兼容）
            if (toAccount != null && !toAccount.isEmpty()) {
                walletService.walletTransferByAccount(fromUserId, toAccount, amount, payPassword, remark);
            } else if (toPhone != null && !toPhone.isEmpty()) {
                walletService.walletTransfer(fromUserId, toPhone, amount, payPassword, remark);
            } else {
                return R.failure("请提供收款人银行账号或手机号");
            }

            return R.success("钱包转账成功");
        } catch (Exception e) {
            return R.failure("钱包转账失败: " + e.getMessage());
        }
    }

    /**
     * 转账（兼容旧接口，默认使用钱包转账）
     */
    @PostMapping("/transfer")
    public R<String> transfer(@RequestBody Map<String, Object> request) {
        try {
            Integer fromUserId = (Integer) request.get("fromUserId");
            String toPhone = (String) request.get("toPhone");
            BigDecimal amount = new BigDecimal(request.get("amount").toString());
            String payPassword = (String) request.get("payPassword");
            String remark = (String) request.get("remark");

            walletService.walletTransfer(fromUserId, toPhone, amount, payPassword, remark);
            return R.success("转账成功");
        } catch (Exception e) {
            return R.failure("转账失败: " + e.getMessage());
        }
    }

    /**
     * 为现有用户创建钱包账户
     */
    @PostMapping("/create-account/{userId}")
    public R<String> createWalletAccount(@PathVariable Integer userId) {
        try {
            walletService.createWalletAccount(userId);
            return R.success("钱包账户创建成功");
        } catch (Exception e) {
            return R.failure("创建失败: " + e.getMessage());
        }
    }

    /**
     * 生成收款码
     */
    @PostMapping("/generate-qr")
    public R<Map<String, Object>> generateReceiveQR(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            BigDecimal amount = request.get("amount") != null ?
                new BigDecimal(request.get("amount").toString()) : null;
            String remark = (String) request.get("remark");

            Map<String, Object> qrInfo = walletService.generateReceiveQR(userId, amount, remark);
            return R.success("收款码生成成功", qrInfo);
        } catch (Exception e) {
            return R.failure("生成收款码失败: " + e.getMessage());
        }
    }

    /**
     * 收款
     */
    @PostMapping("/receive")
    public R<String> receiveMoney(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            BigDecimal amount = new BigDecimal(request.get("amount").toString());
            String fromPhone = (String) request.get("fromPhone");
            String remark = (String) request.get("remark");

            walletService.receiveMoney(userId, amount, fromPhone, remark);
            return R.success("收款成功");
        } catch (Exception e) {
            return R.failure("收款失败: " + e.getMessage());
        }
    }

    /**
     * 测试用：为用户钱包充值余额
     */
    @PostMapping("/test/add-balance")
    public R<String> addTestBalance(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            BigDecimal amount = new BigDecimal(request.get("amount").toString());

            walletService.addTestBalance(userId, amount);
            return R.success("测试余额添加成功");
        } catch (Exception e) {
            return R.failure("添加测试余额失败: " + e.getMessage());
        }
    }

    /**
     * 测试用：初始化测试数据
     */
    @PostMapping("/test/init-data")
    public R<String> initTestData() {
        try {
            walletService.initTestData();
            return R.success("测试数据初始化成功");
        } catch (Exception e) {
            return R.failure("初始化测试数据失败: " + e.getMessage());
        }
    }

    /**
     * 扫码收钱
     */
    @PostMapping("/scan-pay")
    public R<String> scanPay(@RequestBody Map<String, Object> request) {
        try {
            String qrCode = (String) request.get("qrCode");
            Integer payerUserId = (Integer) request.get("payerUserId");
            String payPassword = (String) request.get("payPassword");

            walletService.scanPay(qrCode, payerUserId, payPassword);
            return R.success("支付成功");
        } catch (Exception e) {
            return R.failure("支付失败: " + e.getMessage());
        }
    }

    /**
     * 批量为所有用户初始化钱包和银行卡
     */
    @PostMapping("/init-all-users")
    public R<Map<String, Object>> initAllUsers() {
        try {
            Map<String, Object> result = walletService.initAllUsers();
            return R.success("批量初始化完成", result);
        } catch (Exception e) {
            return R.failure("批量初始化失败: " + e.getMessage());
        }
    }
}
