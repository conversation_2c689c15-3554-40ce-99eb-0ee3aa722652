import request from '@/utils/request'

// 银行卡API服务
const bankCardApi = {
  // 获取用户所有银行卡
  getUserCards(userId) {
    return request({
      url: `/api/bank-card/user/${userId}`,
      method: 'get'
    })
  },

  // 获取所有银行卡（管理员）
  getAllCards() {
    return request({
      url: '/api/bank-card/admin/all',
      method: 'get'
    })
  },

  // 获取用户已绑定的银行卡
  getBoundCards(userId) {
    return request({
      url: `/api/bank-card/bound-cards/${userId}`,
      method: 'get'
    })
  },

  // 获取用户信用卡
  getCreditCards(userId) {
    return request({
      url: `/api/bank-card/credit-cards/${userId}`,
      method: 'get'
    })
  },

  // 获取银行卡详情
  getCardDetails(cardId) {
    return request({
      url: `/api/bank-card/${cardId}`,
      method: 'get'
    })
  },

  // 添加银行卡
  addBankCard(cardData) {
    return request({
      url: '/api/bank-card',
      method: 'post',
      data: cardData
    })
  },

  // 绑定银行卡
  bindCard(cardId, userId) {
    return request({
      url: `/api/bank-card/bind/${cardId}`,
      method: 'put',
      params: { userId }
    })
  },

  // 删除银行卡（从数据库中彻底删除）
  deleteCard(cardId, userId) {
    return request({
      url: `/api/bank-card/${cardId}`,
      method: 'delete',
      params: { userId }
    })
  },

  // 管理员删除银行卡（不验证用户权限）
  adminDeleteCard(cardId) {
    return request({
      url: `/api/bank-card/admin/${cardId}`,
      method: 'delete'
    })
  },

  // 解绑银行卡（只更新状态，不删除数据）
  unbindCard(cardId, userId) {
    return request({
      url: `/api/bank-card/unbind/${cardId}`,
      method: 'put',
      params: { userId }
    })
  },

  // 设置默认银行卡
  setDefaultCard(userId, cardId) {
    return request({
      url: '/api/bank-card/default',
      method: 'put',
      params: { userId, cardId }
    })
  },

  // 验证银行卡信息
  validateBankCard(cardData) {
    return request({
      url: '/api/bank-card/validate',
      method: 'post',
      data: cardData
    })
  },

  // 发送验证码
  sendVerificationCode(cardId, phone) {
    return request({
      url: `/api/bank-card/send-verification/${cardId}`,
      method: 'post',
      params: { phone }
    })
  },

  // 验证连接
  verifyCardConnection(cardId, verificationCode) {
    return request({
      url: `/api/bank-card/verify-connection/${cardId}`,
      method: 'post',
      params: { verificationCode }
    })
  },

  // 断开连接
  disconnectCard(cardId, userId) {
    return request({
      url: `/api/bank-card/disconnect/${cardId}`,
      method: 'put',
      params: { userId }
    })
  },

  // 获取连接统计
  getConnectionStats(userId) {
    return request({
      url: `/api/bank-card/connection-stats/${userId}`,
      method: 'get'
    })
  },

  // 检查银行卡是否可操作
  isCardOperational(cardId) {
    return request({
      url: `/api/bank-card/operational/${cardId}`,
      method: 'get'
    })
  },

  // 分页获取银行卡列表
  getBankCardsByPage(params) {
    return request({
      url: '/api/bank-card/page',
      method: 'get',
      params
    })
  },

  // 更新银行卡信息
  updateBankCard(cardId, data) {
    return request({
      url: `/api/bank-card/${cardId}`,
      method: 'put',
      data
    })
  },

  // 更新银行卡状态
  updateCardStatus(cardId, status) {
    return request({
      url: `/api/bank-card/status/${cardId}`,
      method: 'put',
      data: { status }
    })
  },

  // 删除银行卡（管理员用）
  deleteBankCard(cardId) {
    return request({
      url: `/api/bank-card/${cardId}`,
      method: 'delete'
    })
  },

  // 表格编辑 - 更新单个字段
  updateField(cardId, fieldData) {
    return request({
      url: `/api/bank-card/field/${cardId}`,
      method: 'put',
      data: fieldData
    })
  },

  // 批量更新银行卡
  batchUpdate(bankCards) {
    return request({
      url: `/api/bank-card/batch`,
      method: 'put',
      data: bankCards
    })
  },

  // 获取所有银行卡（管理员用）
  getAllCards() {
    return request({
      url: `/api/bank-card/admin/all`,
      method: 'get'
    })
  },

  // 管理员删除银行卡
  adminDeleteCard(cardId) {
    return request({
      url: `/api/bank-card/${cardId}`,
      method: 'delete'
    })
  },

  // 根据手机号查询银行卡
  getCardsByPhone(phone) {
    return request({
      url: `/api/bank-card/by-phone/${phone}`,
      method: 'get'
    })
  }
}

// 导出API对象
export { bankCardApi }
export default bankCardApi
