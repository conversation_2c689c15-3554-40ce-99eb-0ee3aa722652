/**
 * 数据格式化工具类
 * 提供各种数据格式化和脱敏处理功能
 */
export class DataFormatter {
  private static instance: DataFormatter;

  private constructor() {}

  public static getInstance(): DataFormatter {
    if (!DataFormatter.instance) {
      DataFormatter.instance = new DataFormatter();
    }
    return DataFormatter.instance;
  }

  /**
   * 格式化金额显示
   */
  public formatAmount(amount: number, showSymbol: boolean = true): string {
    if (isNaN(amount)) {
      return showSymbol ? '¥0.00' : '0.00';
    }
    
    const formatted = amount.toFixed(2);
    return showSymbol ? `¥${formatted}` : formatted;
  }

  /**
   * 格式化大额金额（添加千分位分隔符）
   */
  public formatLargeAmount(amount: number, showSymbol: boolean = true): string {
    if (isNaN(amount)) {
      return showSymbol ? '¥0.00' : '0.00';
    }
    
    const formatted = amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return showSymbol ? `¥${formatted}` : formatted;
  }

  /**
   * 脱敏手机号
   */
  public maskPhone(phone: string): string {
    if (!phone || phone.length !== 11) {
      return phone;
    }
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }

  /**
   * 脱敏身份证号
   */
  public maskIdCard(idCard: string): string {
    if (!idCard || idCard.length < 8) {
      return idCard;
    }
    
    if (idCard.length === 18) {
      return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
    } else if (idCard.length === 15) {
      return idCard.replace(/(\d{6})\d{6}(\d{3})/, '$1******$2');
    }
    
    return idCard;
  }

  /**
   * 脱敏银行卡号
   */
  public maskBankCard(cardNo: string): string {
    if (!cardNo || cardNo.length < 8) {
      return cardNo;
    }
    
    // 显示前4位和后4位
    const start = cardNo.substring(0, 4);
    const end = cardNo.substring(cardNo.length - 4);
    const middle = '*'.repeat(cardNo.length - 8);
    
    return `${start}${middle}${end}`;
  }

  /**
   * 脱敏真实姓名
   */
  public maskRealName(name: string): string {
    if (!name || name.length < 2) {
      return name;
    }
    
    if (name.length === 2) {
      return name.charAt(0) + '*';
    } else {
      const first = name.charAt(0);
      const last = name.charAt(name.length - 1);
      const middle = '*'.repeat(name.length - 2);
      return `${first}${middle}${last}`;
    }
  }

  /**
   * 格式化日期时间
   */
  public formatDateTime(dateTime: string | Date, format: 'full' | 'date' | 'time' | 'relative' = 'full'): string {
    let date: Date;
    
    if (typeof dateTime === 'string') {
      date = new Date(dateTime);
    } else {
      date = dateTime;
    }
    
    if (isNaN(date.getTime())) {
      return '无效日期';
    }
    
    switch (format) {
      case 'full':
        return this.formatFullDateTime(date);
      case 'date':
        return this.formatDate(date);
      case 'time':
        return this.formatTime(date);
      case 'relative':
        return this.formatRelativeTime(date);
      default:
        return this.formatFullDateTime(date);
    }
  }

  /**
   * 格式化完整日期时间
   */
  private formatFullDateTime(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * 格式化日期
   */
  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  }

  /**
   * 格式化时间
   */
  private formatTime(date: Date): string {
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${hours}:${minutes}:${seconds}`;
  }

  /**
   * 格式化相对时间
   */
  private formatRelativeTime(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffMinutes < 1) {
      return '刚刚';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return this.formatDate(date);
    }
  }

  /**
   * 格式化文件大小
   */
  public formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 格式化百分比
   */
  public formatPercentage(value: number, total: number, decimals: number = 1): string {
    if (total === 0) return '0%';
    
    const percentage = (value / total) * 100;
    return `${percentage.toFixed(decimals)}%`;
  }

  /**
   * 格式化交易状态
   */
  public formatTransactionStatus(status: string): TransactionStatusFormat {
    switch (status) {
      case '成功':
        return new TransactionStatusFormat('成功', '#4CAF50');
      case '失败':
        return new TransactionStatusFormat('失败', '#F44336');
      case '处理中':
        return new TransactionStatusFormat('处理中', '#FF9800');
      case '已取消':
        return new TransactionStatusFormat('已取消', '#9E9E9E');
      default:
        return new TransactionStatusFormat(status, '#666666');
    }
  }

  /**
   * 格式化交易类型
   */
  public formatTransactionType(type: string): TransactionTypeFormat {
    switch (type) {
      case '充值':
        return new TransactionTypeFormat('充值', '⬇️');
      case '提现':
        return new TransactionTypeFormat('提现', '⬆️');
      case '转账':
        return new TransactionTypeFormat('转账', '💸');
      case '支付':
        return new TransactionTypeFormat('支付', '💳');
      case '退款':
        return new TransactionTypeFormat('退款', '↩️');
      default:
        return new TransactionTypeFormat(type, '📄');
    }
  }

  /**
   * 验证并格式化金额输入
   */
  public validateAndFormatAmount(input: string): AmountValidationResult {
    // 移除非数字和小数点的字符
    const cleaned = input.replace(/[^\d.]/g, '');

    // 检查小数点
    const dotCount = (cleaned.match(/\./g) || []).length;
    if (dotCount > 1) {
      return new AmountValidationResult(false, input, 0);
    }

    // 限制小数位数
    const parts = cleaned.split('.');
    if (parts.length === 2 && parts[1].length > 2) {
      parts[1] = parts[1].substring(0, 2);
    }

    const formatted = parts.join('.');
    const value = parseFloat(formatted) || 0;

    return new AmountValidationResult(
      !isNaN(value) && value >= 0,
      formatted,
      value
    );
  }
}

/**
 * 交易状态格式化结果
 */
export class TransactionStatusFormat {
  public text: string;
  public color: string;

  constructor(text: string, color: string) {
    this.text = text;
    this.color = color;
  }
}

/**
 * 交易类型格式化结果
 */
export class TransactionTypeFormat {
  public text: string;
  public icon: string;

  constructor(text: string, icon: string) {
    this.text = text;
    this.icon = icon;
  }
}

/**
 * 金额验证结果
 */
export class AmountValidationResult {
  public isValid: boolean;
  public formatted: string;
  public value: number;

  constructor(isValid: boolean, formatted: string, value: number) {
    this.isValid = isValid;
    this.formatted = formatted;
    this.value = value;
  }
}

/**
 * 导出单例实例
 */
export const dataFormatter = DataFormatter.getInstance();
