package com.icss.springbootbig.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icss.springbootbig.entity.Account;
import com.icss.springbootbig.entity.BankCard;
import com.icss.springbootbig.entity.Merchant;
import com.icss.springbootbig.entity.Transaction;
import com.icss.springbootbig.entity.User;
import com.icss.springbootbig.enums.TransactionType;
import com.icss.springbootbig.mapper.AccountMapper;
import com.icss.springbootbig.mapper.BankCardMapper;
import com.icss.springbootbig.mapper.MerchantMapper;
import com.icss.springbootbig.mapper.TransactionMapper;
import com.icss.springbootbig.mapper.UserMapper;
import com.icss.springbootbig.exception.ApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Service
public class PaymentService {
    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private TransactionMapper transactionMapper;

    @Autowired
    private BankCardMapper bankCardMapper;

    @Autowired
    private MerchantMapper merchantMapper;

    @Transactional
    public void walletPayment(Integer userId, BigDecimal amount, String paymentType, String remark, String payPassword) {
        // 1. 验证用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 2. 验证支付密码（从默认银行卡获取）
        BankCard defaultCard = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("user_id", userId)
                        .eq("is_default", 1)
                        .eq("status", 1));

        if (defaultCard == null) {
            throw new ApiException("请先设置默认银行卡");
        }

        if (!payPassword.equals(defaultCard.getPayPassword())) {
            throw new ApiException("支付密码错误");
        }

        // 3. 获取账户信息
        Account account = accountMapper.selectOne(
                new QueryWrapper<Account>().eq("user_id", userId));

        if (account == null) {
            throw new ApiException("账户不存在");
        }

        // 4. 检查余额是否充足（如果是扣款）
        if (amount.compareTo(BigDecimal.ZERO) < 0 &&
                account.getBalance().compareTo(amount.abs()) < 0) {
            throw new ApiException("余额不足");
        }

        // 5. 检查支付限额
        if (amount.compareTo(account.getSingleLimit()) > 0) {
            throw new ApiException("超过单笔支付限额");
        }

        // 6. 更新账户余额
        BigDecimal newBalance = account.getBalance().add(amount);
        account.setBalance(newBalance);
        account.setUpdatedAt(new Date());
        accountMapper.updateById(account);

        // 7. 创建交易记录
        Transaction transaction = new Transaction();
        transaction.setTxnNo(generateTxnNo());
        transaction.setUserId(userId);
        transaction.setAccountId(account.getAccountId());
        transaction.setType(convertPaymentType(paymentType));
        transaction.setAmount(amount);
        transaction.setBalance(newBalance);
        transaction.setStatus(1); // 1表示成功
        transaction.setRemark(remark);
        transaction.setCreatedAt(new Date());
        transaction.setUpdatedAt(new Date());
        transactionMapper.insert(transaction);
    }

    private String generateTxnNo() {
        return "TXN" + UUID.randomUUID().toString().replace("-", "").substring(0, 20);
    }

    /**
     * 银行卡支付
     */
    @Transactional
    public void bankCardPayment(Integer userId, BigDecimal amount, Integer cardId,
                               Integer merchantId, String payPassword, String remark) {
        // 1. 验证用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 2. 验证银行卡和支付密码
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null || !card.getUserId().equals(userId)) {
            throw new ApiException("银行卡不存在或不属于该用户");
        }
        if (card.getStatus() != 1) {
            throw new ApiException("银行卡未连接或已冻结");
        }
        if (!payPassword.equals(card.getPayPassword())) {
            throw new ApiException("支付密码错误");
        }



        // 3. 验证商户（如果有）
        Merchant merchant = null;
        if (merchantId != null) {
            merchant = merchantMapper.selectById(merchantId);
            if (merchant == null || merchant.getStatus() != 1) {
                throw new ApiException("商户不存在或已停用");
            }
        }

        // 4. 验证支付金额
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApiException("支付金额必须大于0");
        }

        // 5. 获取用户账户（用于记录交易后余额）
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        if (account == null) {
            throw new ApiException("用户账户不存在");
        }

        // 6. 创建交易记录
        Transaction transaction = new Transaction();
        transaction.setTxnNo(generateTxnNo());
        transaction.setUserId(userId);
        transaction.setAccountId(account.getAccountId());
        transaction.setType(4); // 4-消费
        transaction.setAmount(amount);
        transaction.setBalance(account.getBalance()); // 银行卡支付不影响钱包余额
        transaction.setCardId(cardId);
        transaction.setMerchantId(merchantId);
        transaction.setPaymentMethod(2); // 2-银行卡
        transaction.setPaymentChannel(1); // 1-商户付款
        transaction.setStatus(1); // 1-成功
        transaction.setRemark(remark != null ? remark : "银行卡支付");
        transaction.setCreatedAt(new Date());
        transaction.setUpdatedAt(new Date());

        transactionMapper.insert(transaction);
    }

    /**
     * 扫码支付
     */
    @Transactional
    public void qrCodePayment(Integer userId, String qrCode, String payPassword) {
        // 1. 验证用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 2. 验证支付密码（从默认银行卡获取）
        BankCard defaultCard = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("user_id", userId)
                        .eq("is_default", 1)
                        .eq("status", 1));

        if (defaultCard == null) {
            throw new ApiException("请先设置默认银行卡");
        }

        if (!payPassword.equals(defaultCard.getPayPassword())) {
            throw new ApiException("支付密码错误");
        }

        // 2. 解析二维码信息
        // 这里应该解析二维码获取商户信息和金额
        // 为了演示，假设二维码格式为 "QR{merchantId}_{amount}"
        Map<String, Object> qrInfo = parseQRCode(qrCode);
        Integer merchantId = (Integer) qrInfo.get("merchantId");
        BigDecimal amount = (BigDecimal) qrInfo.get("amount");

        // 3. 验证商户
        Merchant merchant = merchantMapper.selectById(merchantId);
        if (merchant == null || merchant.getStatus() != 1) {
            throw new ApiException("商户不存在或已停用");
        }

        // 4. 获取用户账户
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        if (account == null) {
            throw new ApiException("用户账户不存在");
        }
        if (account.getStatus() != 1) {
            throw new ApiException("用户账户已冻结");
        }

        // 5. 验证余额
        if (account.getBalance().compareTo(amount) < 0) {
            throw new ApiException("余额不足");
        }

        // 6. 更新账户余额
        BigDecimal newBalance = account.getBalance().subtract(amount);
        account.setBalance(newBalance);
        account.setUpdatedAt(new Date());
        accountMapper.updateById(account);

        // 7. 创建交易记录
        Transaction transaction = new Transaction();
        transaction.setTxnNo(generateTxnNo());
        transaction.setUserId(userId);
        transaction.setAccountId(account.getAccountId());
        transaction.setType(4); // 4-消费
        transaction.setAmount(amount);
        transaction.setBalance(newBalance);
        transaction.setMerchantId(merchantId);
        transaction.setPaymentMethod(1); // 1-钱包
        transaction.setPaymentChannel(2); // 2-扫码付款
        transaction.setStatus(1); // 1-成功
        transaction.setRemark("扫码支付");
        transaction.setCreatedAt(new Date());
        transaction.setUpdatedAt(new Date());

        transactionMapper.insert(transaction);
    }

    /**
     * NFC支付
     */
    @Transactional
    public void nfcPayment(Integer userId, String nfcData, String payPassword) {
        // 1. 验证用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 2. 验证支付密码（从默认银行卡获取）
        BankCard defaultCard = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("user_id", userId)
                        .eq("is_default", 1)
                        .eq("status", 1));

        if (defaultCard == null) {
            throw new ApiException("请先设置默认银行卡");
        }

        if (!payPassword.equals(defaultCard.getPayPassword())) {
            throw new ApiException("支付密码错误");
        }

        // 2. 解析NFC数据
        // 这里应该解析NFC数据获取商户信息和金额
        Map<String, Object> nfcInfo = parseNFCData(nfcData);
        Integer merchantId = (Integer) nfcInfo.get("merchantId");
        BigDecimal amount = (BigDecimal) nfcInfo.get("amount");

        // 3. 验证商户
        Merchant merchant = merchantMapper.selectById(merchantId);
        if (merchant == null || merchant.getStatus() != 1) {
            throw new ApiException("商户不存在或已停用");
        }

        // 4. 获取用户账户
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        if (account == null) {
            throw new ApiException("用户账户不存在");
        }
        if (account.getStatus() != 1) {
            throw new ApiException("用户账户已冻结");
        }

        // 5. 验证余额
        if (account.getBalance().compareTo(amount) < 0) {
            throw new ApiException("余额不足");
        }

        // 6. 更新账户余额
        BigDecimal newBalance = account.getBalance().subtract(amount);
        account.setBalance(newBalance);
        account.setUpdatedAt(new Date());
        accountMapper.updateById(account);

        // 7. 创建交易记录
        Transaction transaction = new Transaction();
        transaction.setTxnNo(generateTxnNo());
        transaction.setUserId(userId);
        transaction.setAccountId(account.getAccountId());
        transaction.setType(4); // 4-消费
        transaction.setAmount(amount);
        transaction.setBalance(newBalance);
        transaction.setMerchantId(merchantId);
        transaction.setPaymentMethod(1); // 1-钱包
        transaction.setPaymentChannel(3); // 3-NFC支付
        transaction.setStatus(1); // 1-成功
        transaction.setRemark("NFC支付");
        transaction.setCreatedAt(new Date());
        transaction.setUpdatedAt(new Date());

        transactionMapper.insert(transaction);
    }

    /**
     * 解析二维码信息
     */
    private Map<String, Object> parseQRCode(String qrCode) {
        Map<String, Object> info = new HashMap<>();

        // 简化的二维码解析逻辑
        // 实际应用中应该有更复杂的编码和解析逻辑
        try {
            if (qrCode.startsWith("QR")) {
                String[] parts = qrCode.substring(2).split("_");
                info.put("merchantId", Integer.parseInt(parts[0]));
                info.put("amount", new BigDecimal(parts[1]));
            } else {
                throw new ApiException("无效的二维码格式");
            }
        } catch (Exception e) {
            throw new ApiException("二维码解析失败");
        }

        return info;
    }

    /**
     * 解析NFC数据
     */
    private Map<String, Object> parseNFCData(String nfcData) {
        Map<String, Object> info = new HashMap<>();

        // 简化的NFC数据解析逻辑
        try {
            if (nfcData.startsWith("NFC")) {
                String[] parts = nfcData.substring(3).split("_");
                info.put("merchantId", Integer.parseInt(parts[0]));
                info.put("amount", new BigDecimal(parts[1]));
            } else {
                throw new ApiException("无效的NFC数据格式");
            }
        } catch (Exception e) {
            throw new ApiException("NFC数据解析失败");
        }

        return info;
    }

    private int convertPaymentType(String paymentType) {
        switch (paymentType.toUpperCase()) {
            case "RECHARGE":
            case "DEPOSIT":
                return TransactionType.DEPOSIT.getCode();
            case "CONSUME":
                return TransactionType.CONSUME.getCode();
            case "TRANSFER":
                return TransactionType.TRANSFER.getCode();
            case "WITHDRAW":
                return TransactionType.WITHDRAW.getCode();
            case "REFUND":
                return TransactionType.DEPOSIT.getCode(); // 退款视为充值
            default:
                return TransactionType.CONSUME.getCode();
        }
    }
}