import router from '@ohos.router';
import { Wallet<PERSON><PERSON> } from '../api/WalletApi';
import { UserApi } from '../api/UserApi';
import { TransactionApi } from '../api/TransactionApi';
import { BankCardApi } from '../api/BankCardApi';
import { storageManager, LocalUserInfo, LocalWalletInfo } from '../common/storage/StorageManager';
import { httpClient } from '../common/http/HttpClient';
import { loadingManager, LoadingKeys } from '../common/utils/LoadingManager';
import { errorHandler } from '../common/utils/ErrorHandler';
import { dataFormatter } from '../common/utils/DataFormatter';
import { globalStateManager, RefreshTypes } from '../common/utils/EventManager';
import {
  LoadingState,
  UserInfo,
  WalletInfo,
  Transaction,
  BankCard,
  TransactionQueryParams,
  PageResult,
  TransactionType,
  PaymentMethod,
  TransactionStatus,
  BankCardType,
  BankCardStatus,
  SpringBootUserResponse,
  SpringBootAccountResponse,
  SpringBootTransactionResponse,
  SpringBootBankCardResponse
} from '../common/types/index';
import { tempDataManager } from '../common/storage/TempDataManager';

@Entry
@Component
struct MainPage {
  @State currentTabIndex: number = 0;
  @State userInfo: LocalUserInfo | null = null;
  @State walletBalance: LocalWalletInfo | null = null;
  @State pageLoadingState: LoadingState = LoadingState.IDLE;
  @State userLoadingState: LoadingState = LoadingState.IDLE;
  @State walletLoadingState: LoadingState = LoadingState.IDLE;
  @State transactions: Transaction[] = [];
  @State bankCards: BankCard[] = [];
  @State transactionLoading: boolean = false;
  @State bankCardLoading: boolean = false;
  @State bankCardRefreshFlag: number = 0; // 用于强制刷新银行卡列表

  aboutToAppear() {
    this.loadUserData();
  }

  onPageShow() {
    // 检查全局状态是否需要刷新
    const refreshInfo = globalStateManager.checkNeedsRefresh();
    if (refreshInfo.needsRefresh) {
      console.log('页面显示，检测到需要刷新:', refreshInfo.refreshType);
      if (refreshInfo.refreshType === RefreshTypes.WALLET || refreshInfo.refreshType === RefreshTypes.TRANSACTION) {
        this.refreshWalletData();
      } else {
        this.loadUserData();
      }
    }

    // 检查是否有银行卡添加事件
    const cardAdded = tempDataManager.getData('BANK_CARD_ADDED');
    console.log('MainPage onPageShow - 检查银行卡添加事件:', cardAdded);
    if (cardAdded) {
      console.log('MainPage - 检测到银行卡添加，重新加载银行卡列表');
      // 重新加载银行卡列表
      this.loadBankCards();
      // 设置标志表示MainPage已处理
      tempDataManager.setData('MAIN_PAGE_PROCESSED', true);
    }

    // 检查是否有银行卡解绑事件
    const cardUnbound = tempDataManager.getData('BANK_CARD_UNBOUND');
    console.log('MainPage onPageShow - 检查银行卡解绑事件:', cardUnbound);
    if (cardUnbound) {
      console.log('MainPage - 检测到银行卡解绑，重新加载银行卡列表');
      // 重新加载银行卡列表
      this.loadBankCards();
      // 设置标志表示MainPage已处理解绑事件
      tempDataManager.setData('MAIN_PAGE_UNBOUND_PROCESSED', true);
    }
  }

  async loadUserData() {
    this.pageLoadingState = LoadingState.LOADING;

    try {
      // 检查登录状态
      const isLoggedIn = await storageManager.isLoggedIn();
      if (!isLoggedIn) {
        router.replaceUrl({
          url: 'pages/LoginPage'
        });
        return;
      }

      // 获取并设置token
      const token = await storageManager.getUserToken();
      if (token) {
        httpClient.setAuthToken(token);
      }

      // 尝试从本地存储获取用户信息
      const cachedUserInfo = await storageManager.getUserInfo();
      const cachedWalletInfo = await storageManager.getWalletInfo();

      if (cachedUserInfo) {
        this.userInfo = cachedUserInfo;
      }
      if (cachedWalletInfo) {
        this.walletBalance = cachedWalletInfo;
      }

      // 使用加载管理器并行加载最新数据
      await Promise.all([
        this.loadUserInfo(),
        this.loadWalletInfo(),
        this.loadTransactions(),
        this.loadBankCards()
      ]);

      this.pageLoadingState = LoadingState.SUCCESS;

    } catch (error) {
      console.error('加载用户数据失败:', error);
      this.pageLoadingState = LoadingState.ERROR;

      // 处理认证错误
      await errorHandler.handleError(error as Error, '加载用户数据');

      // 如果是认证错误，清除本地数据并跳转到登录页
      if (error && typeof error === 'object' && this.isAuthError(error)) {
        await storageManager.clearUserData();
        httpClient.clearAuthToken();
        router.replaceUrl({
          url: 'pages/LoginPage'
        });
      }
    }
  }

  async loadUserInfo() {
    try {
      // 从本地存储获取当前用户ID
      const cachedUserInfo = await storageManager.getUserInfo();
      if (!cachedUserInfo || !cachedUserInfo.userId) {
        console.error('无法获取用户ID');
        return;
      }

      // 调用SpringBoot3后端API获取用户信息
      const response = await httpClient.get<SpringBootUserResponse>(`/user/${cachedUserInfo.userId}`);
      const userInfo: SpringBootUserResponse = response.data;

      this.userInfo = this.convertSpringBootUserToLocal(userInfo);
      await storageManager.saveUserInfo(this.userInfo);
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  }

  async loadWalletInfo() {
    try {
      // 从本地存储获取当前用户ID
      const cachedUserInfo = await storageManager.getUserInfo();
      if (!cachedUserInfo || !cachedUserInfo.userId) {
        console.error('无法获取用户ID');
        return;
      }

      // 调用SpringBoot3后端API获取账户信息
      const response = await httpClient.get<SpringBootAccountResponse>(`/account/${cachedUserInfo.userId}`);
      const accountInfo: SpringBootAccountResponse = response.data;

      this.walletBalance = this.convertSpringBootAccountToLocal(accountInfo);
      await storageManager.saveWalletInfo(this.walletBalance);
    } catch (error) {
      console.error('获取钱包余额失败:', error);
    }
  }

  private convertToLocalUserInfo(userInfo: UserInfo): LocalUserInfo {
    return {
      userId: userInfo.userId,
      phone: userInfo.phone,
      realName: userInfo.realName,
      idCard: userInfo.idCard,
      walletNo: '',
      balance: 0,
      payLimit: userInfo.payLimit,
      status: 1,
      createTime: userInfo.createTime,
      updateTime: userInfo.updateTime
    };
  }

  private convertToLocalWalletInfo(walletInfo: WalletInfo): LocalWalletInfo {
    return {
      walletNo: walletInfo.walletNo,
      balance: walletInfo.balance,
      status: 1
    };
  }

  /**
   * 转换SpringBoot3用户信息到本地格式
   */
  private convertSpringBootUserToLocal(userInfo: SpringBootUserResponse): LocalUserInfo {
    return {
      userId: userInfo.userId || 0,
      phone: userInfo.phone || '',
      realName: userInfo.realName || userInfo.username || '用户',
      idCard: userInfo.idCard || '',
      walletNo: '',
      balance: 0,
      payLimit: userInfo.payLimit || 1000,
      status: userInfo.status || 1,
      createTime: userInfo.createdAt || userInfo.createTime || '',
      updateTime: userInfo.updatedAt || userInfo.updateTime || ''
    };
  }

  /**
   * 转换SpringBoot3账户信息到本地钱包格式
   */
  private convertSpringBootAccountToLocal(accountInfo: SpringBootAccountResponse): LocalWalletInfo {
    return {
      walletNo: accountInfo.accountNo || '',
      balance: accountInfo.balance || 0,
      status: accountInfo.status || 1
    };
  }

  /**
   * 转换SpringBoot3交易记录到本地格式
   */
  private convertSpringBootTransactionsToLocal(transactionList: SpringBootTransactionResponse[]): Transaction[] {
    return transactionList.map((transaction: SpringBootTransactionResponse): Transaction => ({
      transactionId: transaction.transactionId || 0,
      transactionNo: transaction.transactionNo || '',
      fromUserId: transaction.fromUserId || transaction.userId || 0,
      toUserId: transaction.toUserId || 0,
      amount: transaction.amount || 0,
      transactionType: this.mapTransactionTypeToEnum(transaction.type),
      paymentMethod: this.mapPaymentMethod(transaction.paymentMethod || 1),
      description: transaction.description || '',
      status: this.mapTransactionStatusToEnum(transaction.status),
      createTime: transaction.createdAt || transaction.createTime || '',
      relatedCardId: transaction.relatedCardId,
      cardNo: transaction.cardNo,
      statusDesc: this.mapTransactionStatus(transaction.status),
      maskedCardNo: transaction.cardNo ? this.maskCardNo(transaction.cardNo) : undefined,
      fromUserName: transaction.fromUserName,
      toUserName: transaction.toUserName
    }));
  }

  /**
   * 转换SpringBoot3银行卡到本地格式
   */
  private convertSpringBootBankCardsToLocal(bankCardList: SpringBootBankCardResponse[]): BankCard[] {
    return bankCardList.map((card: SpringBootBankCardResponse): BankCard => ({
      cardId: card.cardId || 0,
      userId: card.userId || 0,
      cardNo: card.cardNumber || '',
      cardType: this.mapBankCardType(card.cardType),
      bankName: card.bankName || '',
      holderName: card.cardHolder || '',
      isBound: card.status === 1 ? BankCardStatus.BOUND : BankCardStatus.UNBOUND,
      createTime: card.createdAt || card.createTime || '',
      updateTime: card.updatedAt || card.updateTime || '',
      maskedCardNo: card.cardNumber ? this.maskCardNo(card.cardNumber) : undefined
    }));
  }

  /**
   * 映射银行卡类型
   */
  private mapBankCardType(cardType: string | number | undefined): BankCardType {
    if (typeof cardType === 'number') {
      // 数字类型：1=储蓄卡，2=信用卡
      return cardType === 2 ? BankCardType.CREDIT : BankCardType.DEBIT;
    } else if (typeof cardType === 'string') {
      // 字符串类型
      if (cardType === '信用卡' || cardType === 'CREDIT') {
        return BankCardType.CREDIT;
      }
    }
    // 默认返回储蓄卡
    return BankCardType.DEBIT;
  }

  /**
   * 映射交易类型到枚举
   */
  private mapTransactionTypeToEnum(type: number): TransactionType {
    switch (type) {
      case 1: return TransactionType.RECHARGE;
      case 2: return TransactionType.WITHDRAW;
      case 3: return TransactionType.TRANSFER;
      case 4: return TransactionType.RECEIVE;
      case 5: return TransactionType.PAYMENT;
      case 6: return TransactionType.REFUND;
      default: return TransactionType.PAYMENT;
    }
  }

  /**
   * 映射支付方式
   */
  private mapPaymentMethod(method: number): PaymentMethod {
    switch (method) {
      case 1: return PaymentMethod.WALLET;
      case 2: return PaymentMethod.BANK_CARD;
      case 3: return PaymentMethod.THIRD_PARTY;
      case 4: return PaymentMethod.THIRD_PARTY;
      default: return PaymentMethod.WALLET;
    }
  }

  /**
   * 映射交易状态到枚举
   */
  private mapTransactionStatusToEnum(status: number): TransactionStatus {
    switch (status) {
      case 1: return TransactionStatus.SUCCESS;
      case 0: return TransactionStatus.FAILED;
      case 2: return TransactionStatus.PENDING;
      default: return TransactionStatus.PENDING;
    }
  }

  /**
   * 映射交易状态到字符串
   */
  private mapTransactionStatus(status: number): string {
    switch (status) {
      case 1: return '成功';
      case 0: return '失败';
      case 2: return '处理中';
      default: return '未知';
    }
  }

  /**
   * 脱敏银行卡号
   */
  private maskCardNo(cardNo: string): string {
    if (!cardNo || cardNo.length < 8) return cardNo;
    return cardNo.replace(/(\d{4})\d*(\d{4})/, '$1****$2');
  }

  private isAuthError(error: object): boolean {
    return typeof (error as AuthErrorType).code === 'number' &&
           (error as AuthErrorType).code === 401;
  }

  async loadTransactions() {
    try {
      this.transactionLoading = true;

      // 从本地存储获取当前用户ID
      const cachedUserInfo = await storageManager.getUserInfo();
      if (!cachedUserInfo || !cachedUserInfo.userId) {
        console.error('无法获取用户ID');
        return;
      }

      // 调用SpringBoot3后端API获取交易记录
      const response = await httpClient.get<SpringBootTransactionResponse[]>(`/transaction/query?userId=${cachedUserInfo.userId}`);
      const transactionList: SpringBootTransactionResponse[] = response.data;

      this.transactions = this.convertSpringBootTransactionsToLocal(transactionList);
    } catch (error) {
      console.error('获取交易记录失败:', error);
    } finally {
      this.transactionLoading = false;
    }
  }

  async loadBankCards() {
    try {
      console.log('MainPage - 开始加载银行卡列表');
      this.bankCardLoading = true;

      // 从本地存储获取当前用户ID
      const cachedUserInfo = await storageManager.getUserInfo();
      if (!cachedUserInfo || !cachedUserInfo.userId) {
        console.error('无法获取用户ID');
        return;
      }

      // 调用SpringBoot3后端API获取银行卡列表
      const response = await httpClient.get<SpringBootBankCardResponse[]>(`/bank-card/user/${cachedUserInfo.userId}`);
      const bankCardList: SpringBootBankCardResponse[] = response.data;

      this.bankCards = this.convertSpringBootBankCardsToLocal(bankCardList);
      this.bankCardRefreshFlag++; // 强制界面刷新
      console.log('MainPage - 银行卡列表加载完成，数量:', this.bankCards.length, 'refreshFlag:', this.bankCardRefreshFlag);
    } catch (error) {
      console.error('获取银行卡列表失败:', error);
    } finally {
      this.bankCardLoading = false;
    }
  }

  async refreshWalletData() {
    try {
      console.log('刷新钱包数据...');
      // 并行刷新钱包余额和交易记录
      await Promise.all([
        this.loadWalletInfo(),
        this.loadTransactions()
      ]);
      console.log('钱包数据刷新完成');
    } catch (error) {
      console.error('刷新钱包数据失败:', error);
    }
  }

  @Builder
  WalletHomeTab() {
    Column() {
      if (this.pageLoadingState === LoadingState.LOADING) {
        this.LoadingView()
      } else {
        this.WalletContent()
      }
    }
    .width('100%')
    .height('100%')
  }

  @Builder
  LoadingView() {
    Column() {
      LoadingProgress()
        .width(40)
        .height(40)
        .color('#1976D2')
      
      Text('加载中...')
        .fontSize(14)
        .fontColor('#999999')
        .margin({ top: 16 })
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  WalletContent() {
    Scroll() {
      Column() {
        // 钱包卡片
        this.WalletCard()
        
        // 快捷操作
        this.QuickActions()
        
        // 最近交易
        this.RecentTransactions()
      }
      .padding({ left: 16, right: 16, bottom: 20 })
    }
    .scrollable(ScrollDirection.Vertical)
    .scrollBar(BarState.Off)
  }

  @Builder
  WalletCard() {
    Column() {
      // 顶部用户信息
      Row() {
        Column() {
          Text(`你好，${this.userInfo?.realName || '用户'}`)
            .fontSize(16)
            .fontColor('#FFFFFF')
            .fontWeight(FontWeight.Medium)
          
          Text(this.userInfo?.phone || '')
            .fontSize(12)
            .fontColor('#E3F2FD')
            .margin({ top: 4 })
        }
        .alignItems(HorizontalAlign.Start)
        .layoutWeight(1)

        Row() {


          Text('⚙️')
            .fontSize(24)
            .fontColor('#FFFFFF')
            .onClick(() => {
              router.pushUrl({
                url: 'pages/SettingsPage'
              });
            })
        }
      }
      .width('100%')
      .margin({ bottom: 24 })

      // 余额显示
      Column() {
        Text('钱包余额')
          .fontSize(14)
          .fontColor('#E3F2FD')
          .margin({ bottom: 8 })

        Text(`¥${(this.walletBalance?.balance ?? 0).toFixed(2)}`)
          .fontSize(32)
          .fontColor('#FFFFFF')
          .fontWeight(FontWeight.Bold)

        Text(`钱包号：${this.walletBalance?.walletNo || ''}`)
          .fontSize(12)
          .fontColor('#E3F2FD')
          .margin({ top: 8 })
      }
      .alignItems(HorizontalAlign.Start)
    }
    .width('100%')
    .padding(20)
    .borderRadius(16)
    .backgroundColor('#1976D2')
    .margin({ top: 20, bottom: 24 })
  }

  @Builder
  QuickActions() {
    Column() {
      Text('快捷操作')
        .fontSize(16)
        .fontColor('#333333')
        .fontWeight(FontWeight.Medium)
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })

      Row() {
        this.ActionButton('充值', $r('app.media.ic_recharge'), () => {
          router.pushUrl({
            url: 'pages/WalletOperationPage',
            params: { operationType: 'recharge' }
          });
        })

        this.ActionButton('提现', $r('app.media.ic_withdraw'), () => {
          router.pushUrl({
            url: 'pages/WalletOperationPage',
            params: { operationType: 'withdraw' }
          });
        })

        this.ActionButton('转账', $r('app.media.ic_transfer'), () => {
          router.pushUrl({
            url: 'pages/WalletOperationPage',
            params: { operationType: 'transfer' }
          });
        })

        this.ActionButton('银行卡', $r('app.media.ic_card'), () => {
          router.pushUrl({ url: 'pages/BankCardPage' });
        })
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceEvenly)
    }
    .width('100%')
    .margin({ bottom: 24 })
  }

  @Builder
  ActionButton(title: string, icon: Resource, onClick: () => void) {
    Column() {
      Button() {
        Image(icon)
          .width(24)
          .height(24)
          .fillColor('#1976D2')
      }
      .width(56)
      .height(56)
      .borderRadius(28)
      .backgroundColor('#E3F2FD')
      .onClick(onClick)

      Text(title)
        .fontSize(12)
        .fontColor('#666666')
        .margin({ top: 8 })
    }
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  RecentTransactions() {
    Column() {
      Row() {
        Text('最近交易')
          .fontSize(16)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)

        Text('查看全部')
          .fontSize(14)
          .fontColor('#1976D2')
          .onClick(() => {
            this.currentTabIndex = 1; // 切换到交易记录tab
          })
      }
      .width('100%')
      .margin({ bottom: 16 })

      // 显示最近的交易记录
      if (this.transactionLoading) {
        Row() {
          LoadingProgress()
            .width(20)
            .height(20)
            .color('#1976D2')
            .margin({ right: 8 })

          Text('加载中...')
            .fontSize(14)
            .fontColor('#999999')
        }
        .width('100%')
        .height(80)
        .justifyContent(FlexAlign.Center)
        .backgroundColor('#F8F9FA')
        .borderRadius(8)
      } else if (this.transactions.length === 0) {
        Column() {
          Text('暂无交易记录')
            .fontSize(14)
            .fontColor('#999999')
            .margin({ bottom: 12 })

          Button('去充值')
            .fontSize(12)
            .fontColor('#1976D2')
            .backgroundColor('#E3F2FD')
            .borderRadius(6)
            .height(32)
            .onClick(() => {
              router.pushUrl({ url: 'pages/RechargePage' });
            })
        }
        .width('100%')
        .height(100)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .backgroundColor('#F8F9FA')
        .borderRadius(8)
      } else {
        Column() {
          ForEach(this.transactions.slice(0, 3), (transaction: Transaction, index: number) => {
            this.RecentTransactionItem(transaction)
          })

          if (this.transactions.length > 3) {
            Text(`还有${this.transactions.length - 3}条交易记录`)
              .fontSize(12)
              .fontColor('#999999')
              .textAlign(TextAlign.Center)
              .width('100%')
              .margin({ top: 8 })
              .onClick(() => {
                this.currentTabIndex = 1; // 切换到交易记录tab
              })
          }
        }
        .width('100%')
        .backgroundColor('#F8F9FA')
        .borderRadius(8)
        .padding(12)
      }
    }
    .width('100%')
    .padding(20)
    .borderRadius(12)
    .backgroundColor('#FFFFFF')
  }

  build() {
    Column() {
      Tabs({ barPosition: BarPosition.End, index: this.currentTabIndex }) {
        TabContent() {
          this.WalletHomeTab()
        }
        .tabBar(this.TabBuilder('钱包', $r('app.media.ic_wallet'), 0))

        TabContent() {
          this.TransactionTab()
        }
        .tabBar(this.TabBuilder('交易', $r('app.media.ic_transaction'), 1))

        TabContent() {
          this.BankCardTab()
        }
        .tabBar(this.TabBuilder('银行卡', $r('app.media.ic_card'), 2))

        TabContent() {
          this.ProfileTab()
        }
        .tabBar(this.TabBuilder('我的', $r('app.media.ic_profile'), 3))
      }
      .onChange((index: number) => {
        console.log('Tab切换到:', index);
        this.currentTabIndex = index;

        // 如果切换到银行卡tab，检查是否需要刷新
        if (index === 2) {
          // 检查银行卡添加事件
          const cardAdded = tempDataManager.getData('BANK_CARD_ADDED');
          const mainPageProcessed = tempDataManager.getData('MAIN_PAGE_PROCESSED');
          console.log('切换到银行卡tab，检查银行卡添加事件:', cardAdded, 'MainPage已处理:', mainPageProcessed);

          if (cardAdded && !mainPageProcessed) {
            console.log('检测到银行卡添加且MainPage未处理，重新加载银行卡列表');
            this.loadBankCards();
            // 设置标志表示MainPage已处理
            tempDataManager.setData('MAIN_PAGE_PROCESSED', true);
          }

          // 检查银行卡解绑事件
          const cardUnbound = tempDataManager.getData('BANK_CARD_UNBOUND');
          const mainPageUnboundProcessed = tempDataManager.getData('MAIN_PAGE_UNBOUND_PROCESSED');
          console.log('切换到银行卡tab，检查银行卡解绑事件:', cardUnbound, 'MainPage已处理解绑:', mainPageUnboundProcessed);

          if (cardUnbound && !mainPageUnboundProcessed) {
            console.log('检测到银行卡解绑且MainPage未处理，重新加载银行卡列表');
            this.loadBankCards();
            // 设置标志表示MainPage已处理解绑事件
            tempDataManager.setData('MAIN_PAGE_UNBOUND_PROCESSED', true);
          }

          // 如果MainPage已处理添加事件，清理相关标志
          if (cardAdded && mainPageProcessed) {
            console.log('MainPage已处理添加事件，清理相关标志');
            tempDataManager.removeData('BANK_CARD_ADDED');
            tempDataManager.removeData('MAIN_PAGE_PROCESSED');
          }

          // 如果MainPage已处理解绑事件，清理相关标志
          if (cardUnbound && mainPageUnboundProcessed) {
            console.log('MainPage已处理解绑事件，清理相关标志');
            tempDataManager.removeData('BANK_CARD_UNBOUND');
            tempDataManager.removeData('MAIN_PAGE_UNBOUND_PROCESSED');
          }
        }
      })
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  TransactionTab() {
    Column() {
      // 交易记录标题
      Row() {
        Text('交易记录')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)

        Button('查看全部')
          .fontSize(14)
          .fontColor('#1976D2')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            router.pushUrl({ url: 'pages/TransactionListPage' });
          })
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 16, bottom: 8 })
      .backgroundColor('#FFFFFF')

      // 交易记录列表
      if (this.transactionLoading) {
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
            .color('#1976D2')

          Text('加载中...')
            .fontSize(14)
            .fontColor('#999999')
            .margin({ top: 16 })
        }
        .width('100%')
        .height(200)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .backgroundColor('#FFFFFF')
      } else if (this.transactions.length === 0) {
        Column() {
          Text('暂无交易记录')
            .fontSize(16)
            .fontColor('#999999')
            .margin({ bottom: 20 })

          Button('去充值')
            .fontSize(14)
            .fontColor('#1976D2')
            .backgroundColor('#E3F2FD')
            .borderRadius(8)
            .onClick(() => {
              router.pushUrl({ url: 'pages/RechargePage' });
            })
        }
        .width('100%')
        .height(200)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .backgroundColor('#FFFFFF')
      } else {
        List() {
          ForEach(this.transactions, (transaction: Transaction, index: number) => {
            ListItem() {
              this.TransactionItem(transaction)
            }
            .onClick(() => {
              router.pushUrl({
                url: 'pages/TransactionDetailPage',
                params: {
                  transactionId: transaction.transactionId
                }
              });
            })
          })
        }
        .layoutWeight(1)
        .backgroundColor('#FFFFFF')
        .padding({ left: 16, right: 16, bottom: 16 })
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  BankCardTab() {
    Column() {
      // 银行卡标题
      Row() {
        Text('我的银行卡')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)

        Button('管理')
          .fontSize(14)
          .fontColor('#1976D2')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            router.pushUrl({ url: 'pages/BankCardPage' });
          })
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 16, bottom: 8 })
      .backgroundColor('#FFFFFF')

      // 银行卡列表
      if (this.bankCardLoading) {
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
            .color('#1976D2')

          Text('加载中...')
            .fontSize(14)
            .fontColor('#999999')
            .margin({ top: 16 })
        }
        .width('100%')
        .height(200)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .backgroundColor('#FFFFFF')
      } else if (this.bankCards.length === 0) {
        Column() {
          Text('暂无银行卡')
            .fontSize(16)
            .fontColor('#999999')
            .margin({ bottom: 20 })

          Button('添加银行卡')
            .fontSize(14)
            .fontColor('#1976D2')
            .backgroundColor('#E3F2FD')
            .borderRadius(8)
            .onClick(() => {
              router.pushUrl({ url: 'pages/AddBankCardPage' });
            })
        }
        .width('100%')
        .height(200)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .backgroundColor('#FFFFFF')
      } else {
        List() {
          ForEach(this.bankCards, (card: BankCard, index: number) => {
            ListItem() {
              this.BankCardItem(card, this.bankCardRefreshFlag)
            }
            .onClick(() => {
              router.pushUrl({ url: 'pages/BankCardPage' });
            })
          }, (card: BankCard, index: number) => `${card.cardId}_${this.bankCardRefreshFlag}`)
        }
        .layoutWeight(1)
        .backgroundColor('#FFFFFF')
        .padding({ left: 16, right: 16, bottom: 16 })
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  ProfileTab() {
    Scroll() {
      Column() {
        // 用户信息卡片
        if (this.userInfo) {
          Column() {
            Row() {
              // 头像
              Text(this.userInfo.realName?.charAt(0) || '用')
                .fontSize(24)
                .fontColor('#FFFFFF')
                .fontWeight(FontWeight.Bold)
                .width(60)
                .height(60)
                .textAlign(TextAlign.Center)
                .backgroundColor('#1976D2')
                .borderRadius(30)

              Column() {
                Text(this.userInfo.realName || '用户')
                  .fontSize(18)
                  .fontColor('#333333')
                  .fontWeight(FontWeight.Medium)
                  .alignSelf(ItemAlign.Start)

                Text(this.maskPhone(this.userInfo.phone))
                  .fontSize(14)
                  .fontColor('#666666')
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 4 })
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Start)
              .margin({ left: 16 })

              Text('>')
                .fontSize(16)
                .fontColor('#CCCCCC')
            }
            .width('100%')
            .onClick(() => {
              router.pushUrl({ url: 'pages/SettingsPage' });
            })
          }
          .width('100%')
          .padding(20)
          .borderRadius(12)
          .backgroundColor('#FFFFFF')
          .margin({ top: 16 })
        }

        // 银行卡管理
        Column() {
          Row() {
            Text('我的银行卡')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .layoutWeight(1)

            Button('管理')
              .fontSize(14)
              .fontColor('#1976D2')
              .backgroundColor(Color.Transparent)
              .onClick(() => {
                router.pushUrl({ url: 'pages/BankCardPage' });
              })
          }
          .width('100%')
          .margin({ bottom: 16 })

          if (this.bankCardLoading) {
            Row() {
              LoadingProgress()
                .width(20)
                .height(20)
                .color('#1976D2')
                .margin({ right: 8 })

              Text('加载中...')
                .fontSize(14)
                .fontColor('#999999')
            }
            .width('100%')
            .height(60)
            .justifyContent(FlexAlign.Center)
          } else if (this.bankCards.length === 0) {
            Column() {
              Text('暂无银行卡')
                .fontSize(14)
                .fontColor('#999999')
                .margin({ bottom: 12 })

              Button('添加银行卡')
                .fontSize(14)
                .fontColor('#1976D2')
                .backgroundColor('#E3F2FD')
                .borderRadius(8)
                .height(36)
                .onClick(() => {
                  router.pushUrl({ url: 'pages/AddBankCardPage' });
                })
            }
            .width('100%')
            .height(80)
            .justifyContent(FlexAlign.Center)
            .alignItems(HorizontalAlign.Center)
          } else {
            Column() {
              ForEach(this.bankCards.slice(0, 3), (card: BankCard, index: number) => {
                this.BankCardItem(card, this.bankCardRefreshFlag)
              }, (card: BankCard, index: number) => `${card.cardId}_${this.bankCardRefreshFlag}`)

              if (this.bankCards.length > 3) {
                Text(`还有${this.bankCards.length - 3}张银行卡`)
                  .fontSize(12)
                  .fontColor('#999999')
                  .textAlign(TextAlign.Center)
                  .width('100%')
                  .margin({ top: 8 })
              }
            }
          }
        }
        .width('100%')
        .padding(20)
        .borderRadius(12)
        .backgroundColor('#FFFFFF')
        .margin({ top: 16 })

        // 功能菜单
        Column() {
          this.MenuItemBuilder('交易记录', '查看所有交易记录', '📊', () => {
            router.pushUrl({ url: 'pages/TransactionListPage' });
          })

          this.MenuItemBuilder('设置', '账户设置和安全', '⚙️', () => {
            router.pushUrl({ url: 'pages/SettingsPage' });
          })

          this.MenuItemBuilder('帮助中心', '常见问题和使用指南', '❓', () => {
            router.pushUrl({ url: 'pages/HelpCenterPage' });
          })

          this.MenuItemBuilder('关于我们', 'E-Wallet v1.2.0', 'ℹ️', () => {
            router.pushUrl({ url: 'pages/AboutPage' });
          })
        }
        .width('100%')
        .padding(20)
        .borderRadius(12)
        .backgroundColor('#FFFFFF')
        .margin({ top: 16 })
      }
      .padding({ left: 16, right: 16, bottom: 20 })
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  RecentTransactionItem(transaction: Transaction) {
    Row() {
      // 交易类型图标
      Text(this.getTransactionIcon(transaction.transactionType))
        .fontSize(20)
        .width(32)
        .height(32)
        .textAlign(TextAlign.Center)
        .backgroundColor('#FFFFFF')
        .borderRadius(16)

      Column() {
        Row() {
          Text(transaction.transactionType)
            .fontSize(14)
            .fontColor('#333333')
            .fontWeight(FontWeight.Medium)
            .layoutWeight(1)

          Text(this.formatAmount(transaction))
            .fontSize(14)
            .fontColor(this.getAmountColor(transaction))
            .fontWeight(FontWeight.Medium)
        }
        .width('100%')

        Text(this.formatDateTime(transaction.createTime))
          .fontSize(12)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
          .margin({ top: 2 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)
      .margin({ left: 12 })
    }
    .width('100%')
    .padding(8)
    .margin({ bottom: 4 })
    .onClick(() => {
      router.pushUrl({
        url: 'pages/TransactionDetailPage',
        params: {
          transactionId: transaction.transactionId
        }
      });
    })
  }

  @Builder
  TransactionItem(transaction: Transaction) {
    Row() {
      // 交易类型图标
      Text(this.getTransactionIcon(transaction.transactionType))
        .fontSize(24)
        .width(40)
        .height(40)
        .textAlign(TextAlign.Center)
        .backgroundColor('#F0F0F0')
        .borderRadius(20)

      Column() {
        Row() {
          Text(transaction.transactionType)
            .fontSize(16)
            .fontColor('#333333')
            .fontWeight(FontWeight.Medium)
            .layoutWeight(1)

          Text(this.formatAmount(transaction))
            .fontSize(16)
            .fontColor(this.getAmountColor(transaction))
            .fontWeight(FontWeight.Medium)
        }
        .width('100%')

        Row() {
          Text(this.formatDateTime(transaction.createTime))
            .fontSize(12)
            .fontColor('#999999')
            .layoutWeight(1)

          Text(transaction.status)
            .fontSize(12)
            .fontColor(this.getStatusColor(transaction.status))
        }
        .width('100%')
        .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)
      .margin({ left: 12 })
    }
    .width('100%')
    .padding(12)
    .margin({ bottom: 8 })
    .borderRadius(8)
    .backgroundColor('#F8F9FA')
  }

  @Builder
  BankCardItem(card: BankCard, refreshFlag?: number) {
    Row() {
      Text('💳')
        .fontSize(24)
        .width(40)
        .height(40)
        .textAlign(TextAlign.Center)
        .backgroundColor('#E3F2FD')
        .borderRadius(20)

      Column() {
        Text(card.bankName)
          .fontSize(16)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .alignSelf(ItemAlign.Start)

        Text(`${card.cardType} **** ${card.cardNo.slice(-4)}`)
          .fontSize(14)
          .fontColor('#666666')
          .alignSelf(ItemAlign.Start)
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)
      .margin({ left: 12 })

      Text('>')
        .fontSize(16)
        .fontColor('#CCCCCC')
    }
    .width('100%')
    .padding(12)
    .margin({ bottom: 8 })
    .borderRadius(8)
    .backgroundColor('#F8F9FA')
    .onClick(() => {
      router.pushUrl({ url: 'pages/BankCardPage' });
    })
  }

  @Builder
  MenuItemBuilder(title: string, subtitle: string, icon: string, onClick: () => void) {
    Row() {
      Text(icon)
        .fontSize(20)
        .width(40)
        .textAlign(TextAlign.Center)

      Column() {
        Text(title)
          .fontSize(16)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)

        Text(subtitle)
          .fontSize(12)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)
      .margin({ left: 12 })

      Text('>')
        .fontSize(16)
        .fontColor('#CCCCCC')
    }
    .width('100%')
    .height(60)
    .padding({ top: 8, bottom: 8 })
    .onClick(onClick)
  }

  @Builder
  TabBuilder(title: string, icon: Resource, index: number) {
    Column() {
      Image(icon)
        .width(24)
        .height(24)
        .fillColor(this.currentTabIndex === index ? '#1976D2' : '#999999')

      Text(title)
        .fontSize(12)
        .fontColor(this.currentTabIndex === index ? '#1976D2' : '#999999')
        .margin({ top: 4 })
    }
    .alignItems(HorizontalAlign.Center)
    .padding({ top: 8, bottom: 8 })
  }

  // 工具方法
  maskPhone(phone: string): string {
    if (!phone || phone.length !== 11) return phone;
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }

  getTransactionIcon(type: string): string {
    switch (type) {
      case '充值': return '⬇️';
      case '提现': return '⬆️';
      case '转账': return '💸';
      case '收钱': return '💰';
      case '支付': return '💳';
      case '退款': return '↩️';
      default: return '📄';
    }
  }

  formatAmount(transaction: Transaction): string {
    // 收入类型：充值、收钱、退款
    const isIncome = transaction.transactionType === TransactionType.RECHARGE ||
                     transaction.transactionType === TransactionType.RECEIVE ||
                     transaction.transactionType === TransactionType.REFUND;
    const prefix = isIncome ? '+' : '-';
    return `${prefix}¥${transaction.amount.toFixed(2)}`;
  }

  getAmountColor(transaction: Transaction): string {
    // 收入类型显示绿色，支出类型显示黑色
    const isIncome = transaction.transactionType === TransactionType.RECHARGE ||
                     transaction.transactionType === TransactionType.RECEIVE ||
                     transaction.transactionType === TransactionType.REFUND;
    return isIncome ? '#4CAF50' : '#333333';
  }

  getStatusColor(status: string): string {
    switch (status) {
      case '成功': return '#4CAF50';
      case '失败': return '#F44336';
      case '处理中': return '#FF9800';
      default: return '#666666';
    }
  }

  formatDateTime(dateTime: string): string {
    const date = new Date(dateTime);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false });
    } else if (diffDays === 1) {
      return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false });
    } else {
      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false });
    }
  }
}

/**
 * 认证错误类型
 */
interface AuthErrorType {
  code: number;
}
