// 用户数据初始化服务
import { ElMessage } from 'element-plus'
import bankCardApi from '@/api/bankCard'
import { bankApi } from '@/api/bank'
// 可以添加更多API导入，如交易记录、账户信息等

/**
 * 用户登录后初始化所有相关数据
 * @param {Object} userInfo - 用户信息
 * @returns {Object} - 包含用户所有数据的对象
 */
export async function initializeUserData(userInfo) {
  if (!userInfo || !userInfo.userId) {
    throw new Error('用户信息无效')
  }

  const userId = userInfo.userId
  console.log(`开始初始化用户 ${userId} 的数据...`)

  try {
    // 并行获取用户的所有数据
    const [
      bankCardsResult,
      accountInfoResult,
      transactionRecordsResult,
      bankListResult
    ] = await Promise.allSettled([
      getUserBankCards(userId),
      getUserAccountInfo(userId),
      getUserTransactionRecords(userId),
      getBankList()
    ])

    // 处理结果
    const userData = {
      userInfo,
      bankCards: bankCardsResult.status === 'fulfilled' ? bankCardsResult.value : [],
      accountInfo: accountInfoResult.status === 'fulfilled' ? accountInfoResult.value : null,
      transactionRecords: transactionRecordsResult.status === 'fulfilled' ? transactionRecordsResult.value : [],
      bankList: bankListResult.status === 'fulfilled' ? bankListResult.value : [],
      loadTime: new Date().toISOString()
    }

    // 记录加载结果
    console.log('用户数据初始化完成:', {
      userId,
      bankCardsCount: userData.bankCards.length,
      transactionRecordsCount: userData.transactionRecords.length,
      hasAccountInfo: !!userData.accountInfo,
      bankListCount: userData.bankList.length
    })

    // 保存到本地存储
    localStorage.setItem(`userData_${userId}`, JSON.stringify(userData))

    return userData
  } catch (error) {
    console.error('用户数据初始化失败:', error)
    throw error
  }
}

/**
 * 获取用户银行卡数据
 * @param {number} userId - 用户ID
 * @returns {Array} - 银行卡列表
 */
async function getUserBankCards(userId) {
  try {
    console.log(`正在获取用户 ${userId} 的银行卡数据...`)
    const response = await bankCardApi.getUserCards(userId)

    if (response && response.code === 0) {
      console.log(`成功获取到 ${response.data?.length || 0} 张银行卡`)
      return response.data || []
    } else {
      console.warn('API返回错误，尝试使用模拟数据:', response?.msg)
      // 只有在API明确返回错误时才使用模拟数据
      if (response && response.code !== 0) {
        return generateMockBankCards(userId)
      }
      return []
    }
  } catch (error) {
    console.error('银行卡API调用失败:', error)
    // 网络错误时使用模拟数据作为降级方案
    console.log('使用模拟数据作为降级方案')
    return generateMockBankCards(userId)
  }
}

/**
 * 获取用户账户信息
 * @param {number} userId - 用户ID
 * @returns {Object} - 账户信息
 */
async function getUserAccountInfo(userId) {
  try {
    // 这里可以调用账户信息API
    // const response = await accountApi.getUserAccount(userId)
    // if (response && response.code === 0) {
    //   return response.data
    // }
    
    // 暂时返回模拟数据
    return generateMockAccountInfo(userId)
  } catch (error) {
    console.warn('获取账户信息失败，使用模拟数据:', error)
    return generateMockAccountInfo(userId)
  }
}

/**
 * 获取用户交易记录
 * @param {number} userId - 用户ID
 * @returns {Array} - 交易记录列表
 */
async function getUserTransactionRecords(userId) {
  try {
    // 这里可以调用交易记录API
    // const response = await transactionApi.getUserTransactions(userId)
    // if (response && response.code === 0) {
    //   return response.data || []
    // }
    
    // 暂时返回模拟数据
    return generateMockTransactionRecords(userId)
  } catch (error) {
    console.warn('获取交易记录失败，使用模拟数据:', error)
    return generateMockTransactionRecords(userId)
  }
}

/**
 * 获取银行列表
 * @returns {Array} - 银行列表
 */
async function getBankList() {
  try {
    const response = await bankApi.getAllBanks()
    if (response && response.code === 0) {
      return response.data || []
    } else {
      return generateMockBankList()
    }
  } catch (error) {
    console.warn('获取银行列表失败，使用模拟数据:', error)
    return generateMockBankList()
  }
}

/**
 * 生成用户专属的模拟银行卡数据
 * @param {number} userId - 用户ID
 * @returns {Array} - 模拟银行卡数据
 */
function generateMockBankCards(userId) {
  const userSuffix = userId.toString().padStart(3, '0')
  
  return [
    {
      cardId: parseInt(`${userId}001`),
      userId: userId,
      bankName: '招商银行',
      cardNumber: `*************${userSuffix}`,
      cardType: 1,
      cardHolder: `用户${userId}`,
      balance: 15680.50 + (userId * 1000),
      status: 1,
      isDefault: 1,
      isBound: true,
      creditLimit: 0,
      phone: `**********${userId}`
    },
    {
      cardId: parseInt(`${userId}002`),
      userId: userId,
      bankName: '建设银行',
      cardNumber: `*************${userSuffix}`,
      cardType: 2,
      cardHolder: `用户${userId}`,
      balance: 0,
      status: 1,
      isDefault: 0,
      isBound: true,
      creditLimit: 50000 + (userId * 10000),
      phone: `**********${userId}`
    },
    {
      cardId: parseInt(`${userId}003`),
      userId: userId,
      bankName: '工商银行',
      cardNumber: `*************${userSuffix}`,
      cardType: 1,
      cardHolder: `用户${userId}`,
      balance: 8520.30 + (userId * 500),
      status: 1,
      isDefault: 0,
      isBound: true,
      creditLimit: 0,
      phone: `**********${userId}`
    }
  ]
}

/**
 * 生成用户专属的模拟账户信息
 * @param {number} userId - 用户ID
 * @returns {Object} - 模拟账户信息
 */
function generateMockAccountInfo(userId) {
  return {
    userId: userId,
    accountId: `ACC${userId.toString().padStart(6, '0')}`,
    accountName: `用户${userId}的钱包账户`,
    balance: 25000 + (userId * 2000),
    frozenAmount: 0,
    availableAmount: 25000 + (userId * 2000),
    accountStatus: 1, // 1-正常，0-冻结
    createTime: new Date(Date.now() - (userId * ********)).toISOString(), // 不同用户不同创建时间
    lastUpdateTime: new Date().toISOString()
  }
}

/**
 * 生成用户专属的模拟交易记录
 * @param {number} userId - 用户ID
 * @returns {Array} - 模拟交易记录
 */
function generateMockTransactionRecords(userId) {
  const records = []
  const baseTime = Date.now()
  
  for (let i = 0; i < 10; i++) {
    records.push({
      transactionId: `TXN${userId}${i.toString().padStart(3, '0')}`,
      userId: userId,
      type: i % 3 === 0 ? 'recharge' : (i % 3 === 1 ? 'withdraw' : 'transfer'),
      amount: (Math.random() * 1000 + 100).toFixed(2),
      description: `交易记录 ${i + 1}`,
      status: 1, // 1-成功，0-失败，2-处理中
      createTime: new Date(baseTime - (i * 3600000)).toISOString(), // 每小时一条记录
      fromAccount: `用户${userId}账户`,
      toAccount: i % 2 === 0 ? '商户账户' : '其他用户账户'
    })
  }
  
  return records
}

/**
 * 生成模拟银行列表
 * @returns {Array} - 银行列表
 */
function generateMockBankList() {
  return [
    { bankId: 1, bankName: '招商银行', bankCode: 'CMB' },
    { bankId: 2, bankName: '建设银行', bankCode: 'CCB' },
    { bankId: 3, bankName: '工商银行', bankCode: 'ICBC' },
    { bankId: 4, bankName: '农业银行', bankCode: 'ABC' },
    { bankId: 5, bankName: '中国银行', bankCode: 'BOC' },
    { bankId: 6, bankName: '交通银行', bankCode: 'BOCOM' },
    { bankId: 7, bankName: '邮储银行', bankCode: 'PSBC' },
    { bankId: 8, bankName: '民生银行', bankCode: 'CMBC' },
    { bankId: 9, bankName: '光大银行', bankCode: 'CEB' },
    { bankId: 10, bankName: '平安银行', bankCode: 'PAB' }
  ]
}

/**
 * 从本地存储获取用户数据
 * @param {number} userId - 用户ID
 * @returns {Object|null} - 用户数据或null
 */
export function getUserDataFromStorage(userId) {
  try {
    const data = localStorage.getItem(`userData_${userId}`)
    return data ? JSON.parse(data) : null
  } catch (error) {
    console.error('从本地存储获取用户数据失败:', error)
    return null
  }
}

/**
 * 清除用户数据
 * @param {number} userId - 用户ID
 */
export function clearUserData(userId) {
  if (userId) {
    localStorage.removeItem(`userData_${userId}`)
  }
  // 清除所有用户数据的模式匹配
  Object.keys(localStorage).forEach(key => {
    if (key.startsWith('userData_')) {
      localStorage.removeItem(key)
    }
  })
}

/**
 * 刷新用户数据
 * @param {Object} userInfo - 用户信息
 * @returns {Object} - 更新后的用户数据
 */
export async function refreshUserData(userInfo) {
  if (!userInfo || !userInfo.userId) {
    throw new Error('用户信息无效')
  }
  
  // 清除旧数据
  clearUserData(userInfo.userId)
  
  // 重新初始化
  return await initializeUserData(userInfo)
}

export default {
  initializeUserData,
  getUserDataFromStorage,
  clearUserData,
  refreshUserData
}
