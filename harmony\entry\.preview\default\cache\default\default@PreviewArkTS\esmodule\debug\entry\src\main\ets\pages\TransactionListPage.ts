if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface TransactionListPage_Params {
    transactions?: Transaction[];
    isLoading?: boolean;
    isRefreshing?: boolean;
    hasMore?: boolean;
    currentPage?: number;
    pageSize?: number;
    selectedType?: string;
    showFilterDialog?: boolean;
    filterType?: string;
    filterStartDate?: string;
    filterEndDate?: string;
    transactionTypes?: string[];
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { TransactionType, TransactionStatus, PaymentMethod } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { Transaction, SpringBootTransactionResponse } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
class TransactionListPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__transactions = new ObservedPropertyObjectPU([], this, "transactions");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__isRefreshing = new ObservedPropertySimplePU(false, this, "isRefreshing");
        this.__hasMore = new ObservedPropertySimplePU(true, this, "hasMore");
        this.__currentPage = new ObservedPropertySimplePU(1, this, "currentPage");
        this.__pageSize = new ObservedPropertySimplePU(20, this, "pageSize");
        this.__selectedType = new ObservedPropertySimplePU('全部交易', this, "selectedType");
        this.__showFilterDialog = new ObservedPropertySimplePU(false, this, "showFilterDialog");
        this.__filterType = new ObservedPropertySimplePU('', this, "filterType");
        this.__filterStartDate = new ObservedPropertySimplePU('', this, "filterStartDate");
        this.__filterEndDate = new ObservedPropertySimplePU('', this, "filterEndDate");
        this.transactionTypes = ['全部交易', '支付记录', '充值记录', '提现记录', '转账记录', '收钱记录'];
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: TransactionListPage_Params) {
        if (params.transactions !== undefined) {
            this.transactions = params.transactions;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.isRefreshing !== undefined) {
            this.isRefreshing = params.isRefreshing;
        }
        if (params.hasMore !== undefined) {
            this.hasMore = params.hasMore;
        }
        if (params.currentPage !== undefined) {
            this.currentPage = params.currentPage;
        }
        if (params.pageSize !== undefined) {
            this.pageSize = params.pageSize;
        }
        if (params.selectedType !== undefined) {
            this.selectedType = params.selectedType;
        }
        if (params.showFilterDialog !== undefined) {
            this.showFilterDialog = params.showFilterDialog;
        }
        if (params.filterType !== undefined) {
            this.filterType = params.filterType;
        }
        if (params.filterStartDate !== undefined) {
            this.filterStartDate = params.filterStartDate;
        }
        if (params.filterEndDate !== undefined) {
            this.filterEndDate = params.filterEndDate;
        }
        if (params.transactionTypes !== undefined) {
            this.transactionTypes = params.transactionTypes;
        }
    }
    updateStateVars(params: TransactionListPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__transactions.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__isRefreshing.purgeDependencyOnElmtId(rmElmtId);
        this.__hasMore.purgeDependencyOnElmtId(rmElmtId);
        this.__currentPage.purgeDependencyOnElmtId(rmElmtId);
        this.__pageSize.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedType.purgeDependencyOnElmtId(rmElmtId);
        this.__showFilterDialog.purgeDependencyOnElmtId(rmElmtId);
        this.__filterType.purgeDependencyOnElmtId(rmElmtId);
        this.__filterStartDate.purgeDependencyOnElmtId(rmElmtId);
        this.__filterEndDate.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__transactions.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__isRefreshing.aboutToBeDeleted();
        this.__hasMore.aboutToBeDeleted();
        this.__currentPage.aboutToBeDeleted();
        this.__pageSize.aboutToBeDeleted();
        this.__selectedType.aboutToBeDeleted();
        this.__showFilterDialog.aboutToBeDeleted();
        this.__filterType.aboutToBeDeleted();
        this.__filterStartDate.aboutToBeDeleted();
        this.__filterEndDate.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __transactions: ObservedPropertyObjectPU<Transaction[]>;
    get transactions() {
        return this.__transactions.get();
    }
    set transactions(newValue: Transaction[]) {
        this.__transactions.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __isRefreshing: ObservedPropertySimplePU<boolean>;
    get isRefreshing() {
        return this.__isRefreshing.get();
    }
    set isRefreshing(newValue: boolean) {
        this.__isRefreshing.set(newValue);
    }
    private __hasMore: ObservedPropertySimplePU<boolean>;
    get hasMore() {
        return this.__hasMore.get();
    }
    set hasMore(newValue: boolean) {
        this.__hasMore.set(newValue);
    }
    private __currentPage: ObservedPropertySimplePU<number>;
    get currentPage() {
        return this.__currentPage.get();
    }
    set currentPage(newValue: number) {
        this.__currentPage.set(newValue);
    }
    private __pageSize: ObservedPropertySimplePU<number>;
    get pageSize() {
        return this.__pageSize.get();
    }
    set pageSize(newValue: number) {
        this.__pageSize.set(newValue);
    }
    private __selectedType: ObservedPropertySimplePU<string>; // 当前选中的交易类型
    get selectedType() {
        return this.__selectedType.get();
    }
    set selectedType(newValue: string) {
        this.__selectedType.set(newValue);
    }
    private __showFilterDialog: ObservedPropertySimplePU<boolean>;
    get showFilterDialog() {
        return this.__showFilterDialog.get();
    }
    set showFilterDialog(newValue: boolean) {
        this.__showFilterDialog.set(newValue);
    }
    // 筛选条件
    private __filterType: ObservedPropertySimplePU<string>;
    get filterType() {
        return this.__filterType.get();
    }
    set filterType(newValue: string) {
        this.__filterType.set(newValue);
    }
    private __filterStartDate: ObservedPropertySimplePU<string>;
    get filterStartDate() {
        return this.__filterStartDate.get();
    }
    set filterStartDate(newValue: string) {
        this.__filterStartDate.set(newValue);
    }
    private __filterEndDate: ObservedPropertySimplePU<string>;
    get filterEndDate() {
        return this.__filterEndDate.get();
    }
    set filterEndDate(newValue: string) {
        this.__filterEndDate.set(newValue);
    }
    // 交易类型选项 - 按照用户要求的5种记录类型
    private transactionTypes: string[];
    aboutToAppear() {
        this.loadTransactions(true);
    }
    async loadTransactions(refresh: boolean = false) {
        if (this.isLoading)
            return;
        if (refresh) {
            this.currentPage = 1;
            this.hasMore = true;
            this.isRefreshing = true;
        }
        else {
            this.isLoading = true;
        }
        try {
            // 从本地存储获取当前用户ID
            const cachedUserInfo = await storageManager.getUserInfo();
            if (!cachedUserInfo || !cachedUserInfo.userId) {
                console.error('无法获取用户ID');
                promptAction.showToast({ message: '用户信息获取失败' });
                return;
            }
            // 调用SpringBoot3后端API获取交易记录
            let url = `/transaction/user/${cachedUserInfo.userId}?page=${this.currentPage}&size=${this.pageSize}`;
            // 添加类型筛选 - 按照5种记录类型映射
            if (this.selectedType && this.selectedType !== '全部交易') {
                const typeMap: Record<string, number> = {
                    '支付记录': 5,
                    '充值记录': 1,
                    '提现记录': 2,
                    '转账记录': 3,
                    '收钱记录': 4 // 收款
                };
                const typeId = typeMap[this.selectedType];
                if (typeId) {
                    url += `&type=${typeId}`;
                }
            }
            const response = await httpClient.get<SpringBootTransactionResponse[]>(url);
            const transactionList: SpringBootTransactionResponse[] = response.data;
            // 转换为本地格式
            const localTransactions = this.convertSpringBootTransactionsToLocal(transactionList);
            if (refresh) {
                this.transactions = localTransactions;
            }
            else {
                this.transactions = [...this.transactions, ...localTransactions];
            }
            // 简单分页处理
            this.hasMore = localTransactions.length === this.pageSize;
            this.currentPage++;
        }
        catch (error) {
            console.error('获取交易记录失败:', error);
            promptAction.showToast({ message: '获取交易记录失败' });
        }
        finally {
            this.isLoading = false;
            this.isRefreshing = false;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(95:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(97:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(98:9)", "entry");
            Button.width(40);
            Button.height(40);
            Button.borderRadius(20);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777238, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(99:11)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#333333');
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('交易记录');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(112:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('查看全部');
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(119:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#1976D2');
            Text.onClick(() => {
                this.viewAllTransactions();
            });
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        // 交易类型筛选标签栏
        this.TransactionTypeTabsView.bind(this)();
        // 交易记录列表
        this.TransactionListView.bind(this)();
        Column.pop();
    }
    TransactionTypeTabsView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(144:5)", "entry");
            Row.width('100%');
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const type = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Button.createWithLabel(type);
                    Button.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(146:9)", "entry");
                    Button.fontSize(14);
                    Button.fontColor(this.selectedType === type ? '#FFFFFF' : '#666666');
                    Button.backgroundColor(this.selectedType === type ? '#1976D2' : '#F8F9FA');
                    Button.borderRadius(20);
                    Button.height(36);
                    Button.padding({ left: 16, right: 16 });
                    Button.margin({ right: 8 });
                    Button.onClick(() => {
                        this.selectedType = type;
                        this.loadTransactions(true);
                    });
                }, Button);
                Button.pop();
            };
            this.forEachUpdateFunction(elmtId, this.transactionTypes, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        Row.pop();
    }
    TransactionListView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.transactions.length === 0 && !this.isLoading) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(168:7)", "entry");
                        Column.width('100%');
                        Column.layoutWeight(1);
                        Column.justifyContent(FlexAlign.Center);
                        Column.alignItems(HorizontalAlign.Center);
                        Column.backgroundColor('#F5F5F5');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('📄');
                        Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(169:9)", "entry");
                        Text.fontSize(60);
                        Text.fontColor('#CCCCCC');
                        Text.margin({ bottom: 16 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无交易记录');
                        Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(174:9)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#999999');
                        Text.margin({ bottom: 20 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('去充值');
                        Button.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(179:9)", "entry");
                        Button.fontSize(14);
                        Button.fontColor('#1976D2');
                        Button.backgroundColor('#E3F2FD');
                        Button.borderRadius(8);
                        Button.padding({ left: 20, right: 20, top: 8, bottom: 8 });
                        Button.onClick(() => {
                            router.pushUrl({ url: 'pages/RechargePage' });
                        });
                    }, Button);
                    Button.pop();
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        List.create();
                        List.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(195:7)", "entry");
                        List.layoutWeight(1);
                        List.backgroundColor('#F5F5F5');
                        List.padding({ left: 16, right: 16, top: 8 });
                        List.onReachEnd(() => {
                            if (this.hasMore && !this.isLoading) {
                                this.loadTransactions(false);
                            }
                        });
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = (_item, index: number) => {
                            const transaction = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                    ListItem.onClick(() => {
                                        this.viewTransactionDetail(transaction);
                                    });
                                    ListItem.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(197:11)", "entry");
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.TransactionItem.bind(this)(transaction);
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.transactions, forEachItemGenFunction, undefined, true, false);
                    }, ForEach);
                    ForEach.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 加载更多
                        if (this.hasMore) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                {
                                    const itemCreation = (elmtId, isInitialRender) => {
                                        ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                        itemCreation2(elmtId, isInitialRender);
                                        if (!isInitialRender) {
                                            ListItem.pop();
                                        }
                                        ViewStackProcessor.StopGetAccessRecording();
                                    };
                                    const itemCreation2 = (elmtId, isInitialRender) => {
                                        ListItem.create(deepRenderFunction, true);
                                        ListItem.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(207:11)", "entry");
                                    };
                                    const deepRenderFunction = (elmtId, isInitialRender) => {
                                        itemCreation(elmtId, isInitialRender);
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Row.create();
                                            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(208:13)", "entry");
                                            Row.width('100%');
                                            Row.height(50);
                                            Row.justifyContent(FlexAlign.Center);
                                            Row.onClick(() => {
                                                if (!this.isLoading) {
                                                    this.loadTransactions(false);
                                                }
                                            });
                                        }, Row);
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            If.create();
                                            if (this.isLoading) {
                                                this.ifElseBranchUpdateFunction(0, () => {
                                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                                        LoadingProgress.create();
                                                        LoadingProgress.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(210:17)", "entry");
                                                        LoadingProgress.width(20);
                                                        LoadingProgress.height(20);
                                                        LoadingProgress.color('#1976D2');
                                                        LoadingProgress.margin({ right: 8 });
                                                    }, LoadingProgress);
                                                });
                                            }
                                            else {
                                                this.ifElseBranchUpdateFunction(1, () => {
                                                });
                                            }
                                        }, If);
                                        If.pop();
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(this.isLoading ? '加载中...' : '点击加载更多');
                                            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(216:15)", "entry");
                                            Text.fontSize(14);
                                            Text.fontColor('#666666');
                                        }, Text);
                                        Text.pop();
                                        Row.pop();
                                        ListItem.pop();
                                    };
                                    this.observeComponentCreation2(itemCreation2, ListItem);
                                    ListItem.pop();
                                }
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    List.pop();
                });
            }
        }, If);
        If.pop();
    }
    TransactionItem(transaction: Transaction, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(244:5)", "entry");
            Row.width('100%');
            Row.padding(16);
            Row.margin({ bottom: 1 });
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 交易类型图标
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(246:7)", "entry");
            // 交易类型图标
            Column.width(40);
            // 交易类型图标
            Column.height(40);
            // 交易类型图标
            Column.justifyContent(FlexAlign.Center);
            // 交易类型图标
            Column.backgroundColor(this.getTransactionIconBg(transaction.transactionType));
            // 交易类型图标
            Column.borderRadius(20);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTransactionIcon(transaction.transactionType));
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(247:9)", "entry");
            Text.fontSize(20);
            Text.fontColor('#FFFFFF');
        }, Text);
        Text.pop();
        // 交易类型图标
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(257:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ left: 12 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(258:9)", "entry");
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTransactionTypeText(transaction.transactionType));
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(259:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatAmount(transaction));
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(265:11)", "entry");
            Text.fontSize(16);
            Text.fontColor(this.getAmountColor(transaction));
            Text.fontWeight(FontWeight.Medium);
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(272:9)", "entry");
            Row.width('100%');
            Row.margin({ top: 4 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.formatDateTime(transaction.createTime));
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(273:11)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.getTransactionStatusText(transaction.status));
            Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(278:11)", "entry");
            Text.fontSize(12);
            Text.fontColor(this.getStatusColor(transaction.status));
        }, Text);
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (transaction.description) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(transaction.description);
                        Text.debugLine("entry/src/main/ets/pages/TransactionListPage.ets(286:11)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#666666');
                        Text.maxLines(1);
                        Text.textOverflow({ overflow: TextOverflow.Ellipsis });
                        Text.width('100%');
                        Text.margin({ top: 4 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
        Row.pop();
    }
    getTransactionIcon(type: TransactionType): string {
        switch (type) {
            case TransactionType.RECHARGE: return '💰'; // 充值记录
            case TransactionType.WITHDRAW: return '💸'; // 提现记录
            case TransactionType.TRANSFER: return '📤'; // 转账记录
            case TransactionType.RECEIVE: return '📥'; // 收钱记录
            case TransactionType.PAYMENT: return '💳'; // 支付记录
            case TransactionType.REFUND: return '↩️'; // 退款
            default: return '📄';
        }
    }
    getTransactionIconBg(type: TransactionType): string {
        switch (type) {
            case TransactionType.RECHARGE: return '#4CAF50';
            case TransactionType.WITHDRAW: return '#2196F3';
            case TransactionType.TRANSFER: return '#FF9800';
            case TransactionType.RECEIVE: return '#4CAF50';
            case TransactionType.PAYMENT: return '#9C27B0';
            case TransactionType.REFUND: return '#607D8B';
            default: return '#999999';
        }
    }
    formatAmount(transaction: Transaction): string {
        // 收入类型：充值、收款、退款
        const isIncome = transaction.transactionType === TransactionType.RECHARGE ||
            transaction.transactionType === TransactionType.RECEIVE ||
            transaction.transactionType === TransactionType.REFUND;
        const prefix = isIncome ? '+' : '-';
        return `${prefix}¥${transaction.amount.toFixed(2)}`;
    }
    getAmountColor(transaction: Transaction): string {
        // 收入类型显示绿色，支出类型显示黑色
        const isIncome = transaction.transactionType === TransactionType.RECHARGE ||
            transaction.transactionType === TransactionType.RECEIVE ||
            transaction.transactionType === TransactionType.REFUND;
        return isIncome ? '#4CAF50' : '#333333';
    }
    getStatusColor(status: TransactionStatus): string {
        switch (status) {
            case TransactionStatus.SUCCESS: return '#4CAF50';
            case TransactionStatus.FAILED: return '#F44336';
            case TransactionStatus.PENDING: return '#FF9800';
            default: return '#666666';
        }
    }
    /**
     * 获取交易类型显示文本
     */
    getTransactionTypeText(type: TransactionType): string {
        switch (type) {
            case TransactionType.RECHARGE: return '充值记录';
            case TransactionType.WITHDRAW: return '提现记录';
            case TransactionType.TRANSFER: return '转账记录';
            case TransactionType.RECEIVE: return '收钱记录';
            case TransactionType.PAYMENT: return '支付记录';
            case TransactionType.REFUND: return '退款记录';
            default: return '其他记录';
        }
    }
    /**
     * 获取交易状态显示文本
     */
    getTransactionStatusText(status: TransactionStatus): string {
        switch (status) {
            case TransactionStatus.SUCCESS: return '成功';
            case TransactionStatus.FAILED: return '失败';
            case TransactionStatus.PENDING: return '处理中';
            default: return '未知';
        }
    }
    formatDateTime(dateTime: string): string {
        const date = new Date(dateTime);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        if (diffDays === 0) {
            return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false });
        }
        else if (diffDays === 1) {
            return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false });
        }
        else {
            return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false });
        }
    }
    viewTransactionDetail(transaction: Transaction) {
        router.pushUrl({
            url: 'pages/TransactionDetailPage',
            params: {
                transactionId: transaction.transactionId
            }
        });
    }
    viewAllTransactions() {
        // 查看全部交易记录
        this.selectedType = '全部交易';
        this.loadTransactions(true);
    }
    /**
     * 转换SpringBoot3交易记录到本地格式
     */
    private convertSpringBootTransactionsToLocal(transactionList: SpringBootTransactionResponse[]): Transaction[] {
        return transactionList.map((transaction: SpringBootTransactionResponse): Transaction => ({
            transactionId: transaction.transactionId || 0,
            transactionNo: transaction.transactionNo || '',
            fromUserId: transaction.fromUserId || 0,
            toUserId: transaction.toUserId || 0,
            amount: transaction.amount || 0,
            transactionType: this.mapTransactionType(transaction.type),
            paymentMethod: this.mapPaymentMethod(transaction.paymentMethod),
            description: transaction.description || '',
            status: this.mapTransactionStatus(transaction.status),
            createTime: transaction.createdAt || transaction.createTime || ''
        }));
    }
    /**
     * 映射交易类型
     */
    private mapTransactionType(type: number | undefined): TransactionType {
        switch (type) {
            case 1: return TransactionType.RECHARGE;
            case 2: return TransactionType.WITHDRAW;
            case 3: return TransactionType.TRANSFER;
            case 4: return TransactionType.RECEIVE;
            case 5: return TransactionType.PAYMENT;
            default: return TransactionType.PAYMENT;
        }
    }
    /**
     * 映射支付方式
     */
    private mapPaymentMethod(method: number | undefined): PaymentMethod {
        switch (method) {
            case 1: return PaymentMethod.WALLET;
            case 2: return PaymentMethod.BANK_CARD;
            case 3: return PaymentMethod.THIRD_PARTY;
            case 4: return PaymentMethod.THIRD_PARTY;
            default: return PaymentMethod.WALLET;
        }
    }
    /**
     * 映射交易状态
     */
    private mapTransactionStatus(status: number | undefined): TransactionStatus {
        switch (status) {
            case 1: return TransactionStatus.SUCCESS;
            case 0: return TransactionStatus.FAILED;
            case 2: return TransactionStatus.PENDING;
            default: return TransactionStatus.SUCCESS;
        }
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "TransactionListPage";
    }
}
registerNamedRoute(() => new TransactionListPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/TransactionListPage", pageFullPath: "entry/src/main/ets/pages/TransactionListPage", integratedHsp: "false", moduleType: "followWithHap" });
