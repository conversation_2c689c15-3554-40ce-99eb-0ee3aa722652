import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { UserApi } from '../api/UserApi';
import { storageManager, LocalUserInfo } from '../common/storage/StorageManager';
import { UpdatePayPasswordRequest, UpdatePayLimitRequest, UserInfo } from '../common/types/index';
import { httpClient } from '../common/http/HttpClient';

@Entry
@Component
struct SettingsPage {
  @State userInfo: LocalUserInfo | null = null;
  @State showLogoutDialog: boolean = false;
  @State showUserSwitchDialog: boolean = false;
  @State showSecuritySettings: boolean = false;

  aboutToAppear() {
    this.loadUserInfo();
  }

  onPageShow() {
    // 页面显示时重新加载用户信息，确保数据是最新的
    console.log('设置页面显示，重新加载用户信息');
    this.loadUserInfo();
  }

  async loadUserInfo(): Promise<void> {
    try {
      // 直接从本地存储获取用户信息
      const cachedUserInfo = await storageManager.getUserInfo();
      if (cachedUserInfo) {
        this.userInfo = cachedUserInfo;
        console.log('设置页面加载用户信息，支付限额:', this.userInfo.payLimit);
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  }

  build() {
    Stack() {
      // 主要内容
      Column() {
      // 顶部导航栏
      Row() {
        Button('返回')
          .fontSize(16)
          .fontColor('#1976D2')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            router.back();
          })

        Text('设置')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(60) // 占位，保持标题居中
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')

      Scroll() {
        Column() {
          // 个人信息
          if (this.userInfo) {
            Column() {
              Text('个人信息')
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 16 })

              this.InfoItem('真实姓名', this.maskRealName(this.userInfo.realName))
              this.InfoItem('手机号', this.maskPhone(this.userInfo.phone))
              this.InfoItem('身份证号', this.maskIdCard(this.userInfo.idCard))
              this.InfoItem('注册时间', this.formatDate(this.userInfo.createTime))
            }
            .width('100%')
            .padding(20)
            .margin({ top: 16 })
            .borderRadius(12)
            .backgroundColor('#FFFFFF')
          }

          // 安全设置
          Column() {
            Text('安全设置')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 16 })

            this.SettingItem('修改支付密码', '保护您的资金安全', () => {
              router.pushUrl({ url: 'pages/ChangePayPasswordPage' });
            })

            this.SettingItem('支付限额', '设置您的限额', () => {
              router.pushUrl({ url: 'pages/PayLimitSettingPage' });
            })

            this.SettingItem('银行卡管理', '管理您的银行卡', () => {
              router.pushUrl({ url: 'pages/BankCardPage' });
            })

            this.SettingItem('安全设置', '账户安全管理', () => {
              this.showSecuritySettings = true;
            })

            this.SettingItem('用户切换', '切换登录账户', () => {
              this.showUserSwitchDialog = true;
            })
          }
          .width('100%')
          .padding(20)
          .margin({ top: 16 })
          .borderRadius(12)
          .backgroundColor('#FFFFFF')

          // 退出登录
          Button('退出登录')
            .width('100%')
            .height(48)
            .fontSize(16)
            .fontColor('#F44336')
            .backgroundColor('#FFFFFF')
            .borderRadius(8)
            .border({ width: 1, color: '#F44336' })
            .margin({ top: 24, bottom: 20 })
            .onClick(() => {
              this.showLogoutDialog = true;
            })
        }
        .padding({ left: 16, right: 16 })
      }
      .layoutWeight(1)
      .backgroundColor('#F5F5F5')
    }
      .width('100%')
      .height('100%')
      .backgroundColor('#F5F5F5')
      .bindSheet($$this.showSecuritySettings, this.SecuritySettingsDialog(), {
        height: 500,
        showClose: true,
        dragBar: true,
        onDisappear: () => {
          this.showSecuritySettings = false;
        }
      })
      .bindSheet($$this.showUserSwitchDialog, this.UserSwitchDialog(), {
        height: 200,
        showClose: true,
        dragBar: true,
        onDisappear: () => {
          this.showUserSwitchDialog = false;
        }
      })

      // 退出登录确认弹窗
      if (this.showLogoutDialog) {
        Column() {
          // 遮罩层
          Column()
            .width('100%')
            .height('100%')
            .backgroundColor('#80000000')
            .onClick(() => {
              this.showLogoutDialog = false;
            })

          // 弹窗内容
          Column() {
            Text('确认退出')
              .fontSize(18)
              .fontWeight(FontWeight.Medium)
              .fontColor('#333333')
              .margin({ bottom: 16 })

            Text('确定要退出登录吗？')
              .fontSize(14)
              .fontColor('#666666')
              .textAlign(TextAlign.Center)
              .margin({ bottom: 24 })

            Row() {
              Button('取消')
                .fontSize(16)
                .fontColor('#666666')
                .backgroundColor('#F8F9FA')
                .borderRadius(8)
                .layoutWeight(1)
                .height(44)
                .onClick(() => {
                  this.showLogoutDialog = false;
                })

              Button('确认退出')
                .fontSize(16)
                .fontColor('#FFFFFF')
                .backgroundColor('#F44336')
                .borderRadius(8)
                .layoutWeight(1)
                .height(44)
                .margin({ left: 12 })
                .onClick(() => {
                  this.showLogoutDialog = false;
                  this.handleLogout();
                })
            }
            .width('100%')
          }
          .width('280vp')
          .padding(24)
          .borderRadius(16)
          .backgroundColor('#FFFFFF')
          .shadow({
            radius: 16,
            color: '#40000000',
            offsetX: 0,
            offsetY: 8
          })
          .position({ x: '50%', y: '50%' })
          .translate({ x: '-50%', y: '-50%' })
        }
        .width('100%')
        .height('100%')
        .position({ x: 0, y: 0 })
        .zIndex(1000)
      }
    }
    .width('100%')
    .height('100%')
  }

  @Builder
  InfoItem(label: string, value: string) {
    Row() {
      Text(label)
        .fontSize(14)
        .fontColor('#666666')
        .width(80)

      Text(value)
        .fontSize(14)
        .fontColor('#333333')
        .layoutWeight(1)
        .textAlign(TextAlign.End)
    }
    .width('100%')
    .height(44)
    .justifyContent(FlexAlign.SpaceBetween)
    .alignItems(VerticalAlign.Center)
  }

  @Builder
  SettingItem(title: string, subtitle: string, onClick: () => void) {
    Row() {
      Column() {
        Text(title)
          .fontSize(16)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)

        Text(subtitle)
          .fontSize(12)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)

      Text('>')
        .fontSize(16)
        .fontColor('#CCCCCC')
    }
    .width('100%')
    .height(60)
    .padding({ top: 8, bottom: 8 })
    .justifyContent(FlexAlign.SpaceBetween)
    .alignItems(VerticalAlign.Center)
    .onClick(onClick)
  }







  // 工具方法
  maskRealName(name: string): string {
    if (!name || name.length < 2) return name;
    if (name.length === 2) {
      return name.charAt(0) + '*';
    } else {
      return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1);
    }
  }

  maskPhone(phone: string): string {
    if (!phone || phone.length !== 11) return phone;
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }

  maskIdCard(idCard: string): string {
    if (!idCard || idCard.length < 8) return idCard;
    if (idCard.length === 18) {
      return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
    }
    return idCard;
  }

  formatDate(dateTime: string): string {
    const date = new Date(dateTime);
    return date.toLocaleDateString('zh-CN');
  }









  async handleLogout() {
    try {
      // 清除本地数据
      await storageManager.clearUserData();
      httpClient.clearAuthToken();
      
      promptAction.showToast({ message: '已退出登录' });
      
      // 跳转到登录页
      router.replaceUrl({ url: 'pages/LoginPage' });

    } catch (error) {
      console.error('退出登录失败:', error);
      promptAction.showToast({ message: '退出登录失败' });
    }
  }

  @Builder
  SecuritySettingsDialog() {
    Column() {
      Text('安全设置')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 20 })

      Column() {
        this.SecurityItem('登录密码', '修改登录密码', () => {
          promptAction.showToast({ message: '跳转到修改登录密码页面' });
        })

        this.SecurityItem('支付密码', '修改支付密码', () => {
          router.pushUrl({ url: 'pages/ChangePayPasswordPage' });
        })

        this.SecurityItem('手机绑定', '更换绑定手机号', () => {
          promptAction.showToast({ message: '手机绑定功能开发中' });
        })

        this.SecurityItem('风险提醒', '账户安全提醒设置', () => {
          promptAction.showToast({ message: '风险提醒设置功能开发中' });
        })

        this.SecurityItem('设备管理', '管理登录设备', () => {
          promptAction.showToast({ message: '设备管理功能开发中' });
        })
      }
      .width('100%')

      Button('关闭')
        .width('100%')
        .height(48)
        .fontSize(16)
        .fontColor('#FFFFFF')
        .backgroundColor('#1976D2')
        .borderRadius(8)
        .margin({ top: 20 })
        .onClick(() => {
          this.showSecuritySettings = false;
        })
    }
    .width('100%')
    .padding(20)
  }

  @Builder
  SecurityItem(title: string, subtitle: string, onClick: () => void) {
    Row() {
      Column() {
        Text(title)
          .fontSize(16)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .alignSelf(ItemAlign.Start)

        Text(subtitle)
          .fontSize(14)
          .fontColor('#666666')
          .alignSelf(ItemAlign.Start)
          .margin({ top: 4 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)

      Text('>')
        .fontSize(16)
        .fontColor('#999999')
    }
    .width('100%')
    .padding({ top: 12, bottom: 12 })
    .onClick(onClick)
  }

  @Builder
  UserSwitchDialog() {
    Column() {
      Text('用户切换')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 20 })

      Text('确定要切换用户吗？')
        .fontSize(16)
        .fontColor('#666666')
        .margin({ bottom: 20 })

      Row() {
        Button('取消')
          .fontSize(14)
          .fontColor('#666666')
          .backgroundColor('#F5F5F5')
          .borderRadius(8)
          .layoutWeight(1)
          .margin({ right: 8 })
          .onClick(() => {
            this.showUserSwitchDialog = false;
          })

        Button('确定')
          .fontSize(14)
          .fontColor('#FFFFFF')
          .backgroundColor('#1976D2')
          .borderRadius(8)
          .layoutWeight(1)
          .margin({ left: 8 })
          .onClick(() => {
            this.switchUser();
          })
      }
      .width('100%')
    }
    .width('100%')
    .padding(20)
  }

  async switchUser() {
    try {
      // 清除当前用户数据
      await storageManager.clearUserData();
      httpClient.clearAuthToken();

      this.showUserSwitchDialog = false;
      promptAction.showToast({ message: '已切换用户，请重新登录' });

      // 跳转到登录页面
      router.replaceUrl({ url: 'pages/LoginPage' });
    } catch (error) {
      console.error('切换用户失败:', error);
      promptAction.showToast({ message: '切换用户失败' });
    }
  }
}






