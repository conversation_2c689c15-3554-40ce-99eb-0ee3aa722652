/**
 * 类型检查测试文件
 * 用于验证所有类型定义是否正确
 */

import { httpClient, ApiResponse } from '../common/http/HttpClient';
import { storageManager, UserInfo, WalletInfo } from '../common/storage/StorageManager';
import { UserApi, UserLoginRequest, UserRegisterRequest } from '../api/UserApi';
import { WalletApi, WalletRechargeRequest, WalletWithdrawRequest, WalletTransferRequest } from '../api/WalletApi';
import { BankCardApi, BankCardBindRequest, BankCard } from '../api/BankCardApi';
import { TransactionApi, TransactionQueryParams, Transaction, PageResult } from '../api/TransactionApi';

/**
 * 类型检查函数
 * 这个函数不会被实际调用，只是用来验证类型定义
 */
function typeCheck() {
  // HTTP客户端类型检查
  const client = httpClient;
  client.setBaseUrl('http://localhost:8080/api');
  client.setTimeout(10000);
  client.setAuthToken('test-token');
  client.clearAuthToken();

  // 存储管理器类型检查
  const storage = storageManager;
  
  // 用户信息类型检查
  const userInfo: UserInfo = {
    userId: 1,
    phone: '***********',
    realName: '张三',
    idCard: '110101199001011234',
    walletNo: 'W123456789',
    balance: 1000.00,
    payLimit: 5000.00,
    status: 1,
    createTime: '2025-06-18 10:00:00',
    updateTime: '2025-06-18 10:00:00'
  };

  // 钱包信息类型检查
  const walletInfo: WalletInfo = {
    walletNo: 'W123456789',
    balance: 1000.00,
    status: 1
  };

  // 用户登录请求类型检查
  const loginRequest: UserLoginRequest = {
    phone: '***********',
    password: '123456',
    loginType: 'password'
  };

  // 用户注册请求类型检查
  const registerRequest: UserRegisterRequest = {
    phone: '***********',
    password: '123456',
    realName: '张三',
    idCard: '110101199001011234'
  };

  // 钱包充值请求类型检查
  const rechargeRequest: WalletRechargeRequest = {
    amount: 100.00,
    cardId: 1,
    payPassword: '123456'
  };

  // 钱包提现请求类型检查
  const withdrawRequest: WalletWithdrawRequest = {
    amount: 50.00,
    cardId: 1,
    payPassword: '123456'
  };

  // 钱包转账请求类型检查
  const transferRequest: WalletTransferRequest = {
    toPhone: '***********',
    amount: 200.00,
    payPassword: '123456',
    description: '转账测试'
  };

  // 银行卡绑定请求类型检查
  const bindCardRequest: BankCardBindRequest = {
    cardNo: '6222021234567890123',
    cardType: '储蓄卡',
    bankName: '中国工商银行',
    holderName: '张三'
  };

  // 银行卡信息类型检查
  const bankCard: BankCard = {
    cardId: 1,
    userId: 1,
    cardNo: '6222021234567890123',
    cardType: '储蓄卡',
    bankName: '中国工商银行',
    holderName: '张三',
    isBound: 1,
    createTime: '2025-06-18 10:00:00',
    updateTime: '2025-06-18 10:00:00'
  };

  // 交易查询参数类型检查
  const queryParams: TransactionQueryParams = {
    page: '1',
    size: '10',
    type: 'transfer',
    startDate: '2025-06-01',
    endDate: '2025-06-18'
  };

  // 交易记录类型检查
  const transaction: Transaction = {
    transactionId: 1,
    transactionNumber: 'T202506180001',
    fromUserId: 1,
    toUserId: 2,
    amount: 100.00,
    transactionType: 'transfer',
    paymentMethod: 'wallet',
    description: '转账',
    status: 'success',
    createTime: '2025-06-18 10:00:00',
    relatedCardId: 1,
    cardNo: '6222021234567890123',
    statusDesc: '成功'
  };

  // 分页结果类型检查
  const pageResult: PageResult<Transaction> = {
    records: [transaction],
    total: 1,
    size: 10,
    current: 1,
    pages: 1
  };

  // API响应类型检查
  const apiResponse: ApiResponse<UserInfo> = {
    code: 200,
    message: '成功',
    data: userInfo,
    timestamp: '2025-06-18 10:00:00'
  };

  console.log('所有类型检查通过');
}

export { typeCheck };
