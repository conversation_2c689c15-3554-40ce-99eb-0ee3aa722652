package com.icss.springbootbig.service.impl;

import com.icss.springbootbig.config.SmsConfig;
import com.icss.springbootbig.service.SmsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 模拟短信服务实现类
 * 用于开发和测试环境
 */
@Service
@ConditionalOnProperty(name = "sms.provider", havingValue = "mock", matchIfMissing = true)
public class MockSmsServiceImpl implements SmsService {
    
    private static final Logger logger = LoggerFactory.getLogger(MockSmsServiceImpl.class);
    
    @Autowired
    private SmsConfig smsConfig;
    
    @Override
    public boolean sendVerificationCode(String phone, String code, Integer type) {
        try {
            String template = buildVerificationTemplate(code, type);
            
            // 模拟短信发送
            logger.info("=== 模拟短信发送 ===");
            logger.info("手机号: {}", maskPhone(phone));
            logger.info("内容: {}", template);
            logger.info("类型: {}", getTypeDescription(type));
            logger.info("==================");
            
            // 模拟网络延迟
            Thread.sleep(100);
            
            return true;
            
        } catch (Exception e) {
            logger.error("模拟短信发送失败", e);
            return false;
        }
    }
    
    @Override
    public boolean sendNotification(String phone, String message) {
        try {
            logger.info("=== 模拟通知短信 ===");
            logger.info("手机号: {}", maskPhone(phone));
            logger.info("内容: {}", message);
            logger.info("==================");
            
            Thread.sleep(100);
            return true;
            
        } catch (Exception e) {
            logger.error("模拟通知短信发送失败", e);
            return false;
        }
    }
    
    /**
     * 构建验证码短信模板
     */
    private String buildVerificationTemplate(String code, Integer type) {
        String typeDesc = getTypeDescription(type);
        return String.format("【%s】您的%s验证码是：%s，%d分钟内有效，请勿泄露给他人。", 
                smsConfig.getSignName(), 
                typeDesc, 
                code, 
                smsConfig.getExpireMinutes());
    }
    
    /**
     * 获取验证码类型描述
     */
    private String getTypeDescription(Integer type) {
        switch (type) {
            case 1:
                return "登录";
            case 2:
                return "操作";
            default:
                return "验证";
        }
    }
    
    /**
     * 手机号脱敏
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() != 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
}
