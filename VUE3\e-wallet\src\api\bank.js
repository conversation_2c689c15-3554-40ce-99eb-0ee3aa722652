import request from '@/utils/request'

/**
 * 银行管理API
 */
export const bankApi = {
  /**
   * 创建银行
   */
  createBank(data) {
    return request({
      url: '/api/bank',
      method: 'post',
      data
    })
  },

  /**
   * 根据ID获取银行信息
   */
  getBankById(bankId) {
    return request({
      url: `/api/bank/${bankId}`,
      method: 'get'
    })
  },

  /**
   * 根据银行代码获取银行信息
   */
  getBankByCode(bankCode) {
    return request({
      url: `/api/bank/code/${bankCode}`,
      method: 'get'
    })
  },

  /**
   * 更新银行信息
   */
  updateBank(bankId, data) {
    return request({
      url: `/api/bank/${bankId}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除银行
   */
  deleteBank(bankId) {
    return request({
      url: `/api/bank/${bankId}`,
      method: 'delete'
    })
  },

  /**
   * 更新银行状态
   */
  updateBankStatus(bankId, status) {
    return request({
      url: `/api/bank/${bankId}/status`,
      method: 'put',
      params: { status }
    })
  },

  /**
   * 获取所有银行列表
   */
  getAllBanks() {
    return request({
      url: '/api/bank/all',
      method: 'get'
    })
  },

  /**
   * 获取启用的银行列表
   */
  getActiveBanks() {
    return request({
      url: '/api/bank/active',
      method: 'get'
    })
  },

  /**
   * 分页查询银行
   */
  getBanksByPage(params) {
    return request({
      url: '/api/bank/page',
      method: 'get',
      params
    })
  },

  /**
   * 更新银行排序
   */
  updateBankSortOrder(bankId, sortOrder) {
    return request({
      url: `/api/bank/${bankId}/sort`,
      method: 'put',
      data: { sortOrder }
    })
  },

  /**
   * 批量更新银行状态
   */
  batchUpdateStatus(bankIds, status) {
    return request({
      url: '/api/bank/batch/status',
      method: 'put',
      data: { bankIds, status }
    })
  }
}
