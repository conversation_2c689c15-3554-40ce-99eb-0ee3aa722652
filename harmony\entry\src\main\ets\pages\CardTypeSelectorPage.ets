import router from '@ohos.router';
import { tempDataManager, TempDataKeys } from '../common/storage/TempDataManager';

interface CardTypeOption {
  type: string;
  description: string;
  features: string[];
}

@Entry
@Component
struct CardTypeSelectorPage {
  @State selectedType: string = '';

  private cardTypeOptions: CardTypeOption[] = [
    {
      type: '储蓄卡',
      description: '借记卡，先存款后消费',
      features: ['无透支功能', '资金安全', '适合日常消费', '无年费']
    },
    {
      type: '信用卡',
      description: '贷记卡，先消费后还款',
      features: ['可透支消费', '免息期还款', '积分奖励', '需要审核']
    }
  ];

  aboutToAppear() {
    // 获取传入的参数
    const params = router.getParams() as Record<string, string>;
    this.selectedType = params?.selectedType || '';
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button('取消')
          .fontSize(16)
          .fontColor('#1976D2')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            router.back();
          })

        Text('选择卡片类型')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(60) // 占位，保持标题居中
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')

      // 卡片类型列表
      Column() {
        ForEach(this.cardTypeOptions, (option: CardTypeOption, index: number) => {
          Column() {
            Row() {
              Column() {
                Text(option.type)
                  .fontSize(18)
                  .fontColor('#333333')
                  .fontWeight(FontWeight.Medium)
                  .alignSelf(ItemAlign.Start)

                Text(option.description)
                  .fontSize(14)
                  .fontColor('#666666')
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 4 })
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Start)

              if (this.selectedType === option.type) {
                Text('✓')
                  .fontSize(24)
                  .fontColor('#1976D2')
                  .fontWeight(FontWeight.Bold)
              }
            }
            .width('100%')
            .padding({ left: 20, right: 20, top: 16, bottom: 12 })

            // 特性列表
            Column() {
              ForEach(option.features, (feature: string) => {
                Row() {
                  Text('•')
                    .fontSize(12)
                    .fontColor('#999999')
                    .margin({ right: 8 })

                  Text(feature)
                    .fontSize(12)
                    .fontColor('#999999')
                    .layoutWeight(1)
                }
                .width('100%')
                .margin({ bottom: 4 })
              })
            }
            .width('100%')
            .padding({ left: 32, right: 20, bottom: 16 })
          }
          .width('100%')
          .backgroundColor(this.selectedType === option.type ? '#F0F8FF' : '#FFFFFF')
          .borderRadius(12)
          .border({
            width: this.selectedType === option.type ? 2 : 1,
            color: this.selectedType === option.type ? '#1976D2' : '#E0E0E0'
          })
          .margin({ bottom: 16 })
          .onClick(() => {
            // 保存选中的卡片类型到临时数据管理器
            tempDataManager.setData(TempDataKeys.SELECTED_CARD_TYPE, option.type);

            // 返回上一页
            router.back();
          })
        })
      }
      .width('100%')
      .padding(20)
      .layoutWeight(1)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }
}
