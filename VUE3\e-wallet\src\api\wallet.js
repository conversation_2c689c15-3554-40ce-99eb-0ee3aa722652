import request from '@/utils/request'

// 钱包API服务
const walletApi = {
  // 获取钱包余额
  getBalance(userId) {
    return request({
      url: `/api/wallet/balance/${userId}`,
      method: 'get'
    })
  },

  // 钱包充值
  recharge(data) {
    return request({
      url: '/api/wallet/recharge',
      method: 'post',
      data
    })
  },

  // 钱包提现
  withdraw(data) {
    return request({
      url: '/api/wallet/withdraw',
      method: 'post',
      data
    })
  },

  // 银行卡转账
  bankTransfer(data) {
    return request({
      url: '/api/wallet/bank-transfer',
      method: 'post',
      data
    })
  },

  // 钱包转账
  walletTransfer(data) {
    return request({
      url: '/api/wallet/wallet-transfer',
      method: 'post',
      data
    })
  },

  // 转账（兼容旧接口）
  transfer(data) {
    return request({
      url: '/api/wallet/transfer',
      method: 'post',
      data
    })
  },

  // 获取钱包交易记录
  getTransactions(userId, params) {
    return request({
      url: `/api/transaction/recent/${userId}`,
      method: 'get',
      params
    })
  },

  // 生成收款码
  generateQR(data) {
    return request({
      url: '/api/wallet/generate-qr',
      method: 'post',
      data
    })
  },

  // 扫码支付
  scanPay(data) {
    return request({
      url: '/api/wallet/scan-pay',
      method: 'post',
      data
    })
  },

  // 设置钱包限额
  setLimits(userId, limits) {
    return request({
      url: `/api/wallet/limits/${userId}`,
      method: 'put',
      data: limits
    })
  },

  // 创建钱包账户
  createAccount(userId) {
    return request({
      url: `/api/wallet/create-account/${userId}`,
      method: 'post'
    })
  },

  // 设置支付密码（已废弃，使用PayPasswordService）
  setPayPassword(data) {
    console.warn('此方法已废弃，请使用PayPasswordService相关API');
    return request({
      url: '/api/pay-password/set-default',
      method: 'post',
      data
    })
  },

  // 验证支付密码
  verifyPayPassword(data) {
    return request({
      url: '/api/wallet/verify-pay-password',
      method: 'post',
      data
    })
  },

  // 获取钱包限额信息
  getLimits(userId) {
    return request({
      url: `/api/wallet/limits/${userId}`,
      method: 'get'
    })
  },

  // 设置钱包限额
  setLimits(data) {
    return request({
      url: '/api/wallet/set-limits',
      method: 'post',
      data
    })
  }
}

export { walletApi }
