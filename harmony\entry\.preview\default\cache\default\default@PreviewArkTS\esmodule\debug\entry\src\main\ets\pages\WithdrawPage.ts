if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface WithdrawPage_Params {
    amount?: string;
    payPassword?: string;
    selectedCardId?: number;
    bankCards?: BankCard[];
    walletBalance?: number;
    payLimit?: number;
    isLoading?: boolean;
    showCardSelector?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { WalletApi } from "@normalized:N&&&entry/src/main/ets/api/WalletApi&";
import { BankCardApi } from "@normalized:N&&&entry/src/main/ets/api/BankCardApi&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import type { LocalWalletInfo } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import { globalStateManager, RefreshTypes } from "@normalized:N&&&entry/src/main/ets/common/utils/EventManager&";
import type { WalletWithdrawRequest, BankCard, WalletInfo } from '../common/types/index';
class WithdrawPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__amount = new ObservedPropertySimplePU('', this, "amount");
        this.__payPassword = new ObservedPropertySimplePU('', this, "payPassword");
        this.__selectedCardId = new ObservedPropertySimplePU(-1, this, "selectedCardId");
        this.__bankCards = new ObservedPropertyObjectPU([], this, "bankCards");
        this.__walletBalance = new ObservedPropertySimplePU(0, this, "walletBalance");
        this.__payLimit = new ObservedPropertySimplePU(0, this, "payLimit");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__showCardSelector = new ObservedPropertySimplePU(false, this, "showCardSelector");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: WithdrawPage_Params) {
        if (params.amount !== undefined) {
            this.amount = params.amount;
        }
        if (params.payPassword !== undefined) {
            this.payPassword = params.payPassword;
        }
        if (params.selectedCardId !== undefined) {
            this.selectedCardId = params.selectedCardId;
        }
        if (params.bankCards !== undefined) {
            this.bankCards = params.bankCards;
        }
        if (params.walletBalance !== undefined) {
            this.walletBalance = params.walletBalance;
        }
        if (params.payLimit !== undefined) {
            this.payLimit = params.payLimit;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.showCardSelector !== undefined) {
            this.showCardSelector = params.showCardSelector;
        }
    }
    updateStateVars(params: WithdrawPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__amount.purgeDependencyOnElmtId(rmElmtId);
        this.__payPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCardId.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCards.purgeDependencyOnElmtId(rmElmtId);
        this.__walletBalance.purgeDependencyOnElmtId(rmElmtId);
        this.__payLimit.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__showCardSelector.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__amount.aboutToBeDeleted();
        this.__payPassword.aboutToBeDeleted();
        this.__selectedCardId.aboutToBeDeleted();
        this.__bankCards.aboutToBeDeleted();
        this.__walletBalance.aboutToBeDeleted();
        this.__payLimit.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__showCardSelector.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __amount: ObservedPropertySimplePU<string>;
    get amount() {
        return this.__amount.get();
    }
    set amount(newValue: string) {
        this.__amount.set(newValue);
    }
    private __payPassword: ObservedPropertySimplePU<string>;
    get payPassword() {
        return this.__payPassword.get();
    }
    set payPassword(newValue: string) {
        this.__payPassword.set(newValue);
    }
    private __selectedCardId: ObservedPropertySimplePU<number>;
    get selectedCardId() {
        return this.__selectedCardId.get();
    }
    set selectedCardId(newValue: number) {
        this.__selectedCardId.set(newValue);
    }
    private __bankCards: ObservedPropertyObjectPU<BankCard[]>;
    get bankCards() {
        return this.__bankCards.get();
    }
    set bankCards(newValue: BankCard[]) {
        this.__bankCards.set(newValue);
    }
    private __walletBalance: ObservedPropertySimplePU<number>;
    get walletBalance() {
        return this.__walletBalance.get();
    }
    set walletBalance(newValue: number) {
        this.__walletBalance.set(newValue);
    }
    private __payLimit: ObservedPropertySimplePU<number>;
    get payLimit() {
        return this.__payLimit.get();
    }
    set payLimit(newValue: number) {
        this.__payLimit.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __showCardSelector: ObservedPropertySimplePU<boolean>;
    get showCardSelector() {
        return this.__showCardSelector.get();
    }
    set showCardSelector(newValue: boolean) {
        this.__showCardSelector.set(newValue);
    }
    aboutToAppear() {
        this.loadData();
    }
    async loadData() {
        try {
            // 加载银行卡
            this.bankCards = await BankCardApi.getCardList(1);
            // 加载钱包余额
            const walletInfo = await this.getWalletBalance();
            this.walletBalance = walletInfo?.balance || 0;
            // 加载用户支付限额
            const userInfo = await storageManager.getUserInfo();
            this.payLimit = userInfo?.payLimit || 0;
        }
        catch (error) {
            console.error('加载数据失败:', error);
            promptAction.showToast({ message: '加载数据失败' });
        }
    }
    async getWalletBalance(): Promise<LocalWalletInfo | null> {
        try {
            // 先尝试从本地存储获取
            const cachedWallet = await storageManager.getWalletInfo();
            if (cachedWallet) {
                return cachedWallet;
            }
            // 如果本地没有，从API获取
            const walletInfo = await WalletApi.getBalance();
            const localWalletInfo: LocalWalletInfo = this.convertToLocalWalletInfo(walletInfo);
            await storageManager.saveWalletInfo(localWalletInfo);
            return localWalletInfo;
        }
        catch (error) {
            console.error('获取钱包余额失败:', error);
            return null;
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(63:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(65:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('返回');
            Button.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(66:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('钱包提现');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(74:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(80:9)", "entry");
            Text.width(60);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(88:7)", "entry");
            Scroll.layoutWeight(1);
            Scroll.backgroundColor('#F5F5F5');
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(89:9)", "entry");
            Column.padding({ left: 16, right: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 钱包余额显示
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(91:11)", "entry");
            // 钱包余额显示
            Column.width('100%');
            // 钱包余额显示
            Column.padding(20);
            // 钱包余额显示
            Column.margin({ top: 12 });
            // 钱包余额显示
            Column.borderRadius(12);
            // 钱包余额显示
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('可提现余额');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(92:13)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`¥${this.walletBalance.toFixed(2)}`);
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(98:13)", "entry");
            Text.fontSize(28);
            Text.fontColor('#1976D2');
            Text.fontWeight(FontWeight.Bold);
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        // 钱包余额显示
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 提现金额输入
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(111:11)", "entry");
            // 提现金额输入
            Column.width('100%');
            // 提现金额输入
            Column.padding(20);
            // 提现金额输入
            Column.margin({ top: 12 });
            // 提现金额输入
            Column.borderRadius(12);
            // 提现金额输入
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('提现金额');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(112:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入提现金额', text: this.amount });
            TextInput.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(119:13)", "entry");
            TextInput.type(InputType.Number);
            TextInput.fontSize(24);
            TextInput.fontWeight(FontWeight.Bold);
            TextInput.height(60);
            TextInput.borderRadius(12);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.onChange((value: string) => {
                this.amount = value;
            });
        }, TextInput);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 全部提现按钮
            Button.createWithLabel('全部提现');
            Button.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(132:13)", "entry");
            // 全部提现按钮
            Button.fontSize(14);
            // 全部提现按钮
            Button.fontColor('#1976D2');
            // 全部提现按钮
            Button.backgroundColor('#E3F2FD');
            // 全部提现按钮
            Button.borderRadius(8);
            // 全部提现按钮
            Button.height(36);
            // 全部提现按钮
            Button.alignSelf(ItemAlign.End);
            // 全部提现按钮
            Button.margin({ top: 12 });
            // 全部提现按钮
            Button.onClick(() => {
                this.amount = this.walletBalance.toString();
            });
        }, Button);
        // 全部提现按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 提现说明
            Text.create(`提现手续费：免费\n到账时间：1-3个工作日\n单笔限额：¥${this.payLimit.toFixed(2)}`);
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(145:13)", "entry");
            // 提现说明
            Text.fontSize(12);
            // 提现说明
            Text.fontColor('#999999');
            // 提现说明
            Text.margin({ top: 12 });
            // 提现说明
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        // 提现说明
        Text.pop();
        // 提现金额输入
        Column.pop();
        // 选择银行卡
        this.BankCardSelectionView.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 支付密码输入
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(161:11)", "entry");
            // 支付密码输入
            Column.width('100%');
            // 支付密码输入
            Column.padding(20);
            // 支付密码输入
            Column.margin({ top: 12 });
            // 支付密码输入
            Column.borderRadius(12);
            // 支付密码输入
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('支付密码');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(162:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: '请输入6位支付密码' });
            TextInput.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(169:13)", "entry");
            TextInput.type(InputType.Password);
            TextInput.maxLength(6);
            TextInput.fontSize(16);
            TextInput.height(48);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.showPasswordIcon(true);
            TextInput.onChange((value: string) => {
                this.payPassword = value;
            });
        }, TextInput);
        // 支付密码输入
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 提现按钮
            Button.createWithLabel('确认提现');
            Button.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(189:11)", "entry");
            // 提现按钮
            Button.width('100%');
            // 提现按钮
            Button.height(48);
            // 提现按钮
            Button.fontSize(16);
            // 提现按钮
            Button.fontColor(Color.White);
            // 提现按钮
            Button.backgroundColor('#1976D2');
            // 提现按钮
            Button.borderRadius(8);
            // 提现按钮
            Button.margin({ top: 24, bottom: 20 });
            // 提现按钮
            Button.enabled(!this.isLoading && this.isFormValid());
            // 提现按钮
            Button.opacity((!this.isLoading && this.isFormValid()) ? 1 : 0.5);
            // 提现按钮
            Button.onClick(() => {
                this.handleWithdraw();
            });
        }, Button);
        // 提现按钮
        Button.pop();
        Column.pop();
        Scroll.pop();
        Column.pop();
    }
    BankCardSelectionView(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(215:5)", "entry");
            Column.width('100%');
            Column.padding(20);
            Column.margin({ top: 12 });
            Column.borderRadius(12);
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择银行卡');
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(216:7)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 12 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.bankCards.length === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 无银行卡状态
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(225:9)", "entry");
                        // 无银行卡状态
                        Column.width('100%');
                        // 无银行卡状态
                        Column.height(120);
                        // 无银行卡状态
                        Column.justifyContent(FlexAlign.Center);
                        // 无银行卡状态
                        Column.alignItems(HorizontalAlign.Center);
                        // 无银行卡状态
                        Column.backgroundColor('#F8F9FA');
                        // 无银行卡状态
                        Column.borderRadius(8);
                        // 无银行卡状态
                        Column.border({ width: 1, color: '#E0E0E0', style: BorderStyle.Dashed });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无可用银行卡');
                        Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(226:11)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#999999');
                        Text.margin({ bottom: 12 });
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithLabel('去绑定银行卡');
                        Button.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(231:11)", "entry");
                        Button.fontSize(14);
                        Button.fontColor('#1976D2');
                        Button.backgroundColor('#E3F2FD');
                        Button.borderRadius(8);
                        Button.padding({ left: 16, right: 16, top: 8, bottom: 8 });
                        Button.onClick(() => {
                            router.pushUrl({ url: 'pages/BankCardPage' });
                        });
                    }, Button);
                    Button.pop();
                    // 无银行卡状态
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        // 显示选中的银行卡
                        if (this.selectedCardId !== -1) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.SelectedBankCardDisplay.bind(this)();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    // 显示银行卡列表供选择
                                    Column.create();
                                    Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(254:11)", "entry");
                                }, Column);
                                this.observeComponentCreation2((elmtId, isInitialRender) => {
                                    ForEach.create();
                                    const forEachItemGenFunction = (_item, index: number) => {
                                        const card = _item;
                                        this.BankCardItem.bind(this)(card, index);
                                    };
                                    this.forEachUpdateFunction(elmtId, this.bankCards, forEachItemGenFunction, undefined, true, false);
                                }, ForEach);
                                ForEach.pop();
                                // 显示银行卡列表供选择
                                Column.pop();
                            });
                        }
                    }, If);
                    If.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    SelectedBankCardDisplay(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.getSelectedCard()) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 银行卡展示区域
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(273:7)", "entry");
                        // 银行卡展示区域
                        Column.width('100%');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 真实银行卡样式
                        Stack.create({ alignContent: Alignment.TopStart });
                        Stack.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(275:9)", "entry");
                        // 真实银行卡样式
                        Stack.width('100%');
                        // 真实银行卡样式
                        Stack.margin({ bottom: 12 });
                    }, Stack);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(276:11)", "entry");
                        Column.width('100%');
                        Column.height(120);
                        Column.padding(16);
                        Column.borderRadius(12);
                        Column.linearGradient({
                            direction: GradientDirection.Right,
                            colors: this.getBankCardGradient(this.getSelectedCard()?.bankName || '')
                        });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 银行卡顶部信息
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(278:13)", "entry");
                        // 银行卡顶部信息
                        Row.width('100%');
                        // 银行卡顶部信息
                        Row.margin({ bottom: 20 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.getSelectedCard()?.bankName || '');
                        Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(279:15)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#FFFFFF');
                        Text.fontWeight(FontWeight.Bold);
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.getCardTypeText(this.getSelectedCard()?.cardType || ''));
                        Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(285:15)", "entry");
                        Text.fontSize(12);
                        Text.fontColor('#FFFFFF');
                        Text.backgroundColor('rgba(255,255,255,0.2)');
                        Text.borderRadius(12);
                        Text.padding({ left: 8, right: 8, top: 4, bottom: 4 });
                    }, Text);
                    Text.pop();
                    // 银行卡顶部信息
                    Row.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 银行卡号
                        Text.create(this.formatCardNumber(this.getSelectedCard()?.cardNo || ''));
                        Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(296:13)", "entry");
                        // 银行卡号
                        Text.fontSize(16);
                        // 银行卡号
                        Text.fontColor('#FFFFFF');
                        // 银行卡号
                        Text.fontWeight(FontWeight.Medium);
                        // 银行卡号
                        Text.letterSpacing(1);
                        // 银行卡号
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    // 银行卡号
                    Text.pop();
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 银行卡装饰图案
                        Text.create('💳');
                        Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(313:11)", "entry");
                        // 银行卡装饰图案
                        Text.fontSize(40);
                        // 银行卡装饰图案
                        Text.fontColor('rgba(255,255,255,0.1)');
                    }, Text);
                    // 银行卡装饰图案
                    Text.pop();
                    // 真实银行卡样式
                    Stack.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 更换银行卡按钮
                        Button.createWithLabel('更换银行卡');
                        Button.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(321:9)", "entry");
                        // 更换银行卡按钮
                        Button.fontSize(14);
                        // 更换银行卡按钮
                        Button.fontColor('#1976D2');
                        // 更换银行卡按钮
                        Button.backgroundColor('#E3F2FD');
                        // 更换银行卡按钮
                        Button.borderRadius(8);
                        // 更换银行卡按钮
                        Button.height(36);
                        // 更换银行卡按钮
                        Button.onClick(() => {
                            this.selectedCardId = -1; // 重新选择
                        });
                    }, Button);
                    // 更换银行卡按钮
                    Button.pop();
                    // 银行卡展示区域
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    BankCardItem(card: BankCard, index: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(337:5)", "entry");
            Row.width('100%');
            Row.padding(12);
            Row.margin({ bottom: 8 });
            Row.borderRadius(8);
            Row.backgroundColor(this.selectedCardId === card.cardId ? '#E3F2FD' : '#F8F9FA');
            Row.border({
                width: 1,
                color: this.selectedCardId === card.cardId ? '#1976D2' : '#E0E0E0'
            });
            Row.onClick(() => {
                this.selectedCardId = card.cardId;
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Radio.create({ value: index.toString(), group: 'bankCard' });
            Radio.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(338:7)", "entry");
            Radio.checked(this.selectedCardId === card.cardId);
            Radio.onChange((isChecked: boolean) => {
                if (isChecked) {
                    this.selectedCardId = card.cardId;
                }
            });
        }, Radio);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(346:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ left: 12 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.bankName);
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(347:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${this.getCardTypeText(card.cardType)} **** ${card.cardNo.slice(-4)}`);
            Text.debugLine("entry/src/main/ets/pages/WithdrawPage.ets(353:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    isFormValid(): boolean {
        const amountNum = parseFloat(this.amount);
        return !isNaN(amountNum) &&
            amountNum > 0 &&
            amountNum <= this.walletBalance &&
            amountNum <= this.payLimit &&
            this.selectedCardId !== -1 &&
            this.payPassword.length === 6;
    }
    async handleWithdraw() {
        if (this.isLoading)
            return;
        const amountNum = parseFloat(this.amount);
        if (isNaN(amountNum) || amountNum <= 0) {
            promptAction.showToast({ message: '请输入正确的提现金额' });
            return;
        }
        if (amountNum > this.walletBalance) {
            promptAction.showToast({ message: '提现金额不能超过可用余额' });
            return;
        }
        if (amountNum > this.payLimit) {
            promptAction.showToast({ message: `提现金额不能超过支付限额 ¥${this.payLimit.toFixed(2)}` });
            return;
        }
        if (this.selectedCardId === -1) {
            promptAction.showToast({ message: '请选择银行卡' });
            return;
        }
        if (this.payPassword.length !== 6) {
            promptAction.showToast({ message: '请输入6位支付密码' });
            return;
        }
        this.isLoading = true;
        try {
            // 获取当前用户ID
            const cachedUserInfo = await storageManager.getUserInfo();
            if (!cachedUserInfo || !cachedUserInfo.userId) {
                console.error('无法获取用户ID');
                promptAction.showToast({ message: '用户信息获取失败' });
                return;
            }
            const withdrawData: WalletWithdrawRequest = {
                userId: cachedUserInfo.userId,
                amount: amountNum,
                cardId: this.selectedCardId,
                payPassword: this.payPassword
            };
            await WalletApi.withdraw(withdrawData);
            promptAction.showToast({ message: '提现申请已提交，请等待处理' });
            // 标记需要刷新钱包数据
            globalStateManager.markForRefresh(RefreshTypes.WALLET);
            // 返回主页
            router.back();
        }
        catch (error) {
            console.error('提现失败:', error);
            let errorMessage = '提现失败，请稍后重试';
            if (error instanceof Error) {
                errorMessage = `提现失败: ${error.message}`;
            }
            promptAction.showToast({ message: errorMessage });
        }
        finally {
            this.isLoading = false;
        }
    }
    private convertToLocalWalletInfo(walletInfo: WalletInfo): LocalWalletInfo {
        return {
            walletNo: walletInfo.walletNo,
            balance: walletInfo.balance,
            status: 1
        };
    }
    /**
     * 获取选中的银行卡
     */
    getSelectedCard(): BankCard | null {
        return this.bankCards.find(card => card.cardId === this.selectedCardId) || null;
    }
    /**
     * 格式化银行卡号显示（带空格分隔）
     */
    formatCardNumber(cardNo: string): string {
        if (!cardNo)
            return '';
        // 脱敏处理
        const maskedCardNo = this.maskCardNo(cardNo);
        // 添加空格分隔，每4位一组
        return maskedCardNo.replace(/(.{4})/g, '$1 ').trim();
    }
    /**
     * 脱敏银行卡号
     */
    maskCardNo(cardNo: string): string {
        if (!cardNo || cardNo.length < 8)
            return cardNo;
        return cardNo.replace(/(\d{4})\d*(\d{4})/, '$1****$2');
    }
    /**
     * 获取卡片类型文本
     */
    getCardTypeText(cardType: string | number): string {
        // 这里需要根据实际的cardType类型来判断
        if (typeof cardType === 'string') {
            return cardType;
        }
        // 如果是数字类型，转换为对应文本
        switch (cardType) {
            case 1: return '储蓄卡';
            case 2: return '信用卡';
            default: return '储蓄卡';
        }
    }
    /**
     * 获取银行卡渐变色
     */
    getBankCardGradient(bankName: string): Array<[
        string,
        number
    ]> {
        const gradients: Record<string, Array<[
            string,
            number
        ]>> = {
            '中国工商银行': [['#C41E3A', 0], ['#8B0000', 1]],
            '中国建设银行': [['#003DA5', 0], ['#001F5C', 1]],
            '中国农业银行': [['#00A651', 0], ['#006B35', 1]],
            '中国银行': [['#B8860B', 0], ['#8B6914', 1]],
            '招商银行': [['#DC143C', 0], ['#8B0000', 1]],
            '交通银行': [['#0066CC', 0], ['#003D7A', 1]],
            '中信银行': [['#FF6B35', 0], ['#CC4125', 1]],
            '光大银行': [['#9932CC', 0], ['#6A1B9A', 1]],
            '华夏银行': [['#FF1744', 0], ['#C62828', 1]],
            '民生银行': [['#00BCD4', 0], ['#0097A7', 1]],
            '广发银行': [['#FF5722', 0], ['#D84315', 1]],
            '平安银行': [['#FF9800', 0], ['#F57C00', 1]]
        };
        return gradients[bankName] || [['#1976D2', 0], ['#0D47A1', 1]];
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "WithdrawPage";
    }
}
registerNamedRoute(() => new WithdrawPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/WithdrawPage", pageFullPath: "entry/src/main/ets/pages/WithdrawPage", integratedHsp: "false", moduleType: "followWithHap" });
