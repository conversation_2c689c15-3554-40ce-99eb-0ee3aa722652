import request from '@/utils/request'

// 支付密码API服务
const payPasswordApi = {
  // 设置默认银行卡支付密码
  setDefaultCardPayPassword(data) {
    return request({
      url: '/api/pay-password/set-default',
      method: 'post',
      data
    })
  },

  // 设置指定银行卡支付密码
  setCardPayPassword(data) {
    return request({
      url: '/api/pay-password/set-card',
      method: 'post',
      data
    })
  },

  // 修改银行卡支付密码
  changeCardPayPassword(data) {
    return request({
      url: '/api/pay-password/change-card',
      method: 'put',
      data
    })
  },

  // 重置银行卡支付密码
  resetCardPayPassword(data) {
    return request({
      url: '/api/pay-password/reset-card',
      method: 'put',
      data
    })
  },

  // 验证支付密码
  verifyPayPassword(data) {
    return request({
      url: '/api/pay-password/verify',
      method: 'post',
      data
    })
  },

  // 验证指定银行卡支付密码
  verifyCardPayPassword(data) {
    return request({
      url: '/api/pay-password/verify-card',
      method: 'post',
      data
    })
  },

  // 获取支付密码设置状态
  getPayPasswordStatus(userId) {
    return request({
      url: `/api/pay-password/status/${userId}`,
      method: 'get'
    })
  },

  // 批量设置银行卡支付密码
  setBatchCardPayPassword(data) {
    return request({
      url: '/api/pay-password/set-batch',
      method: 'post',
      data
    })
  },

  // 测试支付密码
  testPayPassword(data) {
    return request({
      url: '/api/pay-password/test',
      method: 'post',
      data
    })
  }
}

// 导出API对象
export { payPasswordApi }
export default payPasswordApi
