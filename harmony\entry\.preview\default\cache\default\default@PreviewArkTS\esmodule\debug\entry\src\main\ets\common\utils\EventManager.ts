/**
 * 简化的全局状态管理器
 * 用于页面间的数据同步
 */
/**
 * 全局状态管理器
 */
export class GlobalStateManager {
    private static instance: GlobalStateManager;
    private needsRefresh: boolean = false;
    private refreshType: string = '';
    private constructor() { }
    public static getInstance(): GlobalStateManager {
        if (!GlobalStateManager.instance) {
            GlobalStateManager.instance = new GlobalStateManager();
        }
        return GlobalStateManager.instance;
    }
    /**
     * 标记需要刷新
     */
    public markForRefresh(type: string): void {
        this.needsRefresh = true;
        this.refreshType = type;
        console.log(`标记需要刷新: ${type}`);
    }
    /**
     * 检查是否需要刷新
     */
    public checkNeedsRefresh(): RefreshInfo {
        const result: RefreshInfo = {
            needsRefresh: this.needsRefresh,
            refreshType: this.refreshType
        };
        // 重置状态
        this.needsRefresh = false;
        this.refreshType = '';
        return result;
    }
    /**
     * 清除刷新标记
     */
    public clearRefreshFlag(): void {
        this.needsRefresh = false;
        this.refreshType = '';
    }
}
/**
 * 刷新信息接口
 */
export interface RefreshInfo {
    needsRefresh: boolean;
    refreshType: string;
}
/**
 * 刷新类型常量
 */
export class RefreshTypes {
    public static readonly WALLET: string = 'wallet';
    public static readonly TRANSACTION: string = 'transaction';
    public static readonly BANK_CARD: string = 'bank_card';
    public static readonly USER_INFO: string = 'user_info';
}
/**
 * 导出单例实例
 */
export const globalStateManager = GlobalStateManager.getInstance();
