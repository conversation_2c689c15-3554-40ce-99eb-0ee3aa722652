package com.icss.springbootbig.enums;

public enum TransactionType {
    DEPOSIT(1, "充值"),
    TRANSFER(2, "转账"),
    RECEIVE(3, "收款"),
    CONSUME(4, "消费"),
    WITHDRAW(5, "提现");

    private final int code;
    private final String description;

    TransactionType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static TransactionType fromCode(int code) {
        for (TransactionType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的交易类型代码: " + code);
    }
}