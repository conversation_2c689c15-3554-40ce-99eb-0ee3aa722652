package com.icss.springbootbig.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.icss.springbootbig.entity.BankCard;
import com.icss.springbootbig.entity.User;
import com.icss.springbootbig.mapper.BankCardMapper;
import com.icss.springbootbig.mapper.UserMapper;
import com.icss.springbootbig.exception.ApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.lang.reflect.Field;

@Service
public class BankCardService {
    @Autowired
    private BankCardMapper bankCardMapper;

    @Autowired
    private UserMapper userMapper;

    @Transactional
    public BankCard addBankCard(BankCard bankCard) {
        // 确保userId不为空
        if (bankCard.getUserId() == null) {
            throw new ApiException("用户ID不能为空");
        }

        // 验证卡号是否已存在
        if (bankCardMapper.selectCount(
                new QueryWrapper<BankCard>()
                        .eq("card_number", bankCard.getCardNumber())
                        .eq("user_id", bankCard.getUserId())) > 0) {
            throw new ApiException("该银行卡已绑定");
        }

        // 设置时间戳
        Date now = new Date();
        bankCard.setCreatedAt(now);
        bankCard.setUpdatedAt(now);

        // 设置默认值
        if (bankCard.getStatus() == null) {
            bankCard.setStatus(1); // 默认状态为正常
        }

        if (bankCard.getIsDefault() == null) {
            bankCard.setIsDefault(0); // 默认不是默认卡
        }

        if (bankCard.getCardType() == null) {
            bankCard.setCardType(1); // 默认为借记卡
        }

        // 确保必需字段不为空
        if (bankCard.getBankName() == null || bankCard.getBankName().trim().isEmpty()) {
            throw new ApiException("银行名称不能为空");
        }

        if (bankCard.getCardNumber() == null || bankCard.getCardNumber().trim().isEmpty()) {
            throw new ApiException("卡号不能为空");
        }

        if (bankCard.getCardHolder() == null || bankCard.getCardHolder().trim().isEmpty()) {
            throw new ApiException("持卡人姓名不能为空");
        }

        // 打印调试信息
        System.out.println("准备插入银行卡数据:");
        System.out.println("userId: " + bankCard.getUserId());
        System.out.println("bankName: " + bankCard.getBankName());
        System.out.println("cardNumber: " + bankCard.getCardNumber());
        System.out.println("cardType: " + bankCard.getCardType());
        System.out.println("cardHolder: " + bankCard.getCardHolder());
        System.out.println("status: " + bankCard.getStatus());
        System.out.println("isDefault: " + bankCard.getIsDefault());

        // 执行插入并返回带ID的实体
        bankCardMapper.insert(bankCard);
        return bankCard;
    }

    public List<BankCard> getCardsByUserId(Integer userId) {
        List<BankCard> cards = bankCardMapper.selectList(
                new QueryWrapper<BankCard>()
                        .eq("user_id", userId)
                        .orderByDesc("is_default"));

        // 获取用户登录密码并设置到每张银行卡
        User user = userMapper.selectById(userId);
        if (user != null) {
            for (BankCard card : cards) {
                card.setPassword(user.getPassword()); // 使用用户登录密码作为银行卡查询密码
            }
        }

        return cards;
    }

    public BankCard getCardDetails(Integer cardId) {
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null) {
            throw new ApiException("银行卡不存在");
        }

        // 获取用户登录密码并设置到银行卡
        User user = userMapper.selectById(card.getUserId());
        if (user != null) {
            card.setPassword(user.getPassword()); // 使用用户登录密码作为银行卡查询密码
        }

        return card;
    }



    /**
     * 删除银行卡（从数据库中彻底删除）
     */
    @Transactional
    public Integer deleteCard(Integer userId, Integer cardId) {
        // 如果没有提供userId，则使用管理员删除
        if (userId == null) {
            return adminDeleteCard(cardId);
        }

        // 验证银行卡是否属于该用户
        BankCard card = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("card_id", cardId)
                        .eq("user_id", userId));

        if (card == null) {
            throw new ApiException("银行卡不存在或不属于该用户");
        }

        // 从数据库中彻底删除
        return bankCardMapper.delete(
                new QueryWrapper<BankCard>()
                        .eq("card_id", cardId)
                        .eq("user_id", userId));
    }

    /**
     * 管理员删除银行卡（不验证用户权限）
     */
    @Transactional
    public Integer adminDeleteCard(Integer cardId) {
        // 验证银行卡是否存在
        BankCard card = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("card_id", cardId));

        if (card == null) {
            throw new ApiException("银行卡不存在");
        }

        // 从数据库中彻底删除（不验证用户权限）
        return bankCardMapper.delete(
                new QueryWrapper<BankCard>()
                        .eq("card_id", cardId));
    }

    /**
     * 解绑银行卡（只更新状态为未绑定，不删除数据）
     */
    @Transactional
    public Integer unbindCard(Integer userId, Integer cardId) {
        // 验证银行卡是否属于该用户
        BankCard card = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("card_id", cardId)
                        .eq("user_id", userId));

        if (card == null) {
            throw new ApiException("银行卡不存在或不属于该用户");
        }

        // 更新状态为未绑定
        BankCard updateCard = new BankCard();
        updateCard.setCardId(cardId);
        updateCard.setStatus(0); // 0表示未绑定/未连接
        updateCard.setUpdatedAt(new Date());

        return bankCardMapper.updateById(updateCard);
    }

    public List<BankCard> getCreditCards(Integer userId) {
        List<BankCard> cards = bankCardMapper.selectList(
                new QueryWrapper<BankCard>()
                        .eq("user_id", userId)
                        .eq("card_type", 2)); // 2表示信用卡

        // 获取用户登录密码并设置到每张银行卡
        User user = userMapper.selectById(userId);
        if (user != null) {
            for (BankCard card : cards) {
                card.setPassword(user.getPassword()); // 使用用户登录密码作为银行卡查询密码
            }
        }

        return cards;
    }

    public List<BankCard> getAllCards() {
        return bankCardMapper.selectList(null);
    }

    public List<BankCard> getBoundCards(Integer userId) {
        List<BankCard> cards = bankCardMapper.selectList(
                new QueryWrapper<BankCard>()
                        .eq("user_id", userId)
                        .eq("status", 1) // 1表示已绑定
                        .orderByDesc("is_default"));

        // 获取用户登录密码并设置到每张银行卡
        User user = userMapper.selectById(userId);
        if (user != null) {
            for (BankCard card : cards) {
                card.setPassword(user.getPassword()); // 使用用户登录密码作为银行卡查询密码
            }
        }

        return cards;
    }

    @Transactional
    public Integer bindCard(Integer userId, Integer cardId) {
        BankCard card = new BankCard();
        card.setCardId(cardId);
        card.setStatus(1); // 1表示已绑定
        card.setUpdatedAt(new Date());
        return bankCardMapper.updateById(card);
    }

    /**
     * 验证银行卡信息
     */
    public boolean validateBankCard(BankCard bankCard) {
        // 验证卡号格式
        if (bankCard.getCardNumber() == null || bankCard.getCardNumber().length() < 16) {
            return false;
        }

        // 验证持卡人姓名
        if (bankCard.getCardHolder() == null || bankCard.getCardHolder().trim().isEmpty()) {
            return false;
        }

        // 验证手机号
        if (bankCard.getPhone() == null || !bankCard.getPhone().matches("^1[3-9]\\d{9}$")) {
            return false;
        }

        // 如果是信用卡，验证有效期和CVV
        if (bankCard.getCardType() == 2) {
            if (bankCard.getExpiryDate() == null || !bankCard.getExpiryDate().matches("^\\d{2}/\\d{2}$")) {
                return false;
            }
            if (bankCard.getCvv() == null || !bankCard.getCvv().matches("^\\d{3}$")) {
                return false;
            }
        }

        return true;
    }

    /**
     * 发送银行卡验证码
     */
    public boolean sendVerificationCode(Integer cardId, String phone) {
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null || !phone.equals(card.getPhone())) {
            throw new ApiException("银行卡信息不匹配");
        }

        // 这里应该调用短信服务发送验证码
        // 为了演示，我们只是记录日志
        System.out.println("向手机号 " + phone + " 发送验证码");

        return true;
    }

    /**
     * 验证银行卡连接
     */
    @Transactional
    public boolean verifyCardConnection(Integer cardId, String verificationCode) {
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null) {
            throw new ApiException("银行卡不存在");
        }

        // 这里应该验证验证码的正确性
        // 为了演示，我们假设验证码是 "123456"
        if (!"123456".equals(verificationCode)) {
            return false;
        }

        // 验证成功，更新银行卡状态为已连接
        BankCard updateCard = new BankCard();
        updateCard.setCardId(cardId);
        updateCard.setStatus(1); // 1表示已连接
        updateCard.setUpdatedAt(new Date());
        bankCardMapper.updateById(updateCard);

        return true;
    }

    /**
     * 断开银行卡连接
     */
    @Transactional
    public Integer disconnectCard(Integer userId, Integer cardId) {
        // 验证银行卡是否属于该用户
        BankCard card = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("card_id", cardId)
                        .eq("user_id", userId));

        if (card == null) {
            throw new ApiException("银行卡不存在或不属于该用户");
        }

        // 更新状态为未连接
        BankCard updateCard = new BankCard();
        updateCard.setCardId(cardId);
        updateCard.setStatus(0); // 0表示未连接
        updateCard.setUpdatedAt(new Date());

        return bankCardMapper.updateById(updateCard);
    }

    /**
     * 获取用户的连接状态统计
     */
    public Map<String, Integer> getConnectionStats(Integer userId) {
        List<BankCard> cards = bankCardMapper.selectList(
                new QueryWrapper<BankCard>().eq("user_id", userId));

        Map<String, Integer> stats = new HashMap<>();
        stats.put("total", cards.size());
        stats.put("connected", (int) cards.stream().filter(card -> card.getStatus() == 1).count());
        stats.put("disconnected", (int) cards.stream().filter(card -> card.getStatus() == 0).count());
        stats.put("frozen", (int) cards.stream().filter(card -> card.getStatus() == 2).count());

        return stats;
    }

    /**
     * 检查银行卡是否可以进行操作
     */
    public boolean isCardOperational(Integer cardId) {
        BankCard card = bankCardMapper.selectById(cardId);
        return card != null && card.getStatus() == 1; // 只有已连接的卡才能操作
    }

    /**
     * 获取用户的默认银行卡
     */
    public BankCard getDefaultCard(Integer userId) {
        BankCard card = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("user_id", userId)
                        .eq("is_default", 1)
                        .eq("status", 1));

        // 获取用户登录密码并设置到银行卡
        if (card != null) {
            User user = userMapper.selectById(userId);
            if (user != null) {
                card.setPassword(user.getPassword()); // 使用用户登录密码作为银行卡查询密码
            }
        }

        return card;
    }

    /**
     * 设置默认银行卡
     */
    @Transactional
    public void setDefaultCard(Integer userId, Integer cardId) {
        // 验证银行卡是否属于该用户
        BankCard card = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("card_id", cardId)
                        .eq("user_id", userId));

        if (card == null) {
            throw new ApiException("银行卡不存在或不属于该用户");
        }

        if (card.getStatus() != 1) {
            throw new ApiException("只能设置已连接的银行卡为默认卡");
        }

        // 取消其他默认卡
        bankCardMapper.update(null,
                new UpdateWrapper<BankCard>()
                        .eq("user_id", userId)
                        .set("is_default", 0)
                        .set("updated_at", new Date()));

        // 设置新的默认卡
        BankCard updateCard = new BankCard();
        updateCard.setCardId(cardId);
        updateCard.setIsDefault(1);
        updateCard.setUpdatedAt(new Date());
        bankCardMapper.updateById(updateCard);
    }

    /**
     * 获取银行卡详细信息（包含脱敏处理）
     */
    public Map<String, Object> getCardDetailInfo(Integer cardId, Integer userId) {
        BankCard card = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("card_id", cardId)
                        .eq("user_id", userId));

        if (card == null) {
            throw new ApiException("银行卡不存在或不属于该用户");
        }

        Map<String, Object> cardInfo = new HashMap<>();
        cardInfo.put("cardId", card.getCardId());
        cardInfo.put("bankName", card.getBankName());
        cardInfo.put("cardNumber", maskCardNumber(card.getCardNumber()));
        cardInfo.put("cardType", card.getCardType());
        cardInfo.put("cardHolder", card.getCardHolder());
        cardInfo.put("phone", card.getPhone());
        cardInfo.put("isDefault", card.getIsDefault());
        cardInfo.put("status", card.getStatus());
        cardInfo.put("createdAt", card.getCreatedAt());

        // 信用卡显示有效期（脱敏）
        if (card.getCardType() == 2 && card.getExpiryDate() != null) {
            cardInfo.put("expiryDate", card.getExpiryDate());
        }

        return cardInfo;
    }

    /**
     * 银行卡号脱敏处理
     */
    private String maskCardNumber(String cardNumber) {
        if (cardNumber == null || cardNumber.length() < 8) {
            return cardNumber;
        }

        String prefix = cardNumber.substring(0, 4);
        String suffix = cardNumber.substring(cardNumber.length() - 4);
        String middle = "*".repeat(cardNumber.length() - 8);

        return prefix + middle + suffix;
    }

    /**
     * 批量删除银行卡
     */
    @Transactional
    public Integer batchDeleteCards(Integer userId, List<Integer> cardIds) {
        if (cardIds == null || cardIds.isEmpty()) {
            throw new ApiException("请选择要删除的银行卡");
        }

        // 验证所有银行卡都属于该用户
        List<BankCard> cards = bankCardMapper.selectList(
                new QueryWrapper<BankCard>()
                        .eq("user_id", userId)
                        .in("card_id", cardIds));

        if (cards.size() != cardIds.size()) {
            throw new ApiException("部分银行卡不存在或不属于该用户");
        }

        // 检查是否有默认卡
        boolean hasDefaultCard = cards.stream().anyMatch(card -> card.getIsDefault() == 1);
        if (hasDefaultCard) {
            throw new ApiException("不能删除默认银行卡，请先设置其他卡为默认卡");
        }

        return bankCardMapper.deleteBatchIds(cardIds);
    }

    /**
     * 获取银行卡使用统计
     */
    public Map<String, Object> getCardUsageStats(Integer userId) {
        List<BankCard> cards = bankCardMapper.selectList(
                new QueryWrapper<BankCard>().eq("user_id", userId));

        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCards", cards.size());
        stats.put("debitCards", cards.stream().filter(card -> card.getCardType() == 1).count());
        stats.put("creditCards", cards.stream().filter(card -> card.getCardType() == 2).count());
        stats.put("connectedCards", cards.stream().filter(card -> card.getStatus() == 1).count());
        stats.put("defaultCard", cards.stream().filter(card -> card.getIsDefault() == 1).findFirst().orElse(null));

        return stats;
    }

    /**
     * 表格编辑 - 更新单个字段
     */
    @Transactional
    public BankCard updateField(Integer cardId, Map<String, Object> fieldData) {
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null) {
            throw new ApiException("银行卡不存在");
        }

        // 使用反射更新字段
        for (Map.Entry<String, Object> entry : fieldData.entrySet()) {
            String fieldName = entry.getKey();
            Object fieldValue = entry.getValue();

            try {
                Field field = BankCard.class.getDeclaredField(fieldName);
                field.setAccessible(true);

                // 类型转换
                if (field.getType() == Integer.class && fieldValue instanceof Number) {
                    field.set(card, ((Number) fieldValue).intValue());
                } else if (field.getType() == String.class) {
                    field.set(card, fieldValue.toString());
                } else {
                    field.set(card, fieldValue);
                }

                System.out.println("更新字段: " + fieldName + " = " + fieldValue);
            } catch (Exception e) {
                System.err.println("更新字段失败: " + fieldName + ", 错误: " + e.getMessage());
                throw new ApiException("更新字段失败: " + fieldName);
            }
        }

        // 设置更新时间
        card.setUpdatedAt(new Date());

        // 如果更新的是默认卡状态，需要处理其他卡的默认状态
        if (fieldData.containsKey("isDefault") && card.getIsDefault() == 1) {
            // 取消其他卡的默认状态
            bankCardMapper.update(null,
                    new UpdateWrapper<BankCard>()
                            .eq("user_id", card.getUserId())
                            .ne("card_id", cardId)
                            .set("is_default", 0)
                            .set("updated_at", new Date()));
        }

        // 更新数据库
        int result = bankCardMapper.updateById(card);
        if (result > 0) {
            return bankCardMapper.selectById(cardId);
        } else {
            throw new ApiException("更新失败");
        }
    }

    /**
     * 批量更新银行卡
     */
    @Transactional
    public List<BankCard> batchUpdate(List<BankCard> bankCards) {
        List<BankCard> updatedCards = new ArrayList<>();

        for (BankCard card : bankCards) {
            if (card.getCardId() != null) {
                card.setUpdatedAt(new Date());
                int result = bankCardMapper.updateById(card);
                if (result > 0) {
                    updatedCards.add(bankCardMapper.selectById(card.getCardId()));
                }
            }
        }

        return updatedCards;
    }

    /**
     * 根据手机号查询银行卡
     */
    public List<BankCard> getCardsByPhone(String phone) {
        // 先根据手机号查找用户
        User user = userMapper.selectOne(
            new QueryWrapper<User>().eq("phone", phone)
        );

        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 查询该用户的银行卡
        return bankCardMapper.selectList(
            new QueryWrapper<BankCard>()
                .eq("user_id", user.getUserId())
                .eq("status", 1) // 只返回正常状态的银行卡
                .orderBy(true, false, "is_default") // 默认卡排在前面
                .orderBy(true, true, "created_at")
        );
    }

    /**
     * 设置银行卡余额（测试用）
     */
    @Transactional
    public void setBankCardBalance(Integer cardId, java.math.BigDecimal balance) {
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null) {
            throw new ApiException("银行卡不存在");
        }

        card.setBalance(balance);
        card.setUpdatedAt(new Date());
        bankCardMapper.updateById(card);
    }
}