if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface RegisterPage_Params {
    phone?: string;
    password?: string;
    confirmPassword?: string;
    realName?: string;
    idCard?: string;
    isLoading?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { UserApi } from "@normalized:N&&&entry/src/main/ets/api/UserApi&";
import type { UserRegisterRequest } from '../common/types/index';
class RegisterPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__phone = new ObservedPropertySimplePU('', this, "phone");
        this.__password = new ObservedPropertySimplePU('', this, "password");
        this.__confirmPassword = new ObservedPropertySimplePU('', this, "confirmPassword");
        this.__realName = new ObservedPropertySimplePU('', this, "realName");
        this.__idCard = new ObservedPropertySimplePU('', this, "idCard");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: RegisterPage_Params) {
        if (params.phone !== undefined) {
            this.phone = params.phone;
        }
        if (params.password !== undefined) {
            this.password = params.password;
        }
        if (params.confirmPassword !== undefined) {
            this.confirmPassword = params.confirmPassword;
        }
        if (params.realName !== undefined) {
            this.realName = params.realName;
        }
        if (params.idCard !== undefined) {
            this.idCard = params.idCard;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
    }
    updateStateVars(params: RegisterPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__phone.purgeDependencyOnElmtId(rmElmtId);
        this.__password.purgeDependencyOnElmtId(rmElmtId);
        this.__confirmPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__realName.purgeDependencyOnElmtId(rmElmtId);
        this.__idCard.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__phone.aboutToBeDeleted();
        this.__password.aboutToBeDeleted();
        this.__confirmPassword.aboutToBeDeleted();
        this.__realName.aboutToBeDeleted();
        this.__idCard.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __phone: ObservedPropertySimplePU<string>;
    get phone() {
        return this.__phone.get();
    }
    set phone(newValue: string) {
        this.__phone.set(newValue);
    }
    private __password: ObservedPropertySimplePU<string>;
    get password() {
        return this.__password.get();
    }
    set password(newValue: string) {
        this.__password.set(newValue);
    }
    private __confirmPassword: ObservedPropertySimplePU<string>;
    get confirmPassword() {
        return this.__confirmPassword.get();
    }
    set confirmPassword(newValue: string) {
        this.__confirmPassword.set(newValue);
    }
    private __realName: ObservedPropertySimplePU<string>;
    get realName() {
        return this.__realName.get();
    }
    set realName(newValue: string) {
        this.__realName.set(newValue);
    }
    private __idCard: ObservedPropertySimplePU<string>;
    get idCard() {
        return this.__idCard.get();
    }
    set idCard(newValue: string) {
        this.__idCard.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RegisterPage.ets(17:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/RegisterPage.ets(19:7)", "entry");
            // 顶部导航
            Row.width('100%');
            // 顶部导航
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/RegisterPage.ets(20:9)", "entry");
            Button.width(40);
            Button.height(40);
            Button.borderRadius(20);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('←');
            Text.debugLine("entry/src/main/ets/pages/RegisterPage.ets(21:11)", "entry");
            Text.fontSize(24);
            Text.fontColor('#333333');
        }, Text);
        Text.pop();
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('用户注册');
            Text.debugLine("entry/src/main/ets/pages/RegisterPage.ets(33:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 占位，保持标题居中
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/RegisterPage.ets(41:9)", "entry");
            // 占位，保持标题居中
            Row.width(40);
            // 占位，保持标题居中
            Row.height(40);
        }, Row);
        // 占位，保持标题居中
        Row.pop();
        // 顶部导航
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/RegisterPage.ets(46:7)", "entry");
            Scroll.layoutWeight(1);
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Off);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RegisterPage.ets(47:9)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 注册表单
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RegisterPage.ets(49:11)", "entry");
            // 注册表单
            Column.width('100%');
            // 注册表单
            Column.padding({ left: 24, right: 24, top: 20, bottom: 40 });
        }, Column);
        // 手机号
        this.InputField.bind(this)('手机号', '请输入手机号', this.phone, (value: string) => {
            this.phone = value;
        }, InputType.PhoneNumber, 11);
        // 真实姓名
        this.InputField.bind(this)('真实姓名', '请输入真实姓名', this.realName, (value: string) => {
            this.realName = value;
        });
        // 身份证号
        this.InputField.bind(this)('身份证号', '请输入身份证号', this.idCard, (value: string) => {
            this.idCard = value;
        }, InputType.Normal, 18);
        // 密码
        this.PasswordField.bind(this)('登录密码', '请输入6-20位密码', this.password, (value: string) => {
            this.password = value;
        });
        // 确认密码
        this.PasswordField.bind(this)('确认密码', '请再次输入密码', this.confirmPassword, (value: string) => {
            this.confirmPassword = value;
        });
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 注册按钮
            Button.createWithLabel('注册');
            Button.debugLine("entry/src/main/ets/pages/RegisterPage.ets(76:13)", "entry");
            // 注册按钮
            Button.width('100%');
            // 注册按钮
            Button.height(48);
            // 注册按钮
            Button.fontSize(16);
            // 注册按钮
            Button.fontColor(Color.White);
            // 注册按钮
            Button.backgroundColor('#1976D2');
            // 注册按钮
            Button.borderRadius(8);
            // 注册按钮
            Button.margin({ top: 30 });
            // 注册按钮
            Button.enabled(!this.isLoading && this.isFormValid());
            // 注册按钮
            Button.opacity((!this.isLoading && this.isFormValid()) ? 1 : 0.5);
            // 注册按钮
            Button.onClick(() => {
                this.handleRegister();
            });
        }, Button);
        // 注册按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 登录链接
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/RegisterPage.ets(91:13)", "entry");
            // 登录链接
            Row.margin({ top: 20 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('已有账号？');
            Text.debugLine("entry/src/main/ets/pages/RegisterPage.ets(92:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('立即登录');
            Text.debugLine("entry/src/main/ets/pages/RegisterPage.ets(96:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#1976D2');
            Text.onClick(() => {
                router.back();
            });
        }, Text);
        Text.pop();
        // 登录链接
        Row.pop();
        // 注册表单
        Column.pop();
        Column.pop();
        Scroll.pop();
        Column.pop();
    }
    InputField(label: string, placeholder: string, value: string, onChange: (value: string) => void, inputType: InputType = InputType.Normal, maxLength?: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RegisterPage.ets(127:5)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/RegisterPage.ets(128:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: placeholder });
            TextInput.debugLine("entry/src/main/ets/pages/RegisterPage.ets(134:7)", "entry");
            TextInput.type(inputType);
            TextInput.maxLength(maxLength);
            TextInput.fontSize(16);
            TextInput.height(48);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.onChange(onChange);
        }, TextInput);
        Column.pop();
    }
    PasswordField(label: string, placeholder: string, value: string, onChange: (value: string) => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/RegisterPage.ets(155:5)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/RegisterPage.ets(156:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: placeholder });
            TextInput.debugLine("entry/src/main/ets/pages/RegisterPage.ets(162:7)", "entry");
            TextInput.type(InputType.Password);
            TextInput.fontSize(16);
            TextInput.height(48);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.showPasswordIcon(true);
            TextInput.onChange(onChange);
        }, TextInput);
        Column.pop();
    }
    /**
     * 表单验证
     */
    isFormValid(): boolean {
        return this.phone.length === 11 &&
            this.realName.length >= 2 &&
            this.idCard.length === 18 &&
            this.password.length >= 6 &&
            this.password === this.confirmPassword;
    }
    /**
     * 处理注册
     */
    async handleRegister() {
        if (this.isLoading)
            return;
        // 表单验证
        if (!this.validateForm())
            return;
        this.isLoading = true;
        try {
            const registerData: UserRegisterRequest = {
                phone: this.phone,
                password: this.password,
                realName: this.realName,
                idCard: this.idCard
            };
            await UserApi.register(registerData);
            promptAction.showToast({ message: '注册成功，请登录' });
            // 返回登录页
            router.back();
        }
        catch (error) {
            console.error('注册失败:', error);
            promptAction.showToast({ message: '注册失败，请重试' });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 表单验证
     */
    validateForm(): boolean {
        if (this.phone.length !== 11) {
            promptAction.showToast({ message: '请输入正确的手机号' });
            return false;
        }
        if (this.realName.length < 2) {
            promptAction.showToast({ message: '请输入真实姓名' });
            return false;
        }
        if (this.idCard.length !== 18) {
            promptAction.showToast({ message: '请输入正确的身份证号' });
            return false;
        }
        if (this.password.length < 6) {
            promptAction.showToast({ message: '密码长度不能少于6位' });
            return false;
        }
        if (this.password !== this.confirmPassword) {
            promptAction.showToast({ message: '两次输入的密码不一致' });
            return false;
        }
        return true;
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "RegisterPage";
    }
}
registerNamedRoute(() => new RegisterPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/RegisterPage", pageFullPath: "entry/src/main/ets/pages/RegisterPage", integratedHsp: "false", moduleType: "followWithHap" });
