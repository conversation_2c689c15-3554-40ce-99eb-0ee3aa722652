{"version": "2.0", "ppid": 12876, "events": [{"head": {"id": "4758f47a-4975-4bfa-8590-5c7ff11def1a", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315607443500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7f887c0-d834-4873-b0e8-c7ceac3567d5", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315609422200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85c01eac-e583-4849-a8ca-8d6976460382", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315609629300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "650af044-34bb-4b51-8bc5-e344ee6cb56f", "name": "worker[0] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315617972000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d330ab9-9bc8-41e1-b1c5-57a8695cd935", "name": "worker[1] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315622877400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce55c73d-5727-4cfa-ac59-3473be301ab6", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315897580100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4adb00ef-ad83-4492-9169-fade0b7ebe13", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315905392400, "endTime": 11316120356800}, "additional": {"children": ["9674c96f-852b-4742-9198-526966e28d95", "35ec9ffb-1abd-4860-b420-d5c12eab37fe", "cf39c49a-c793-43d5-a852-cf93a4dfb47f", "aafbf4f5-6d82-42f1-a265-82dc4dde5e2b", "ea295af2-8958-46ae-8035-224ca1f8009f", "79bf4ede-227f-4a37-b265-0c7b7c8cf091", "cdf0b684-607f-4396-91f6-7bc169c70e34"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "67796a00-5cd4-43e3-9e6d-6f41b80db71b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9674c96f-852b-4742-9198-526966e28d95", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315905394100, "endTime": 11315916236700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4adb00ef-ad83-4492-9169-fade0b7ebe13", "logId": "54b98106-862b-4a4f-975e-d999512442e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35ec9ffb-1abd-4860-b420-d5c12eab37fe", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315916249800, "endTime": 11316119337300}, "additional": {"children": ["2c31dfc1-df70-4797-ba7c-8ebd3665369f", "7e2124f8-0c18-40ea-a21b-b2cafdb5df73", "1953675e-f403-46f3-a4e1-ed3ce84f80b1", "94dbe5af-1057-4b2e-b9b3-e55d09a2424a", "1135d037-ff63-4525-a3f8-011559a2e093", "b38bbd4d-6cd3-4a46-8db6-0cffe5157072", "054a2424-e2d1-4b49-926d-ab99b2575b71", "2d747989-d519-48ec-9b0d-871066336dc7", "7228f70a-55a9-4731-b1cd-572b9d5402d5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4adb00ef-ad83-4492-9169-fade0b7ebe13", "logId": "50d28ec2-bfa9-4d4f-aa75-b726001a3c3d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf39c49a-c793-43d5-a852-cf93a4dfb47f", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316119355000, "endTime": 11316120345900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4adb00ef-ad83-4492-9169-fade0b7ebe13", "logId": "d057ba4b-c749-4444-912a-e6d9f07dc58c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aafbf4f5-6d82-42f1-a265-82dc4dde5e2b", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316120349300, "endTime": 11316120354100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4adb00ef-ad83-4492-9169-fade0b7ebe13", "logId": "863f4514-36e6-45b9-86f5-d3b679a6538c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea295af2-8958-46ae-8035-224ca1f8009f", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315909003800, "endTime": 11315909040700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4adb00ef-ad83-4492-9169-fade0b7ebe13", "logId": "ea919963-ca49-4d55-8248-f8c4e0100eae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea919963-ca49-4d55-8248-f8c4e0100eae", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315909003800, "endTime": 11315909040700}, "additional": {"logType": "info", "children": [], "durationId": "ea295af2-8958-46ae-8035-224ca1f8009f", "parent": "67796a00-5cd4-43e3-9e6d-6f41b80db71b"}}, {"head": {"id": "79bf4ede-227f-4a37-b265-0c7b7c8cf091", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315914756100, "endTime": 11315914776300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4adb00ef-ad83-4492-9169-fade0b7ebe13", "logId": "d106f14b-95ec-4000-8127-f0311beb48f3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d106f14b-95ec-4000-8127-f0311beb48f3", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315914756100, "endTime": 11315914776300}, "additional": {"logType": "info", "children": [], "durationId": "79bf4ede-227f-4a37-b265-0c7b7c8cf091", "parent": "67796a00-5cd4-43e3-9e6d-6f41b80db71b"}}, {"head": {"id": "58833cee-1b95-4f2f-9177-2969c9fb4ca6", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315914844600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93614e69-9e24-4bc5-a9cd-4f36408b929f", "name": "Cache service initialization finished in 2 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315916120400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54b98106-862b-4a4f-975e-d999512442e5", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315905394100, "endTime": 11315916236700}, "additional": {"logType": "info", "children": [], "durationId": "9674c96f-852b-4742-9198-526966e28d95", "parent": "67796a00-5cd4-43e3-9e6d-6f41b80db71b"}}, {"head": {"id": "2c31dfc1-df70-4797-ba7c-8ebd3665369f", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315922991500, "endTime": 11315923004500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35ec9ffb-1abd-4860-b420-d5c12eab37fe", "logId": "28bd9259-fcd7-4744-b6d4-32d4b75bfb58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e2124f8-0c18-40ea-a21b-b2cafdb5df73", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315923019900, "endTime": 11315927921500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35ec9ffb-1abd-4860-b420-d5c12eab37fe", "logId": "8c41c82f-53a6-475e-b51e-6e31c3225c93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1953675e-f403-46f3-a4e1-ed3ce84f80b1", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315927934000, "endTime": 11316004264000}, "additional": {"children": ["21f30e08-7b08-49e2-8f79-bc0b5b1b8bbd", "85d8176b-a936-426d-be0a-9fce4bb337d7", "5088a804-885b-4301-afb8-2e301f29e375"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35ec9ffb-1abd-4860-b420-d5c12eab37fe", "logId": "c39a01a9-48e9-4f5f-b1ea-044b16c94266"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94dbe5af-1057-4b2e-b9b3-e55d09a2424a", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316004276500, "endTime": 11316028433200}, "additional": {"children": ["182f39f7-6c4e-40db-8d76-3f923f303651"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35ec9ffb-1abd-4860-b420-d5c12eab37fe", "logId": "731a0621-d2ad-4396-bde5-b19e0cbcfe19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1135d037-ff63-4525-a3f8-011559a2e093", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316028439400, "endTime": 11316042484300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35ec9ffb-1abd-4860-b420-d5c12eab37fe", "logId": "50bcb290-5faf-472e-b3e9-1bca1aa33149"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b38bbd4d-6cd3-4a46-8db6-0cffe5157072", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316044198300, "endTime": 11316108929700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35ec9ffb-1abd-4860-b420-d5c12eab37fe", "logId": "f3b6a625-1f83-42a7-bec2-06c396330133"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "054a2424-e2d1-4b49-926d-ab99b2575b71", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316108952500, "endTime": 11316119130700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35ec9ffb-1abd-4860-b420-d5c12eab37fe", "logId": "b9223ad4-185c-4310-8d6f-5a0480af127b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2d747989-d519-48ec-9b0d-871066336dc7", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316119146800, "endTime": 11316119326200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35ec9ffb-1abd-4860-b420-d5c12eab37fe", "logId": "6bd00a66-0005-4da5-aaeb-2f8aff3a60a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28bd9259-fcd7-4744-b6d4-32d4b75bfb58", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315922991500, "endTime": 11315923004500}, "additional": {"logType": "info", "children": [], "durationId": "2c31dfc1-df70-4797-ba7c-8ebd3665369f", "parent": "50d28ec2-bfa9-4d4f-aa75-b726001a3c3d"}}, {"head": {"id": "8c41c82f-53a6-475e-b51e-6e31c3225c93", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315923019900, "endTime": 11315927921500}, "additional": {"logType": "info", "children": [], "durationId": "7e2124f8-0c18-40ea-a21b-b2cafdb5df73", "parent": "50d28ec2-bfa9-4d4f-aa75-b726001a3c3d"}}, {"head": {"id": "21f30e08-7b08-49e2-8f79-bc0b5b1b8bbd", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315928604000, "endTime": 11315928623500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1953675e-f403-46f3-a4e1-ed3ce84f80b1", "logId": "f58f8243-8996-4b80-8b81-930a2a586115"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f58f8243-8996-4b80-8b81-930a2a586115", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315928604000, "endTime": 11315928623500}, "additional": {"logType": "info", "children": [], "durationId": "21f30e08-7b08-49e2-8f79-bc0b5b1b8bbd", "parent": "c39a01a9-48e9-4f5f-b1ea-044b16c94266"}}, {"head": {"id": "85d8176b-a936-426d-be0a-9fce4bb337d7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315932509600, "endTime": 11316003274500}, "additional": {"children": ["e730c964-1fbd-477f-80ae-67b1f11db331", "6787ca89-639f-44d7-8309-630666cfdf22"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1953675e-f403-46f3-a4e1-ed3ce84f80b1", "logId": "5270a32e-9a21-4c49-93d1-000ef8dbfef4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e730c964-1fbd-477f-80ae-67b1f11db331", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315932511000, "endTime": 11315937018900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85d8176b-a936-426d-be0a-9fce4bb337d7", "logId": "67e90d22-d063-41b2-a932-1195b2ba0a36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6787ca89-639f-44d7-8309-630666cfdf22", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315937035200, "endTime": 11316003266500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "85d8176b-a936-426d-be0a-9fce4bb337d7", "logId": "f492e19e-74b7-42bf-a1d0-c43cebd66901"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a395459c-ea2c-4331-8d15-4f355563da45", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315932520800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09230d31-a86b-49f4-b6b4-e283d9c61ddd", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315936891300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67e90d22-d063-41b2-a932-1195b2ba0a36", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315932511000, "endTime": 11315937018900}, "additional": {"logType": "info", "children": [], "durationId": "e730c964-1fbd-477f-80ae-67b1f11db331", "parent": "5270a32e-9a21-4c49-93d1-000ef8dbfef4"}}, {"head": {"id": "651f0f93-a973-4778-bd11-6aa30a839529", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315937049100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fd5028b-c157-471c-b80c-3edb99736262", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315944336600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05004537-8e32-466c-aefd-1857613a66e0", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315944448100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27f7aeac-c490-4ef5-8146-68a7a45eaf85", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315944799200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a09880f-09cf-43c5-808c-9dcc00509623", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315945065300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc3557a8-4aaa-4594-92c5-75660fc8b31e", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315946789400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d84de23-1044-44e2-a873-f4a702913b2d", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315950729100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b262077-8ba1-4aeb-ae24-8dc198dbb6d0", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315960324600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f6c30f6-2671-423a-a5b2-c4e806160567", "name": "Sdk init in 30 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315980903200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7784927c-40e8-4333-aef2-a99e1eba8f15", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315981045400}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 12, "minute": 14}, "markType": "other"}}, {"head": {"id": "b3187bac-829b-4515-8947-2ccbb8f7a95e", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315981059900}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 12, "minute": 14}, "markType": "other"}}, {"head": {"id": "e354eb74-687c-416d-afa9-87ca990c1dca", "name": "Project task initialization takes 21 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316003048800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37390c14-8e7a-402e-9607-fef7c7799c82", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316003158300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67827876-e9c1-47d0-92e0-eea991928bbb", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316003205700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0373802-99c8-408e-b4f6-f59f8726b80c", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\wallet\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316003236900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f492e19e-74b7-42bf-a1d0-c43cebd66901", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315937035200, "endTime": 11316003266500}, "additional": {"logType": "info", "children": [], "durationId": "6787ca89-639f-44d7-8309-630666cfdf22", "parent": "5270a32e-9a21-4c49-93d1-000ef8dbfef4"}}, {"head": {"id": "5270a32e-9a21-4c49-93d1-000ef8dbfef4", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315932509600, "endTime": 11316003274500}, "additional": {"logType": "info", "children": ["67e90d22-d063-41b2-a932-1195b2ba0a36", "f492e19e-74b7-42bf-a1d0-c43cebd66901"], "durationId": "85d8176b-a936-426d-be0a-9fce4bb337d7", "parent": "c39a01a9-48e9-4f5f-b1ea-044b16c94266"}}, {"head": {"id": "5088a804-885b-4301-afb8-2e301f29e375", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316004099100, "endTime": 11316004245000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1953675e-f403-46f3-a4e1-ed3ce84f80b1", "logId": "9cf03781-4d44-4379-b440-294ec2435321"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9cf03781-4d44-4379-b440-294ec2435321", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316004099100, "endTime": 11316004245000}, "additional": {"logType": "info", "children": [], "durationId": "5088a804-885b-4301-afb8-2e301f29e375", "parent": "c39a01a9-48e9-4f5f-b1ea-044b16c94266"}}, {"head": {"id": "c39a01a9-48e9-4f5f-b1ea-044b16c94266", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315927934000, "endTime": 11316004264000}, "additional": {"logType": "info", "children": ["f58f8243-8996-4b80-8b81-930a2a586115", "5270a32e-9a21-4c49-93d1-000ef8dbfef4", "9cf03781-4d44-4379-b440-294ec2435321"], "durationId": "1953675e-f403-46f3-a4e1-ed3ce84f80b1", "parent": "50d28ec2-bfa9-4d4f-aa75-b726001a3c3d"}}, {"head": {"id": "182f39f7-6c4e-40db-8d76-3f923f303651", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316004926300, "endTime": 11316028422100}, "additional": {"children": ["7bb6f508-86ee-46d2-bc5c-c458e1f091f3", "951bbdd7-847e-4f00-945f-8366cda81347", "fac4347a-030f-46cb-b25c-4d8aacbf17d7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "94dbe5af-1057-4b2e-b9b3-e55d09a2424a", "logId": "5d6e6066-71b0-4c1c-82ea-a82f8957b35e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7bb6f508-86ee-46d2-bc5c-c458e1f091f3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316008030500, "endTime": 11316008050000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "182f39f7-6c4e-40db-8d76-3f923f303651", "logId": "05dd7201-9078-4476-be19-13f096008959"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "05dd7201-9078-4476-be19-13f096008959", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316008030500, "endTime": 11316008050000}, "additional": {"logType": "info", "children": [], "durationId": "7bb6f508-86ee-46d2-bc5c-c458e1f091f3", "parent": "5d6e6066-71b0-4c1c-82ea-a82f8957b35e"}}, {"head": {"id": "951bbdd7-847e-4f00-945f-8366cda81347", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316010251900, "endTime": 11316027082000}, "additional": {"children": ["3e56e0ff-bce6-40b0-80f7-6c55810590fb", "f971cb35-e63f-4a07-a354-4bf5ca7e1f81"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "182f39f7-6c4e-40db-8d76-3f923f303651", "logId": "fb585824-aa1e-486e-80ae-14ca2835e025"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e56e0ff-bce6-40b0-80f7-6c55810590fb", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316010253000, "endTime": 11316015936900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "951bbdd7-847e-4f00-945f-8366cda81347", "logId": "5e9c253a-bc3e-449b-a1a5-ae607b20a5c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f971cb35-e63f-4a07-a354-4bf5ca7e1f81", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316015954600, "endTime": 11316027073100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "951bbdd7-847e-4f00-945f-8366cda81347", "logId": "54a0e52e-eb98-4604-9a7c-7c0422b13cec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0de84928-1c05-440b-a02f-d5e2cdc56974", "name": "hvigorfile, resolving D:\\vue\\daxiangmuwallet\\wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316010290600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63a74585-ccfd-46f6-8168-e2a663d95414", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316015796000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e9c253a-bc3e-449b-a1a5-ae607b20a5c0", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316010253000, "endTime": 11316015936900}, "additional": {"logType": "info", "children": [], "durationId": "3e56e0ff-bce6-40b0-80f7-6c55810590fb", "parent": "fb585824-aa1e-486e-80ae-14ca2835e025"}}, {"head": {"id": "e1f61c37-9ec9-42b0-924b-db4a7b6f1cad", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316015968600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cb15e53-9852-492f-81e6-c2484d2f5bcf", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316023318400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf79d849-fe0b-400c-9ca9-2fa6ab3e874e", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316023588500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83141827-bc45-48f3-8fd1-42419cdf725a", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316023784000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e1de7dd-1137-4b3c-bf6a-8688d41fe1a0", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316023886800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57159603-bc7e-4f32-9d2e-1dd44fc2cc73", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316023926200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3e63b82-b229-4d33-8347-f951620b092e", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316023960100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "613a42dd-5436-4648-83d6-ed33ffd5209f", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316023998700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd77953f-0041-4990-a432-05f14f233123", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316026840300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a80b1bce-45fb-49aa-9382-a9afc0d1a351", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316026969100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47be6c44-36a8-4fcd-9ca4-53d65078f3bf", "name": "hvigorfile, no custom plugins were found in D:\\vue\\daxiangmuwallet\\wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316027013900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff32757d-0684-421c-9306-0d4f1e01e474", "name": "hvigorfile, resolve finished D:\\vue\\daxiangmuwallet\\wallet\\entry\\hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316027044800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54a0e52e-eb98-4604-9a7c-7c0422b13cec", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316015954600, "endTime": 11316027073100}, "additional": {"logType": "info", "children": [], "durationId": "f971cb35-e63f-4a07-a354-4bf5ca7e1f81", "parent": "fb585824-aa1e-486e-80ae-14ca2835e025"}}, {"head": {"id": "fb585824-aa1e-486e-80ae-14ca2835e025", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316010251900, "endTime": 11316027082000}, "additional": {"logType": "info", "children": ["5e9c253a-bc3e-449b-a1a5-ae607b20a5c0", "54a0e52e-eb98-4604-9a7c-7c0422b13cec"], "durationId": "951bbdd7-847e-4f00-945f-8366cda81347", "parent": "5d6e6066-71b0-4c1c-82ea-a82f8957b35e"}}, {"head": {"id": "fac4347a-030f-46cb-b25c-4d8aacbf17d7", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316028388000, "endTime": 11316028405500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "182f39f7-6c4e-40db-8d76-3f923f303651", "logId": "7959fc61-9fe9-4106-9990-a7b8e69f6f6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7959fc61-9fe9-4106-9990-a7b8e69f6f6d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316028388000, "endTime": 11316028405500}, "additional": {"logType": "info", "children": [], "durationId": "fac4347a-030f-46cb-b25c-4d8aacbf17d7", "parent": "5d6e6066-71b0-4c1c-82ea-a82f8957b35e"}}, {"head": {"id": "5d6e6066-71b0-4c1c-82ea-a82f8957b35e", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316004926300, "endTime": 11316028422100}, "additional": {"logType": "info", "children": ["05dd7201-9078-4476-be19-13f096008959", "fb585824-aa1e-486e-80ae-14ca2835e025", "7959fc61-9fe9-4106-9990-a7b8e69f6f6d"], "durationId": "182f39f7-6c4e-40db-8d76-3f923f303651", "parent": "731a0621-d2ad-4396-bde5-b19e0cbcfe19"}}, {"head": {"id": "731a0621-d2ad-4396-bde5-b19e0cbcfe19", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316004276500, "endTime": 11316028433200}, "additional": {"logType": "info", "children": ["5d6e6066-71b0-4c1c-82ea-a82f8957b35e"], "durationId": "94dbe5af-1057-4b2e-b9b3-e55d09a2424a", "parent": "50d28ec2-bfa9-4d4f-aa75-b726001a3c3d"}}, {"head": {"id": "4c1965f7-0256-4e4b-81e7-befc05095623", "name": "watch files: [\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\hvigorfile.ts',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\hvigorfile.ts'\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316041785900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4e43007-2a05-4071-9057-29ab2a515843", "name": "hvigorfile, resolve hvigorfile dependencies in 14 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316042366200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50bcb290-5faf-472e-b3e9-1bca1aa33149", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316028439400, "endTime": 11316042484300}, "additional": {"logType": "info", "children": [], "durationId": "1135d037-ff63-4525-a3f8-011559a2e093", "parent": "50d28ec2-bfa9-4d4f-aa75-b726001a3c3d"}}, {"head": {"id": "7228f70a-55a9-4731-b1cd-572b9d5402d5", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316043854100, "endTime": 11316044177700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35ec9ffb-1abd-4860-b420-d5c12eab37fe", "logId": "0aca71db-c9dd-4a6b-a9bb-3d26ff7c0f95"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91e2c1f9-0601-46e4-97d0-a9e2e8724ca5", "name": "project has submodules:entry", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316043921300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aca71db-c9dd-4a6b-a9bb-3d26ff7c0f95", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316043854100, "endTime": 11316044177700}, "additional": {"logType": "info", "children": [], "durationId": "7228f70a-55a9-4731-b1cd-572b9d5402d5", "parent": "50d28ec2-bfa9-4d4f-aa75-b726001a3c3d"}}, {"head": {"id": "7459c426-7d0f-4bac-85bc-0d6db82ea43c", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316047381800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6e0c0e1-97a6-4ce7-a62e-ab2a4f1c205c", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316108075600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3b6a625-1f83-42a7-bec2-06c396330133", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316044198300, "endTime": 11316108929700}, "additional": {"logType": "info", "children": [], "durationId": "b38bbd4d-6cd3-4a46-8db6-0cffe5157072", "parent": "50d28ec2-bfa9-4d4f-aa75-b726001a3c3d"}}, {"head": {"id": "9dd47b85-6a0f-46a0-a027-ebb0fb784fd6", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316108970200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5c8582e-a004-4651-8b6b-180486f1dfde", "name": "<PERSON><PERSON>le wallet Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316114009800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9eac24c-13b4-494f-b25b-207153e1e78b", "name": "Module wallet's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316114116600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88c7e7a1-0e0f-4d4c-8849-daac14ce023b", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316114310900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0e4b7f5-5ca8-423d-b159-2ffd93bdfc69", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316116568700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "807d36ef-c036-4140-88c3-ac4b071feb85", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316116659600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9223ad4-185c-4310-8d6f-5a0480af127b", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316108952500, "endTime": 11316119130700}, "additional": {"logType": "info", "children": [], "durationId": "054a2424-e2d1-4b49-926d-ab99b2575b71", "parent": "50d28ec2-bfa9-4d4f-aa75-b726001a3c3d"}}, {"head": {"id": "405be0ce-6a82-4b25-98f5-5897b6808597", "name": "Configuration phase cost:197 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316119253600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bd00a66-0005-4da5-aaeb-2f8aff3a60a2", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316119146800, "endTime": 11316119326200}, "additional": {"logType": "info", "children": [], "durationId": "2d747989-d519-48ec-9b0d-871066336dc7", "parent": "50d28ec2-bfa9-4d4f-aa75-b726001a3c3d"}}, {"head": {"id": "50d28ec2-bfa9-4d4f-aa75-b726001a3c3d", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315916249800, "endTime": 11316119337300}, "additional": {"logType": "info", "children": ["28bd9259-fcd7-4744-b6d4-32d4b75bfb58", "8c41c82f-53a6-475e-b51e-6e31c3225c93", "c39a01a9-48e9-4f5f-b1ea-044b16c94266", "731a0621-d2ad-4396-bde5-b19e0cbcfe19", "50bcb290-5faf-472e-b3e9-1bca1aa33149", "f3b6a625-1f83-42a7-bec2-06c396330133", "b9223ad4-185c-4310-8d6f-5a0480af127b", "6bd00a66-0005-4da5-aaeb-2f8aff3a60a2", "0aca71db-c9dd-4a6b-a9bb-3d26ff7c0f95"], "durationId": "35ec9ffb-1abd-4860-b420-d5c12eab37fe", "parent": "67796a00-5cd4-43e3-9e6d-6f41b80db71b"}}, {"head": {"id": "cdf0b684-607f-4396-91f6-7bc169c70e34", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316120324700, "endTime": 11316120337100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4adb00ef-ad83-4492-9169-fade0b7ebe13", "logId": "a5a425cc-8a22-4123-8357-516e2b8081d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5a425cc-8a22-4123-8357-516e2b8081d6", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316120324700, "endTime": 11316120337100}, "additional": {"logType": "info", "children": [], "durationId": "cdf0b684-607f-4396-91f6-7bc169c70e34", "parent": "67796a00-5cd4-43e3-9e6d-6f41b80db71b"}}, {"head": {"id": "d057ba4b-c749-4444-912a-e6d9f07dc58c", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316119355000, "endTime": 11316120345900}, "additional": {"logType": "info", "children": [], "durationId": "cf39c49a-c793-43d5-a852-cf93a4dfb47f", "parent": "67796a00-5cd4-43e3-9e6d-6f41b80db71b"}}, {"head": {"id": "863f4514-36e6-45b9-86f5-d3b679a6538c", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316120349300, "endTime": 11316120354100}, "additional": {"logType": "info", "children": [], "durationId": "aafbf4f5-6d82-42f1-a265-82dc4dde5e2b", "parent": "67796a00-5cd4-43e3-9e6d-6f41b80db71b"}}, {"head": {"id": "67796a00-5cd4-43e3-9e6d-6f41b80db71b", "name": "init", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315905392400, "endTime": 11316120356800}, "additional": {"logType": "info", "children": ["54b98106-862b-4a4f-975e-d999512442e5", "50d28ec2-bfa9-4d4f-aa75-b726001a3c3d", "d057ba4b-c749-4444-912a-e6d9f07dc58c", "863f4514-36e6-45b9-86f5-d3b679a6538c", "ea919963-ca49-4d55-8248-f8c4e0100eae", "d106f14b-95ec-4000-8127-f0311beb48f3", "a5a425cc-8a22-4123-8357-516e2b8081d6"], "durationId": "4adb00ef-ad83-4492-9169-fade0b7ebe13"}}, {"head": {"id": "a80eee89-4240-499b-95c6-87ca0610a01d", "name": "Configuration task cost before running: 219 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316120573600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be9d295c-ba9f-4b9b-876d-0251993ad799", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316127318300, "endTime": 11316242220600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "3f46b1e6-d8e2-4b1b-9e49-44a272d4e5e6", "logId": "ab16f90f-c907-4609-81be-76cd03a1b007"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f46b1e6-d8e2-4b1b-9e49-44a272d4e5e6", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316122245500}, "additional": {"logType": "detail", "children": [], "durationId": "be9d295c-ba9f-4b9b-876d-0251993ad799"}}, {"head": {"id": "6e990579-5cca-4b38-b818-5371e53420e0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316123027900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42993688-fc8c-4d0b-86e7-25844173ded2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316123107500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6f1b9a1-3973-4222-af09-420c81251b8a", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316127336700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "775e9a3d-f519-4e63-881c-f6ba2559a347", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316133934400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8e84448-d09f-4eea-9b48-e48ae1647e76", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316134055000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7f44b6e-41dc-47fb-95cd-78f96cc61f33", "name": "current product is not Atomic service.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316240381400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bed2cfe-615c-4c47-bd92-3c4ba75b60fd", "name": "Use tool [win32: JAVA_HOME, CLASSPATH]\n [\n  { JAVA_HOME: 'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\jbr' },\n  { CLASSPATH: undefined }\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316241104500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a952d1d-06e4-4576-98c8-aa56b42099b2", "name": "Use tool [win32: NODE_HOME]\n [ { NODE_HOME: 'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\tools\\\\node' } ]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316241246500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d8b08e2-9f4a-43fb-80fb-851e57143b47", "name": "entry : default@PreBuild cost memory 12.284629821777344", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316242033500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2ad983c-1494-4fd9-b7f0-4a1eda3a8350", "name": "runTaskFromQueue task cost before running: 341 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316242163300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab16f90f-c907-4609-81be-76cd03a1b007", "name": "Finished :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316127318300, "endTime": 11316242220600, "totalTime": 114823200}, "additional": {"logType": "info", "children": [], "durationId": "be9d295c-ba9f-4b9b-876d-0251993ad799"}}, {"head": {"id": "a5d7815f-f161-47b0-b237-5ffcb87a76de", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316247972500, "endTime": 11316255176600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "30ac41be-5421-420e-b9f5-da2898dc03da", "logId": "5301a35c-25f5-492e-a4b6-adefe20e12e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30ac41be-5421-420e-b9f5-da2898dc03da", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316246422900}, "additional": {"logType": "detail", "children": [], "durationId": "a5d7815f-f161-47b0-b237-5ffcb87a76de"}}, {"head": {"id": "66c3964c-0d2c-4522-88f1-9bf1b6fe421a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316247084300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12271812-0915-4885-a8fc-152c82a4b7b0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316247187900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdac7e01-2094-411c-9f4b-72dad7dfac84", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316247985300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29659a6c-793a-4a68-9e26-0e664254768d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316249738500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ed97e2e-9901-4f10-9eb1-697d06420294", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316249938900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdae8a9e-39a6-46e2-9e16-dbf003993e85", "name": "Change app api release type with 'Release'", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316250352500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d6b6b95-b840-47ce-a001-b92eb15b2dbc", "name": "Change app compile API version with '*********'", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316250586400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9b5c179-6b9f-428e-87df-d4d043cd2268", "name": "Change app target API version with '50003015'", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316250649500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f71b614-a084-4b1a-8af4-c2e4daf15ab5", "name": "Change app minimum API version with '50003015'", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316250685600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2563ecee-1dd1-4039-be83-98cee3f36dec", "name": "Use cli appEnvironment", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316250797700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b306a6e4-46bf-4bf0-8d07-d5235ef50b85", "name": "entry : default@MergeProfile cost memory -13.746086120605469", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316254919900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0d9502e-835c-400e-9312-d78367c13588", "name": "runTaskFromQueue task cost before running: 354 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316255100600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5301a35c-25f5-492e-a4b6-adefe20e12e3", "name": "Finished :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316247972500, "endTime": 11316255176600, "totalTime": 7094800}, "additional": {"logType": "info", "children": [], "durationId": "a5d7815f-f161-47b0-b237-5ffcb87a76de"}}, {"head": {"id": "135d59d1-d86c-493b-81b0-2923abb436d1", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316258799000, "endTime": 11316263060300}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ca2a5abc-f433-48f7-bf85-e3422a5e12ea", "logId": "03c9457e-ccd2-48d8-9b0f-40de35d7aa10"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca2a5abc-f433-48f7-bf85-e3422a5e12ea", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316257097100}, "additional": {"logType": "detail", "children": [], "durationId": "135d59d1-d86c-493b-81b0-2923abb436d1"}}, {"head": {"id": "13678fb9-8b6d-4814-8626-1e8d3a80618e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316257685900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60edf0e2-6f66-4bae-924a-c8e31fe8071c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316257814600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fe9ca64-5115-4b3d-8327-8ced754c549f", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316258814600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78ee42d5-d7e2-4d08-bc39-14d900414e91", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316260167900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0be961cf-e7e3-4f4f-b662-c0f9c0969917", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316260439500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7034b1b-63e5-4042-9417-4edccc29ac7a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316260501800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9597764-8c08-429d-b823-5d436a00e640", "name": "entry : default@CreateBuildProfile cost memory 0.1068267822265625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316262848300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5d073fe-ea87-4421-a0d3-dfe1f09c9e9b", "name": "runTaskFromQueue task cost before running: 362 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316263008000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03c9457e-ccd2-48d8-9b0f-40de35d7aa10", "name": "Finished :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316258799000, "endTime": 11316263060300, "totalTime": 4177200}, "additional": {"logType": "info", "children": [], "durationId": "135d59d1-d86c-493b-81b0-2923abb436d1"}}, {"head": {"id": "6f3900b7-2007-4a2d-b635-58817e4c09be", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316266307600, "endTime": 11316266816900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "3f353078-7785-4c16-9a8d-7cf192093674", "logId": "57fe44bc-e4c1-486f-8a23-e0ebe5c20b4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f353078-7785-4c16-9a8d-7cf192093674", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316264895500}, "additional": {"logType": "detail", "children": [], "durationId": "6f3900b7-2007-4a2d-b635-58817e4c09be"}}, {"head": {"id": "e299c9ab-75f5-4b11-ad84-be62b82db2d0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316265460800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0eb9c3c-b50e-4d25-bda3-c7fa99831152", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316265567100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5dc1179-639b-4e40-a0b9-5e6cc12e91b0", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316266319400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68814087-1130-455b-8172-21dead1ff666", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316266442700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47751c89-f876-4cd3-93de-a2253607b0b4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316266482300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74465a58-e245-4552-8553-b8982cd8bfe0", "name": "entry : default@PreCheckSyscap cost memory 0.03759765625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316266678600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78dba6ab-6591-460d-bac6-a66e36a3a680", "name": "runTaskFromQueue task cost before running: 366 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316266775600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57fe44bc-e4c1-486f-8a23-e0ebe5c20b4c", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316266307600, "endTime": 11316266816900, "totalTime": 449300}, "additional": {"logType": "info", "children": [], "durationId": "6f3900b7-2007-4a2d-b635-58817e4c09be"}}, {"head": {"id": "1aeb61cb-8637-4325-bef9-840609d55080", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316279280500, "endTime": 11316280679000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "484c3616-0100-4ba6-8cd8-82029dfb5cb7", "logId": "3bd001c2-5e4e-4fa8-9905-b94cc366586b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "484c3616-0100-4ba6-8cd8-82029dfb5cb7", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316268768800}, "additional": {"logType": "detail", "children": [], "durationId": "1aeb61cb-8637-4325-bef9-840609d55080"}}, {"head": {"id": "f22adf96-72a4-458f-94d1-b73b633f4c7d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316269340600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "230d4ef1-0989-465e-966d-850536191ace", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316269720000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc54db69-c432-4b04-96e2-3aa2e179bf3f", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316279299100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "343d3d09-9cc3-47c3-8b02-574a0ce1a5d9", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316279628700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac3e992c-b728-45dd-be2a-c8f60439b394", "name": "entry : default@GeneratePkgContextInfo cost memory 0.04282379150390625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316280453800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efca13c0-96da-4dab-9f84-894f377c65dd", "name": "runTaskFromQueue task cost before running: 379 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316280597700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bd001c2-5e4e-4fa8-9905-b94cc366586b", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316279280500, "endTime": 11316280679000, "totalTime": 1290700}, "additional": {"logType": "info", "children": [], "durationId": "1aeb61cb-8637-4325-bef9-840609d55080"}}, {"head": {"id": "f6d963fa-12ad-4951-bf64-0a07f52e3723", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316285106800, "endTime": 11316475054000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "7ecf9614-1e30-4f8a-97ea-e994b3bc00fe", "logId": "e17e9c35-c238-4925-b771-50b32d62baf1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ecf9614-1e30-4f8a-97ea-e994b3bc00fe", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316282491600}, "additional": {"logType": "detail", "children": [], "durationId": "f6d963fa-12ad-4951-bf64-0a07f52e3723"}}, {"head": {"id": "0298cd62-33e3-4f1e-bd1c-6137c3fb4b3b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316283125700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c92810b5-3cb8-4e6e-9f8c-69dcc185fa64", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316283225100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6faf628-5124-4b9f-9f13-e84f7111a2bb", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316285121000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3fe338e-f956-48c9-bce4-cea36c36ede8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316285502500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30f1e317-38f0-40a5-bac6-a0aaed123d42", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316285574200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "048c47fe-25d0-4cbe-a4d5-f6c1c65b4d3e", "name": "********", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316469941100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32420623-a6a0-439d-adf8-b2ccef727a9c", "name": "entry : default@ProcessProfile cost memory 1.9788284301757812", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316474801200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8995ea3d-148c-4c0a-ae97-f9384379a11f", "name": "runTaskFromQueue task cost before running: 574 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316474981400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e17e9c35-c238-4925-b771-50b32d62baf1", "name": "Finished :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316285106800, "endTime": 11316475054000, "totalTime": 189844700}, "additional": {"logType": "info", "children": [], "durationId": "f6d963fa-12ad-4951-bf64-0a07f52e3723"}}, {"head": {"id": "b93c1dd8-f482-488f-ad91-7963d1bfe2d7", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316481440400, "endTime": 11316488217900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1c38adac-4d8f-460a-9d88-b5e51b2b7bcf", "logId": "9706e240-c1a8-4df0-a8ec-eed7bb10d4a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c38adac-4d8f-460a-9d88-b5e51b2b7bcf", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316477633500}, "additional": {"logType": "detail", "children": [], "durationId": "b93c1dd8-f482-488f-ad91-7963d1bfe2d7"}}, {"head": {"id": "5760b558-4f17-4e4c-85df-0e20ecb29e14", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316478782100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28b7c828-24d0-48aa-83b8-827a3444a718", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316478915600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d26f3ae-bb34-4758-addd-e398144e0fcf", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316481466600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1aa0803-b693-4ddb-959b-236a45dd66eb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316484630400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "065b090d-ccb9-45c5-862f-4f504c75e176", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316484743300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7bd5b4b-ec76-4fdb-a1bc-b4f3f6e99c44", "name": "entry : default@ProcessRouterMap cost memory 0.150177001953125", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316487971300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8416a281-06b5-4bc2-993f-1ae5629697f4", "name": "runTaskFromQueue task cost before running: 587 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316488141200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9706e240-c1a8-4df0-a8ec-eed7bb10d4a5", "name": "Finished :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316481440400, "endTime": 11316488217900, "totalTime": 6670700}, "additional": {"logType": "info", "children": [], "durationId": "b93c1dd8-f482-488f-ad91-7963d1bfe2d7"}}, {"head": {"id": "5dcf828c-3757-42b1-805d-bb5347c2611e", "name": "entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316497323800, "endTime": 11316500587700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "9578c6f0-002b-4982-953d-4b9945c1744e", "logId": "d4891802-cd7f-4342-906d-47e832b259d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9578c6f0-002b-4982-953d-4b9945c1744e", "name": "create entry:default@PreviewProcessResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316491639400}, "additional": {"logType": "detail", "children": [], "durationId": "5dcf828c-3757-42b1-805d-bb5347c2611e"}}, {"head": {"id": "77c3da86-5fed-466a-b4c8-e8599b7a8206", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316492378400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4764529a-e684-49d1-adcb-62d50c89713b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316492756600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c14b75ad-5888-49b7-a6d0-c1f21c43db5b", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316494861300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b18e09d-8fd1-48c9-b170-92d308caaac5", "name": "Executing task :entry:default@PreviewProcessResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316498999200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "733232c7-3bb2-438a-a346-cb806c84b6f4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316499196100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5fc496f-886c-4c5b-82b2-c312a60f6698", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316499276200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11580b39-dbb9-4088-a5e4-d47baa6ed230", "name": "entry : default@PreviewProcessResource cost memory 0.07375335693359375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316499352400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9428869e-7b0e-4131-9889-3b36f7032d25", "name": "runTaskFromQueue task cost before running: 599 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316500476300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4891802-cd7f-4342-906d-47e832b259d2", "name": "Finished :entry:default@PreviewProcessResource", "description": "Process preview resources incrementally in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316497323800, "endTime": 11316500587700, "totalTime": 2078100}, "additional": {"logType": "info", "children": [], "durationId": "5dcf828c-3757-42b1-805d-bb5347c2611e"}}, {"head": {"id": "2f798bb4-bb8c-4590-8b5b-917d538ae6c0", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316509029200, "endTime": 11316533476000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7ddedd90-bf4d-450a-a484-9874448d0dcc", "logId": "b3420867-d027-4eab-bf09-67e3bce90391"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ddedd90-bf4d-450a-a484-9874448d0dcc", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316503767100}, "additional": {"logType": "detail", "children": [], "durationId": "2f798bb4-bb8c-4590-8b5b-917d538ae6c0"}}, {"head": {"id": "ef072e6f-6dd3-4d24-85c4-ee2be3b6964c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316504349800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4edf9e3-b5ed-4356-a824-d92175eadcb6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316504452400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab804adf-9c22-4787-8e06-5af9986f2a0b", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316509052200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f925bb7d-4dfd-4ed2-a525-2d4a756fea63", "name": "entry : default@GenerateLoaderJson cost memory 0.7722091674804688", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316533227000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7a1bf5c-ce38-4fef-91c1-2f8a8dcc9c82", "name": "runTaskFromQueue task cost before running: 632 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316533411100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3420867-d027-4eab-bf09-67e3bce90391", "name": "Finished :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316509029200, "endTime": 11316533476000, "totalTime": 24349500}, "additional": {"logType": "info", "children": [], "durationId": "2f798bb4-bb8c-4590-8b5b-917d538ae6c0"}}, {"head": {"id": "8cd0baee-83e8-463e-9b05-bfdccfb46517", "name": "entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316545650700, "endTime": 11316788086900}, "additional": {"children": ["1cbefa11-30dc-4360-84c2-8c914ea054cb", "6c4bc486-1fa9-48aa-9db2-be1610c48080", "b0092119-a66c-48c1-b972-e8633ab1a797", "51a91a8c-9f3d-4343-a2c0-0e47c1722e89"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "06c77ba3-f874-42ad-97fb-63d3fa701a13", "logId": "28bdf42f-812c-4a90-9a64-7000e5d0d420"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "06c77ba3-f874-42ad-97fb-63d3fa701a13", "name": "create entry:default@PreviewCompileResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316541661700}, "additional": {"logType": "detail", "children": [], "durationId": "8cd0baee-83e8-463e-9b05-bfdccfb46517"}}, {"head": {"id": "98c6a825-bd76-4068-8a09-36f90006fee3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316542328200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33be390a-7f4e-45d6-b32b-a46ea400a992", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316542453700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aacb8e51-ba2a-4613-add4-290409036058", "name": "restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316543414900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1431e8d4-bf2a-4509-9738-e54977cda1c1", "name": "Executing task :entry:default@PreviewCompileResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316545768600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cbefa11-30dc-4360-84c2-8c914ea054cb", "name": "generate compilation link command", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316549410000, "endTime": 11316553321600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8cd0baee-83e8-463e-9b05-bfdccfb46517", "logId": "00a21c74-541e-4e6f-87f1-ae799b12be41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00a21c74-541e-4e6f-87f1-ae799b12be41", "name": "generate compilation link command", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316549410000, "endTime": 11316553321600}, "additional": {"logType": "info", "children": [], "durationId": "1cbefa11-30dc-4360-84c2-8c914ea054cb", "parent": "28bdf42f-812c-4a90-9a64-7000e5d0d420"}}, {"head": {"id": "23e93a67-9aec-4a5b-a038-9539554b512d", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\AppScope\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316553924200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c4bc486-1fa9-48aa-9db2-be1610c48080", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316554861000, "endTime": 11316657904800}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8cd0baee-83e8-463e-9b05-bfdccfb46517", "logId": "28efd03d-d017-4c8c-aa80-a2e5f9a895ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58fd3189-0881-4e12-93fa-8382004eec53", "name": "current process  memoryUsage: {\n  rss: 209657856,\n  heapTotal: 158199808,\n  heapUsed: 130996128,\n  external: 3295817,\n  arrayBuffers: 285072\n} os memoryUsage :12.628124237060547", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316555924900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6de0c18a-43dd-4d40-b141-8b4f38bfdef5", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316654756900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28efd03d-d017-4c8c-aa80-a2e5f9a895ed", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316554861000, "endTime": 11316657904800}, "additional": {"logType": "info", "children": [], "durationId": "6c4bc486-1fa9-48aa-9db2-be1610c48080", "parent": "28bdf42f-812c-4a90-9a64-7000e5d0d420"}}, {"head": {"id": "5e8a934b-11d5-4b61-9a1d-2f210db941a4", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-x',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\src\\\\main\\\\resources',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled'\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316658183000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0092119-a66c-48c1-b972-e8633ab1a797", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316659149500, "endTime": 11316704212500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8cd0baee-83e8-463e-9b05-bfdccfb46517", "logId": "e098dead-ade1-4384-b8dc-830814471a70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88630b7f-b0e9-4952-ae7c-8d6654b6070a", "name": "current process  memoryUsage: {\n  rss: 209686528,\n  heapTotal: 158199808,\n  heapUsed: 131258120,\n  external: 3295943,\n  arrayBuffers: 285213\n} os memoryUsage :12.629745483398438", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316660046300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bac5f02-76c6-4bfc-b0ce-bc6a59b7b621", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316694825400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e098dead-ade1-4384-b8dc-830814471a70", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316659149500, "endTime": 11316704212500}, "additional": {"logType": "info", "children": [], "durationId": "b0092119-a66c-48c1-b972-e8633ab1a797", "parent": "28bdf42f-812c-4a90-9a64-7000e5d0d420"}}, {"head": {"id": "5fd92ff2-29b2-4bb8-9668-8b03f3f3c04e", "name": "Use tool [D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe]\n [\n  'D:\\\\app\\\\devecostudio\\\\DevEco Studio\\\\sdk\\\\default\\\\openharmony\\\\toolchains\\\\restool.exe',\n  '-m',\n  'entry',\n  '-f',\n  '-j',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\process_profile\\\\default\\\\module.json',\n  '-p',\n  'c***n',\n  '-r',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\generated\\\\r\\\\default\\\\ResourceTable.h',\n  '-z',\n  '--ids',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\ids_map',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\app_compiled',\n  '-i',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default\\\\module_compiled',\n  '-o',\n  'D:\\\\vue\\\\daxiangmuwallet\\\\wallet\\\\entry\\\\.preview\\\\default\\\\intermediates\\\\res\\\\default'\n]", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316704868600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51a91a8c-9f3d-4343-a2c0-0e47c1722e89", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316705958400, "endTime": 11316786050900}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8cd0baee-83e8-463e-9b05-bfdccfb46517", "logId": "d332341e-5fbb-4984-ad97-4960cf2b089a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5df7a461-e20d-4677-96b5-53e2a828d0c4", "name": "current process  memoryUsage: {\n  rss: 209768448,\n  heapTotal: 158199808,\n  heapUsed: 131534608,\n  external: 3304261,\n  arrayBuffers: 294238\n} os memoryUsage :12.63656234741211", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316706961100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "503926ef-32a8-492e-b9be-b6d0bb5b3b75", "name": "Info: restool resources compile success.\r\n", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316768494200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d332341e-5fbb-4984-ad97-4960cf2b089a", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316705958400, "endTime": 11316786050900}, "additional": {"logType": "info", "children": [], "durationId": "51a91a8c-9f3d-4343-a2c0-0e47c1722e89", "parent": "28bdf42f-812c-4a90-9a64-7000e5d0d420"}}, {"head": {"id": "67d509cc-c498-41e5-b7b8-6a04ad7f99da", "name": "entry : default@PreviewCompileResource cost memory 1.1359710693359375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316787809400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e583f305-bc5b-4f75-b26d-09498a3400da", "name": "runTaskFromQueue task cost before running: 887 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316788016700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28bdf42f-812c-4a90-9a64-7000e5d0d420", "name": "Finished :entry:default@PreviewCompileResource", "description": "Use Preview to compile project resources in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316545650700, "endTime": 11316788086900, "totalTime": 242317200}, "additional": {"logType": "info", "children": ["00a21c74-541e-4e6f-87f1-ae799b12be41", "28efd03d-d017-4c8c-aa80-a2e5f9a895ed", "e098dead-ade1-4384-b8dc-830814471a70", "d332341e-5fbb-4984-ad97-4960cf2b089a"], "durationId": "8cd0baee-83e8-463e-9b05-bfdccfb46517"}}, {"head": {"id": "028a40fd-6e1f-4349-b1a5-ca6fa1c7f113", "name": "entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316792098300, "endTime": 11316792419400}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "d23f8f84-1b0f-4e95-8afc-9c7fc41c4ae2", "logId": "48d371f3-8c3e-4b42-aca3-c1f5375821a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d23f8f84-1b0f-4e95-8afc-9c7fc41c4ae2", "name": "create entry:default@PreviewHookCompileResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316791247400}, "additional": {"logType": "detail", "children": [], "durationId": "028a40fd-6e1f-4349-b1a5-ca6fa1c7f113"}}, {"head": {"id": "414570ea-0a6f-4435-8b82-2a6d6495b87f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316791868100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9654fbb1-58da-4187-a030-7a3df80aaab9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316792001000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01ef8078-5716-43eb-a6dc-62104326224a", "name": "Executing task :entry:default@PreviewHookCompileResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316792109800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5e6f4e5-10ae-4ee9-b03f-eaec738d148f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316792196200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b4ec417-a234-4a86-b615-1f60b4fdafcb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316792227600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7f7c022-5fab-4d44-b3fc-7d3e7883c010", "name": "entry : default@PreviewHookCompileResource cost memory 0.039154052734375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316792315800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36945df7-463e-43d1-ab8c-ff49f75cdbc1", "name": "runTaskFromQueue task cost before running: 891 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316792380300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48d371f3-8c3e-4b42-aca3-c1f5375821a9", "name": "Finished :entry:default@PreviewHookCompileResource", "description": "Add a hook task for compiling preview resources.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316792098300, "endTime": 11316792419400, "totalTime": 261700}, "additional": {"logType": "info", "children": [], "durationId": "028a40fd-6e1f-4349-b1a5-ca6fa1c7f113"}}, {"head": {"id": "f8a3c668-5005-4859-b505-f847042aba2e", "name": "entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316795749700, "endTime": 11316803539700}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "d2690110-458a-48cd-a6a3-3720c1a72156", "logId": "902bcb7d-6202-4001-a144-02f349715fce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2690110-458a-48cd-a6a3-3720c1a72156", "name": "create entry:default@CopyPreviewProfile task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316794379200}, "additional": {"logType": "detail", "children": [], "durationId": "f8a3c668-5005-4859-b505-f847042aba2e"}}, {"head": {"id": "a5ff5529-0371-4653-b67f-c47e3bb4c56c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316794959100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c70ee7c8-03dd-44ca-aea6-c353a678a123", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316795088400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "879d76cd-f0e9-4106-b005-751556cb6cc7", "name": "Executing task :entry:default@CopyPreviewProfile", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316795766000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "161e6aea-3ea6-424a-8098-bb9d6e9baf50", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316796483400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39c5ac01-b44d-4680-ba0a-c846c674011c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316796582300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8d8754d-ec2e-4eb5-9bf7-967f919d620f", "name": "entry : default@CopyPreviewProfile cost memory 0.40596771240234375", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316803194600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4060819e-dcb4-4e4b-a245-b2aaa82af67f", "name": "runTaskFromQueue task cost before running: 902 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316803380100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "902bcb7d-6202-4001-a144-02f349715fce", "name": "Finished :entry:default@CopyPreviewProfile", "description": "Copy preview profile.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316795749700, "endTime": 11316803539700, "totalTime": 7590600}, "additional": {"logType": "info", "children": [], "durationId": "f8a3c668-5005-4859-b505-f847042aba2e"}}, {"head": {"id": "dde02493-02ee-48f8-bdfb-d5b98bea1a5b", "name": "entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316807312300, "endTime": 11316807902900}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Other", "taskRunReasons": [], "detailId": "c8acf505-2c1f-42e7-8f79-5bca79f273c6", "logId": "a6625da3-5f61-48b1-aed0-b954d1a4c19b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8acf505-2c1f-42e7-8f79-5bca79f273c6", "name": "create entry:default@ReplacePreviewerPage task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316805618100}, "additional": {"logType": "detail", "children": [], "durationId": "dde02493-02ee-48f8-bdfb-d5b98bea1a5b"}}, {"head": {"id": "cf190453-93af-4fce-90a1-f8003cdb5038", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316806215800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4431695-f89d-4f39-b440-8ec3eeb1ca9d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316806331700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf78ee63-25cc-4227-98e6-c41adf837089", "name": "Executing task :entry:default@ReplacePreviewerPage", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316807330400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14839866-fcc8-403d-8e78-1d2d9750c4b5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316807494500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ad4b73c-7f1b-43d7-b63c-7bb1934acc06", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316807544800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b04320d5-a6bd-46ff-a2e7-e232f53d7019", "name": "entry : default@ReplacePreviewerPage cost memory 0.03907012939453125", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316807768300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88dabff1-9f14-4703-bb7a-d87a9a89f3f6", "name": "runTaskFromQueue task cost before running: 907 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316807861800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6625da3-5f61-48b1-aed0-b954d1a4c19b", "name": "Finished :entry:default@ReplacePreviewerPage", "description": "Accept previewer arguments, then use them to replace the ones in the page file.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316807312300, "endTime": 11316807902900, "totalTime": 524000}, "additional": {"logType": "info", "children": [], "durationId": "dde02493-02ee-48f8-bdfb-d5b98bea1a5b"}}, {"head": {"id": "20f37a07-4ca3-477b-8968-7fecf96f1dff", "name": "entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316810211800, "endTime": 11316810577000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "2c813a74-0b53-4313-bd11-d3a9f3c77a18", "logId": "0ff333a6-4ea8-4305-8534-0e035f3ecfb5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c813a74-0b53-4313-bd11-d3a9f3c77a18", "name": "create entry:buildPreviewerResource task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316810122200}, "additional": {"logType": "detail", "children": [], "durationId": "20f37a07-4ca3-477b-8968-7fecf96f1dff"}}, {"head": {"id": "ee853fad-832a-4bc0-9978-a5ef32bb5ca5", "name": "Executing task :entry:buildPreviewerResource", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316810223700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89f20fc8-7d5d-40f7-a910-3baf99901aca", "name": "entry : buildPreviewerResource cost memory 0.01219940185546875", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316810432500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7530790f-20db-42c6-95a3-29decd3699f1", "name": "runTaskFromQueue task cost before running: 909 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316810524000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ff333a6-4ea8-4305-8534-0e035f3ecfb5", "name": "Finished :entry:buildPreviewerResource", "description": "Build the preview resources.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316810211800, "endTime": 11316810577000, "totalTime": 291000}, "additional": {"logType": "info", "children": [], "durationId": "20f37a07-4ca3-477b-8968-7fecf96f1dff"}}, {"head": {"id": "3954516b-3a47-488f-b508-45ecd0bb5597", "name": "entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316815116900, "endTime": 11316829250500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "690d8886-927a-4625-be4a-57c83b69c77d", "logId": "b2268c2c-7ecf-49c9-9bc2-cf4ec31ace33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "690d8886-927a-4625-be4a-57c83b69c77d", "name": "create entry:default@PreviewUpdateAssets task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316812416300}, "additional": {"logType": "detail", "children": [], "durationId": "3954516b-3a47-488f-b508-45ecd0bb5597"}}, {"head": {"id": "5b6fae18-ec44-44bc-b002-ee76cdc4df02", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316813046200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45f63c0f-8029-462d-8c80-f52db9062ff6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316813235100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08e6d612-067d-4393-adeb-d88094028b32", "name": "Executing task :entry:default@PreviewUpdateAssets", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316815145800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7caee69e-1820-4895-ada2-20404980af7b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316817690900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22659b99-f4cc-4a79-9fb4-88d76244549b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316817820700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e36879c-aa47-4860-b08c-8a6dc757ca9b", "name": "entry : default@PreviewUpdateAssets cost memory 0.08829498291015625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316828909200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94cf5b51-9215-4b22-8b3d-99af3fd12d66", "name": "runTaskFromQueue task cost before running: 928 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316829184200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2268c2c-7ecf-49c9-9bc2-cf4ec31ace33", "name": "Finished :entry:default@PreviewUpdateAssets", "description": "Update main_pages.json and module.json before PreviewBuild.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316815116900, "endTime": 11316829250500, "totalTime": 14032100}, "additional": {"logType": "info", "children": [], "durationId": "3954516b-3a47-488f-b508-45ecd0bb5597"}}, {"head": {"id": "d9a020e6-7041-454e-a523-caa31cee28b5", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316843628400, "endTime": 11338303143400}, "additional": {"children": ["cb4866f2-38b2-4c65-b672-b25253cbc454"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c74a8b48-af6e-4c24-b9a9-6b8e62c74497", "logId": "fe842bb6-59db-4276-8062-fe77f455e653"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c74a8b48-af6e-4c24-b9a9-6b8e62c74497", "name": "create entry:default@PreviewArkTS task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316834052100}, "additional": {"logType": "detail", "children": [], "durationId": "d9a020e6-7041-454e-a523-caa31cee28b5"}}, {"head": {"id": "c1322f6e-669b-4e55-8acd-2a5d5ec285ca", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316835340100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9488581a-a116-48ed-9cb7-b019c2d529ab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316835539800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44bf16f0-8cb8-43c9-9281-85a9429b9f67", "name": "Executing task :entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316843648700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb4866f2-38b2-4c65-b672-b25253cbc454", "name": "entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Worker1", "startTime": 11316877865600, "endTime": 11338294653600}, "additional": {"children": ["4783266a-0692-49eb-b307-49ff3e62366e", "7ae793ae-ee09-4101-943e-f31838290518", "42f7a188-4b06-4243-9ecf-a03c4e500211", "ca1613b0-fa10-478f-b058-c85c8a6680c1", "76677915-a837-45f1-8a2a-790100113995"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "d9a020e6-7041-454e-a523-caa31cee28b5", "logId": "4ffd295e-aef8-4070-8d87-1129d2d9ee9d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c64a912-3b28-4b30-8dfa-a6d4c238bd3c", "name": "entry : default@PreviewArkTS cost memory 1.3090362548828125", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316882633500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8f41d4e-71cb-4f98-ac71-258c32630901", "name": "watch worker: send response to session manager. Response type: WatchStart", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11323024959600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4783266a-0692-49eb-b307-49ff3e62366e", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Worker1", "startTime": 11323027909300, "endTime": 11323028047300}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cb4866f2-38b2-4c65-b672-b25253cbc454", "logId": "6a2779e9-a4b9-4674-b656-6df6ab3fb2fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a2779e9-a4b9-4674-b656-6df6ab3fb2fe", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11323027909300, "endTime": 11323028047300}, "additional": {"logType": "info", "children": [], "durationId": "4783266a-0692-49eb-b307-49ff3e62366e", "parent": "4ffd295e-aef8-4070-8d87-1129d2d9ee9d"}}, {"head": {"id": "4ba8e370-a91d-468b-a7d6-391d2591cdc5", "name": "watch worker: send response to session manager. Response type: WatchResult", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338288073300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ae793ae-ee09-4101-943e-f31838290518", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Worker1", "startTime": 11338291919800, "endTime": 11338291977100}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cb4866f2-38b2-4c65-b672-b25253cbc454", "logId": "610ae9e1-be67-4ab1-b7f4-8f57c60a2cee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "610ae9e1-be67-4ab1-b7f4-8f57c60a2cee", "name": "\"onWatchWorkerMessage\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338291919800, "endTime": 11338291977100}, "additional": {"logType": "info", "children": [], "durationId": "7ae793ae-ee09-4101-943e-f31838290518", "parent": "4ffd295e-aef8-4070-8d87-1129d2d9ee9d"}}, {"head": {"id": "4ffd295e-aef8-4070-8d87-1129d2d9ee9d", "name": "entry:default@PreviewArkTS", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Worker1", "startTime": 11316877865600, "endTime": 11338294653600}, "additional": {"logType": "info", "children": ["6a2779e9-a4b9-4674-b656-6df6ab3fb2fe", "610ae9e1-be67-4ab1-b7f4-8f57c60a2cee", "29a47699-18e2-4c00-8a5a-35e1c998622a", "b6f99ec8-4b13-4e19-aacc-4da4d438e276", "1ed47730-9819-41a9-989f-e372b1da4daf"], "durationId": "cb4866f2-38b2-4c65-b672-b25253cbc454", "parent": "fe842bb6-59db-4276-8062-fe77f455e653"}}, {"head": {"id": "42f7a188-4b06-4243-9ecf-a03c4e500211", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Worker1", "startTime": 11319786511100, "endTime": 11322951122100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "cb4866f2-38b2-4c65-b672-b25253cbc454", "logId": "29a47699-18e2-4c00-8a5a-35e1c998622a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29a47699-18e2-4c00-8a5a-35e1c998622a", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11319786511100, "endTime": 11322951122100}, "additional": {"logType": "info", "children": [], "durationId": "42f7a188-4b06-4243-9ecf-a03c4e500211", "parent": "4ffd295e-aef8-4070-8d87-1129d2d9ee9d"}}, {"head": {"id": "ca1613b0-fa10-478f-b058-c85c8a6680c1", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Worker1", "startTime": 11322951543800, "endTime": 11322951842100}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "cb4866f2-38b2-4c65-b672-b25253cbc454", "logId": "b6f99ec8-4b13-4e19-aacc-4da4d438e276"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6f99ec8-4b13-4e19-aacc-4da4d438e276", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11322951543800, "endTime": 11322951842100}, "additional": {"logType": "info", "children": [], "durationId": "ca1613b0-fa10-478f-b058-c85c8a6680c1", "parent": "4ffd295e-aef8-4070-8d87-1129d2d9ee9d"}}, {"head": {"id": "76677915-a837-45f1-8a2a-790100113995", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Worker1", "startTime": 11322952079800, "endTime": 11338286575400}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "cb4866f2-38b2-4c65-b672-b25253cbc454", "logId": "1ed47730-9819-41a9-989f-e372b1da4daf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ed47730-9819-41a9-989f-e372b1da4daf", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11322952079800, "endTime": 11338286575400}, "additional": {"logType": "info", "children": [], "durationId": "76677915-a837-45f1-8a2a-790100113995", "parent": "4ffd295e-aef8-4070-8d87-1129d2d9ee9d"}}, {"head": {"id": "fe842bb6-59db-4276-8062-fe77f455e653", "name": "Finished :entry:default@PreviewArkTS", "description": "Compile ArkTS components on preview in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11316843628400, "endTime": 11338303143400, "totalTime": 21459422600}, "additional": {"logType": "info", "children": ["4ffd295e-aef8-4070-8d87-1129d2d9ee9d"], "durationId": "d9a020e6-7041-454e-a523-caa31cee28b5"}}, {"head": {"id": "84ca2894-09db-4f1d-8cfa-fc35a0cfac92", "name": "entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338317942300, "endTime": 11338318960500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "074ce136-cd25-4ad0-b8a3-8679db5f48d9", "logId": "6629a703-2059-4abf-8af5-7c123750a32e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "074ce136-cd25-4ad0-b8a3-8679db5f48d9", "name": "create entry:PreviewBuild task", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338317820000}, "additional": {"logType": "detail", "children": [], "durationId": "84ca2894-09db-4f1d-8cfa-fc35a0cfac92"}}, {"head": {"id": "88ed5835-559d-4baf-9462-598e2a259983", "name": "Executing task :entry:PreviewBuild", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338317963800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4c47ca6-ec1d-48f5-8ef5-f76fef37b28a", "name": "entry : PreviewBuild cost memory 0.02152252197265625", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338318508800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d56772ed-dd66-4e54-bcfd-f22c9f91a5a2", "name": "runTaskFromQueue task cost before running: 22 s 418 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338318734900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6629a703-2059-4abf-8af5-7c123750a32e", "name": "Finished :entry:PreviewBuild", "description": "Build preview in the stage model.", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338317942300, "endTime": 11338318960500, "totalTime": 705100}, "additional": {"logType": "info", "children": [], "durationId": "84ca2894-09db-4f1d-8cfa-fc35a0cfac92"}}, {"head": {"id": "f4a5b21f-1ec1-4d60-99c9-868537d32313", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338336135100, "endTime": 11338336184200}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6180730a-706e-4ad6-a159-9fbf4eede3e4", "logId": "1677820b-e168-465c-b4b1-2643fd001b9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1677820b-e168-465c-b4b1-2643fd001b9b", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338336135100, "endTime": 11338336184200}, "additional": {"logType": "info", "children": [], "durationId": "f4a5b21f-1ec1-4d60-99c9-868537d32313"}}, {"head": {"id": "9fa35f50-7d96-4583-a5ae-1182cdb12743", "name": "BUILD SUCCESSFUL in 22 s 435 ms ", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338336394000}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "3ec7bb9a-cf90-4523-bd3e-50424e6c12a4", "name": "PreviewBuild", "description": "", "type": "mark"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11315901629100, "endTime": 11338337480300}, "additional": {"time": {"year": 2025, "month": 6, "day": 25, "hour": 12, "minute": 14}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon\",\"watch\":true,\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"pageType=page\",\"compileResInc=true\",\"previewMode=true\",\"buildRoot=.preview\"],\"incremental\":true,\"_\":[\"PreviewBuild\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p pageType=page -p compileResInc=true -p previewMode=true -p buildRoot=.preview PreviewBuild --watch --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.15.3", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "9ff0a2ca-c013-441d-b3ea-d05afe2b8f73", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338339993000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd5b5afe-e067-42ed-a272-cb54a4c3b40c", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338342037900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb6366e2-d7e0-4560-b3c3-09ce4e861050", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338343491700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c44211e-cf0c-4c07-9cae-d0bd8912dd0d", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338344469500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ee473fb-4390-480e-b6cc-2fd2387fceca", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources\\base\\profile\\main_pages.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338345384900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d537c21-cf59-4bc0-9499-4bde0428554c", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\hvigor\\hvigor-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338346182500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18b2ff81-745c-4b5d-b608-8cd169bb2f67", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338347476500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5500a08-3e8e-4d65-83fc-f6538953cbb1", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338348398200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df3fd7f2-0f76-4dc8-801f-caa66d6012a4", "name": "Update task entry:default@PreBuild input file:D:\\vue\\daxiangmuwallet\\wallet\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338349226100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c464a499-764f-41ad-b715-3f37f92e1239", "name": "Incremental task entry:default@PreBuild post-execution cost:13 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338350796600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d9514d8-43a6-4f9b-bcf2-f2a70d4b8b6c", "name": "Update task entry:default@MergeProfile input file:D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338351967300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b4f636f-7926-4acf-83d2-7ef38eb9b36a", "name": "Update task entry:default@MergeProfile input file:D:\\vue\\daxiangmuwallet\\wallet\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338352954300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a74a63e-4a5a-46e7-b1a6-ce7385bbde08", "name": "Update task entry:default@MergeProfile input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338354178500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dee8b19f-b007-4537-aaa3-5a20d534ea5b", "name": "Update task entry:default@MergeProfile output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338355201200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5369751c-a67f-46a4-8468-32f9dbfc243b", "name": "Incremental task entry:default@MergeProfile post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338356178600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e391d3d-6cd5-4713-8544-a1df893cd94c", "name": "Update task entry:default@CreateBuildProfile input file:D:\\vue\\daxiangmuwallet\\wallet\\AppScope\\app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338356401700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8377e21-372c-4fa9-acd4-225b40dcb1f2", "name": "Update task entry:default@CreateBuildProfile input file:D:\\vue\\daxiangmuwallet\\wallet\\build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338357361700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff163f0c-6256-4dc9-8807-c4af28a72e99", "name": "Update task entry:default@CreateBuildProfile output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338358209300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c07bec52-199c-4f0d-a462-4d69eb1c2704", "name": "Incremental task entry:default@CreateBuildProfile post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338359118800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88412243-17e7-4129-bbb4-f477f50677ac", "name": "Update task entry:default@GeneratePkgContextInfo output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338359417800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8209dfd4-249d-4256-b0d2-43ae07feb6aa", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338360311900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "275b125a-59de-4368-b7f6-3f692ea08afd", "name": "Update task entry:default@ProcessProfile input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338360519400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "969aad09-da41-4bcc-8e48-0006a648b098", "name": "Update task entry:default@ProcessProfile output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338361289600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fd8db30-2401-41b0-8699-423a9911926f", "name": "Incremental task entry:default@ProcessProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338362066100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a46e395-c244-4b97-8f1e-531cc820d645", "name": "Update task entry:default@ProcessRouterMap input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338365688700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee5854b-f6f8-4036-845b-8e55ada7bcaf", "name": "Update task entry:default@ProcessRouterMap input file:D:\\vue\\daxiangmuwallet\\wallet\\oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338366562600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fff313b2-2554-424c-901e-dca0fc17f317", "name": "Update task entry:default@ProcessRouterMap input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338367653100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e7e92bf-30b4-438b-93e1-04a0f4fd0ef3", "name": "Update task entry:default@ProcessRouterMap input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338368425700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca69c477-e2b9-4839-9d8c-3ab96bf5e3b5", "name": "Update task entry:default@ProcessRouterMap output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338369106800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c759c31-e715-4149-9e85-ff3758e20eb3", "name": "Update task entry:default@ProcessRouterMap output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\loader-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338370488400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d84cc46-996d-4ad8-b716-843af74e6f9d", "name": "Incremental task entry:default@ProcessRouterMap post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338371370200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1a20d7b-cebf-483f-8b5b-84d9efb4b7d2", "name": "Update task entry:default@GenerateLoaderJson input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338379134300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f65f088-e9d2-4ad4-9117-117442787cdd", "name": "Update task entry:default@GenerateLoaderJson input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338379943500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ec1a1d2-a5cf-403f-bf7e-8562e3409043", "name": "Update task entry:default@GenerateLoaderJson input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\router_map\\default\\temp-router-map.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338380900200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f38382f-fbd1-4963-9f2c-1f24decb22a7", "name": "Update task entry:default@GenerateLoaderJson output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\loader.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338381737800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b424b0ed-7361-4c10-901a-5461dd88dbd7", "name": "Incremental task entry:default@GenerateLoaderJson post-execution cost:12 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338382649000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4fc1ae1-0568-45fa-9049-2b3c10db0f73", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338383736500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d161a478-fe36-4719-b683-e139f29578fe", "name": "Update task entry:default@PreviewCompileResource input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\merge_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338395615200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4df05e90-d3b6-4bc0-bd33-3dd66d98ee48", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338396607800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95be1b9b-0a20-4490-9e80-23f18a348706", "name": "Update task entry:default@PreviewCompileResource output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\r\\default cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338413520600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14db83b8-11a2-4b10-813a-14f44e17696f", "name": "Incremental task entry:default@PreviewCompileResource post-execution cost:33 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338415239700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a580516a-622a-4174-8e8c-da4acc17d7b9", "name": "Update task entry:default@CopyPreviewProfile input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338415826600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2722f5f7-3d06-4d4e-af8f-faed53f879cc", "name": "Update task entry:default@CopyPreviewProfile output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338418254900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4539ffc-1b0c-4ee5-b77b-a80b060d8863", "name": "Incremental task entry:default@CopyPreviewProfile post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338420575400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89daf1b3-9568-4b45-91ab-06cd5be632ca", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\module.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338421822900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fba1be7-24fc-43e5-99e3-92a966372413", "name": "Update task entry:default@PreviewUpdateAssets output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile\\main_pages.json cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338422690900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb549647-9717-4203-8d77-f89fa830e05e", "name": "Incremental task entry:default@PreviewUpdateAssets post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338423504200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71f36344-3e00-4221-8120-55feb1f17e1c", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338431376700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4598a84-f4be-4455-a218-d9f4154daeb7", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338433410600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bc83cd0-9ddc-4f0c-b993-ad41d6584e64", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338434394400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec8e3d4b-f37d-47df-9ce8-d4389f11c714", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\res\\default\\resources\\base\\profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338435245300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "947665dc-69be-45c8-85c7-8e278d2b3887", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\main\\ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338437186200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59ad8036-9a52-47c6-97b9-070800ebeafa", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader\\default\\pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338441385900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34b5f4bf-70d4-43c0-ba65-9ee2dd78fcdd", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\generated\\profile\\default\\BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338442129800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2eeee7f-fdc0-49db-95bf-43eedc35a424", "name": "Update task entry:default@PreviewArkTS input file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\src\\mock\\mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338443141100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca8d640e-5ab1-4910-8d4b-39f89c0c5062", "name": "Update task entry:default@PreviewArkTS output file:D:\\vue\\daxiangmuwallet\\wallet\\entry\\.preview\\default\\intermediates\\loader_out\\default\\ets cache.", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338444022800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c589ad2-9e06-448c-8870-a9494b1f100b", "name": "Incremental task entry:default@PreviewArkTS post-execution cost:22 ms .", "description": "", "type": "log"}, "body": {"pid": 27984, "tid": "Main Thread", "startTime": 11338444806400}, "additional": {"logType": "debug", "children": []}}], "workLog": []}