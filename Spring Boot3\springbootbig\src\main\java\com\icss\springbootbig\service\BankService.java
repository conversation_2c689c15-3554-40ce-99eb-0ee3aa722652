package com.icss.springbootbig.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icss.springbootbig.entity.Bank;
import com.icss.springbootbig.exception.ApiException;
import com.icss.springbootbig.mapper.BankMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
public class BankService {
    
    @Autowired
    private BankMapper bankMapper;
    
    /**
     * 创建银行
     */
    @Transactional
    public Bank createBank(Bank bank) {
        // 验证银行代码是否已存在
        Bank existingBank = bankMapper.selectByBankCode(bank.getBankCode());
        if (existingBank != null) {
            throw new ApiException("银行代码已存在");
        }
        
        // 设置默认值
        if (bank.getStatus() == null) {
            bank.setStatus(1); // 默认正常状态
        }
        if (bank.getSortOrder() == null) {
            bank.setSortOrder(999); // 默认排序
        }
        
        bank.setCreatedAt(new Date());
        bank.setUpdatedAt(new Date());
        
        bankMapper.insert(bank);
        return bank;
    }
    
    /**
     * 根据ID获取银行信息
     */
    public Bank getBankById(Integer bankId) {
        Bank bank = bankMapper.selectById(bankId);
        if (bank == null) {
            throw new ApiException("银行不存在");
        }
        return bank;
    }
    
    /**
     * 根据银行代码获取银行信息
     */
    public Bank getBankByCode(String bankCode) {
        Bank bank = bankMapper.selectByBankCode(bankCode);
        if (bank == null) {
            throw new ApiException("银行不存在");
        }
        return bank;
    }
    
    /**
     * 更新银行信息
     */
    @Transactional
    public Bank updateBank(Bank bank) {
        Bank existingBank = getBankById(bank.getBankId());
        
        // 如果修改了银行代码，需要验证新代码是否已存在
        if (!existingBank.getBankCode().equals(bank.getBankCode())) {
            Bank bankWithSameCode = bankMapper.selectByBankCode(bank.getBankCode());
            if (bankWithSameCode != null) {
                throw new ApiException("银行代码已存在");
            }
        }
        
        bank.setUpdatedAt(new Date());
        bankMapper.updateById(bank);
        return bankMapper.selectById(bank.getBankId());
    }
    
    /**
     * 删除银行
     */
    @Transactional
    public void deleteBank(Integer bankId) {
        Bank bank = getBankById(bankId);
        
        // 检查是否有关联的银行卡
        // 这里可以添加检查逻辑，如果有关联数据则不允许删除
        
        bankMapper.deleteById(bankId);
    }
    
    /**
     * 启用/禁用银行
     */
    @Transactional
    public void updateBankStatus(Integer bankId, Integer status) {
        Bank bank = getBankById(bankId);
        bank.setStatus(status);
        bank.setUpdatedAt(new Date());
        bankMapper.updateById(bank);
    }
    
    /**
     * 获取所有银行列表
     */
    public List<Bank> getAllBanks() {
        return bankMapper.selectAllBanks();
    }
    
    /**
     * 获取启用的银行列表
     */
    public List<Bank> getActiveBanks() {
        return bankMapper.selectActiveBanks();
    }
    
    /**
     * 分页查询银行
     */
    public IPage<Bank> getBanksByPage(int pageNum, int pageSize, String keyword, Integer status) {
        Page<Bank> page = new Page<>(pageNum, pageSize);
        QueryWrapper<Bank> queryWrapper = new QueryWrapper<>();
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                .like("bank_name", keyword)
                .or()
                .like("bank_short_name", keyword)
                .or()
                .like("bank_code", keyword)
            );
        }
        
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.orderByAsc("sort_order", "bank_name");
        
        return bankMapper.selectPage(page, queryWrapper);
    }
    
    /**
     * 更新银行排序
     */
    @Transactional
    public void updateBankSortOrder(Integer bankId, Integer sortOrder) {
        Bank bank = getBankById(bankId);
        bank.setSortOrder(sortOrder);
        bank.setUpdatedAt(new Date());
        bankMapper.updateById(bank);
    }
}
