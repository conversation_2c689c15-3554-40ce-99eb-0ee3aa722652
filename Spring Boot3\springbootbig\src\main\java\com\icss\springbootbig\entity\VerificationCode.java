package com.icss.springbootbig.entity;

import lombok.Data;
import java.util.Date;

/**
 * 验证码实体类 - 内存存储版本
 * 由于数据库中没有verification_codes表，使用内存存储
 */
@Data
public class VerificationCode {
    private String phone;
    private String code;
    private Integer type; // 1-注册 2-登录 3-找回密码
    private Integer used; // 0-未使用 1-已使用
    private Date expiresAt;
    private Date createdAt;
    private Date updatedAt;

    public VerificationCode() {
        this.createdAt = new Date();
        this.updatedAt = new Date();
        this.used = 0;
    }

    public VerificationCode(String phone, String code, Integer type, Date expiresAt) {
        this();
        this.phone = phone;
        this.code = code;
        this.type = type;
        this.expiresAt = expiresAt;
    }
}
