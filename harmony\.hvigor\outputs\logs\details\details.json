{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"clean": 138718200, "PreBuild": 38460900, "MergeProfile": 3818900, "CreateBuildProfile": 2270200, "PreCheckSyscap": 297600, "GeneratePkgContextInfo": 1608500, "ProcessProfile": 98480100, "ProcessRouterMap": 4625300, "PreviewProcessResource": 3972400, "GenerateLoaderJson": 21493800, "PreviewCompileResource": 203133600, "PreviewHookCompileResource": 218600, "CopyPreviewProfile": 5642500, "ReplacePreviewerPage": 320700, "buildPreviewerResource": 230000, "PreviewUpdateAssets": 3723300}}, "TOTAL_TIME": 10170789500, "BUILD_ID": "202506251210388490", "ERROR_MESSAGE": {"CODE": "00000", "TIMESTAMP": "1750824649010"}}}