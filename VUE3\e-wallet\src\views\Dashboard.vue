<template>
  <div class="dashboard-container">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="welcome-content">
        <h1>欢迎使用钱包管理系统</h1>
        <p>{{ getCurrentTime() }} | 系统运行正常</p>
      </div>
      <div class="system-status">
        <div class="status-item">
          <span class="status-dot online"></span>
          <span>系统在线</span>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card primary">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="icon-users">👥</i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ totalUsers.toLocaleString() }}</div>
              <div class="stat-label">总用户数</div>
              <div class="stat-change positive">+12.5%</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="stat-card success">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="icon-money">💰</i>
            </div>
            <div class="stat-info">
              <div class="stat-number">¥{{ totalWalletBalance.toLocaleString() }}</div>
              <div class="stat-label">总钱包余额</div>
              <div class="stat-change positive">+8.3%</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="stat-card warning">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="icon-loan">🏦</i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ totalBankCards.toLocaleString() }}</div>
              <div class="stat-label">总银行卡数</div>
              <div class="stat-change negative">-2.1%</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="stat-card info">
          <div class="stat-content">
            <div class="stat-icon">
              <i class="icon-transactions">📊</i>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ todayTransactions.toLocaleString() }}</div>
              <div class="stat-label">今日交易数</div>
              <div class="stat-change positive">+15.7%</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表和交易记录 -->
    <el-row :gutter="20" class="content-row">
      <el-col :span="16">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">交易趋势分析</span>
              <el-button-group size="small">
                <el-button :type="chartPeriod === 'week' ? 'primary' : ''" @click="chartPeriod = 'week'">周</el-button>
                <el-button :type="chartPeriod === 'month' ? 'primary' : ''" @click="chartPeriod = 'month'">月</el-button>
                <el-button :type="chartPeriod === 'year' ? 'primary' : ''" @click="chartPeriod = 'year'">年</el-button>
              </el-button-group>
            </div>
          </template>
          <div class="chart-placeholder">
            <div class="chart-mock">
              <div class="chart-bars">
                <div class="bar" style="height: 60%"></div>
                <div class="bar" style="height: 80%"></div>
                <div class="bar" style="height: 45%"></div>
                <div class="bar" style="height: 90%"></div>
                <div class="bar" style="height: 70%"></div>
                <div class="bar" style="height: 85%"></div>
                <div class="bar" style="height: 95%"></div>
              </div>
              <p class="chart-label">交易量趋势图</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card shadow="hover" class="transactions-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">最近交易</span>
              <div>
                <el-button type="text" size="small" @click="loadRecentTransactions">刷新</el-button>
                <el-button type="text" size="small" @click="viewAllTransactions">查看全部</el-button>
              </div>
            </div>
          </template>
          <div class="transaction-list">
            <template v-if="recentTransactions.length > 0">
              <div
                v-for="transaction in recentTransactions"
                :key="transaction.id"
                class="transaction-item"
              >
                <div class="transaction-avatar" :class="transaction.type">
                  {{ getTransactionIcon(transaction.type) }}
                </div>
                <div class="transaction-details">
                  <div class="transaction-title">{{ transaction.title }}</div>
                  <div class="transaction-time">{{ transaction.time }}</div>
                </div>
                <div class="transaction-amount" :class="transaction.type">
                  {{ transaction.type === 'deposit' ? '+' : '-' }}¥{{ Math.abs(transaction.amount).toLocaleString() }}
                </div>
              </div>
            </template>
            <div v-else class="no-transactions">
              <div class="no-transactions-icon">📝</div>
              <div class="no-transactions-text">暂无交易记录</div>
              <div class="no-transactions-tip">完成首次交易后，记录将在这里显示</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router'
import { transactionApi } from '@/api/transaction'
import { getCurrentUserId } from '@/stores/user'
import { ElMessage } from 'element-plus';

const router = useRouter();

// 统计数据
const totalUsers = ref(1256);
const totalWalletBalance = ref(5689321);
const totalBankCards = ref(2345);
const todayTransactions = ref(328);

// 图表周期
const chartPeriod = ref('month');

// 最近交易数据
const recentTransactions = ref([]);

// 获取当前时间
const getCurrentTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 获取最新交易记录
const loadRecentTransactions = async () => {
  try {
    const userId = getCurrentUserId();

    if (!userId) {
      console.warn('用户未登录，无法获取交易记录');
      return;
    }

    const response = await transactionApi.getRecentTransactions(userId, 4);

    if (response && response.code === 0) {
      if (response.data && response.data.length > 0) {
        // 转换数据格式以匹配界面显示
        recentTransactions.value = response.data.map(transaction => ({
          id: transaction.txnId,
          title: getTransactionTitle(transaction),
          type: getTransactionType(transaction),
          amount: transaction.amount,
          time: formatTransactionTime(transaction.createdAt)
        }));
      } else {
        recentTransactions.value = [];
      }
    } else {
      console.error('获取交易记录失败:', response?.message || '未知错误');
      recentTransactions.value = [];
      ElMessage.error(`获取交易记录失败: ${response?.message || '未知错误'}`);
    }
  } catch (error) {
    console.error('获取最新交易记录失败:', error);
    ElMessage.error('获取交易记录失败');
  }
};

// 获取交易标题
const getTransactionTitle = (transaction) => {
  const typeMap = {
    1: '钱包充值',
    2: '转账',
    3: '收款',
    4: '消费',
    5: '提现'
  };
  return typeMap[transaction.type] || '未知交易';
};

// 获取交易类型（用于显示样式）
const getTransactionType = (transaction) => {
  // 收入类型：充值(1)、收款(3)
  if ([1, 3].includes(transaction.type)) {
    return 'deposit';
  }
  // 支出类型：转账(2)、消费(4)、提现(5)
  return 'withdraw';
};

// 格式化交易时间
const formatTransactionTime = (createdAt) => {
  if (!createdAt) return '';

  const now = new Date();
  const transactionDate = new Date(createdAt);
  const diffTime = now - transactionDate;
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    // 今天，显示时间
    return transactionDate.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  } else if (diffDays === 1) {
    return '昨天';
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return transactionDate.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    });
  }
};

// 获取交易图标
const getTransactionIcon = (type) => {
  return type === 'deposit' ? '💰' : '💸';
};

// 查看所有交易
const viewAllTransactions = () => {
  router.push('/transaction');
};



// 组件挂载时加载数据
onMounted(() => {
  loadRecentTransactions();
});
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background: #f8f9fa;
  min-height: calc(100vh - 120px);
}

/* 欢迎横幅 */
.welcome-banner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 16px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.welcome-content h1 {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.welcome-content p {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.system-status {
  display: flex;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.online {
  background: #00ff88;
  box-shadow: 0 0 10px #00ff88;
}

/* 统计卡片 */
.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  border-radius: 16px;
  border: none;
  transition: all 0.3s ease;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-card.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.stat-card.success {
  background: linear-gradient(135deg, #56ab2f, #a8e6cf);
  color: white;
}

.stat-card.warning {
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
  color: white;
}

.stat-card.info {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 25px;
}

.stat-icon {
  font-size: 48px;
  margin-right: 20px;
  opacity: 0.9;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.stat-change {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
}

.stat-change.positive {
  color: #00ff88;
}

.stat-change.negative {
  color: #ff6b6b;
}

/* 内容区域 */
.content-row {
  margin-bottom: 30px;
}

.chart-card,
.transactions-card {
  border-radius: 16px;
  border: none;
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

/* 图表区域 */
.chart-placeholder {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-mock {
  text-align: center;
}

.chart-bars {
  display: flex;
  align-items: end;
  justify-content: center;
  gap: 10px;
  height: 200px;
  margin-bottom: 20px;
}

.bar {
  width: 30px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
}

.bar:hover {
  background: linear-gradient(135deg, #764ba2, #667eea);
  transform: scaleY(1.1);
}

.chart-label {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

/* 交易列表 */
.transaction-list {
  padding: 0;
  max-height: 300px;
  overflow-y: auto;
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f1f3f4;
  transition: all 0.2s ease;
}

.transaction-item:hover {
  background: #f8f9fa;
  margin: 0 -20px;
  padding: 15px 20px;
  border-radius: 8px;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 12px;
}

.transaction-avatar.deposit {
  background: linear-gradient(135deg, #56ab2f, #a8e6cf);
  color: white;
}

.transaction-avatar.withdraw {
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
  color: white;
}

.transaction-details {
  flex: 1;
}

.transaction-title {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.transaction-time {
  font-size: 12px;
  color: #95a5a6;
}

.transaction-amount {
  font-size: 16px;
  font-weight: 600;
}

.transaction-amount.deposit {
  color: #27ae60;
}

.transaction-amount.withdraw {
  color: #e74c3c;
}

/* 无交易记录样式 */
.no-transactions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.no-transactions-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.no-transactions-text {
  font-size: 16px;
  font-weight: 500;
  color: #666;
  margin-bottom: 8px;
}

.no-transactions-tip {
  font-size: 14px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-banner {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .stats-row .el-col {
    margin-bottom: 15px;
  }

  .content-row .el-col {
    margin-bottom: 20px;
  }

  .chart-card,
  .transactions-card {
    height: auto;
  }

  .stat-content {
    padding: 20px;
  }

  .stat-icon {
    font-size: 36px;
  }

  .stat-number {
    font-size: 24px;
  }
}
</style>