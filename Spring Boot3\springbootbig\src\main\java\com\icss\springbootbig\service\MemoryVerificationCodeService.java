package com.icss.springbootbig.service;

import com.icss.springbootbig.entity.VerificationCode;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 内存验证码存储服务
 * 由于数据库中没有verification_codes表，使用内存存储验证码
 */
@Service
public class MemoryVerificationCodeService {
    
    // 使用ConcurrentHashMap存储验证码，key为phone+type，value为验证码对象
    private final Map<String, VerificationCode> verificationCodes = new ConcurrentHashMap<>();
    
    /**
     * 保存验证码
     */
    public void saveVerificationCode(VerificationCode verificationCode) {
        String key = generateKey(verificationCode.getPhone(), verificationCode.getType());
        verificationCodes.put(key, verificationCode);
        
        // 清理过期的验证码
        cleanExpiredCodes();
    }
    
    /**
     * 根据手机号和类型查找验证码
     */
    public VerificationCode findByPhoneAndType(String phone, Integer type) {
        String key = generateKey(phone, type);
        VerificationCode code = verificationCodes.get(key);
        
        // 检查是否过期
        if (code != null && code.getExpiresAt().before(new Date())) {
            verificationCodes.remove(key);
            return null;
        }
        
        return code;
    }
    
    /**
     * 标记验证码为已使用
     */
    public void markAsUsed(String phone, Integer type) {
        String key = generateKey(phone, type);
        VerificationCode code = verificationCodes.get(key);
        if (code != null) {
            code.setUsed(1);
            code.setUpdatedAt(new Date());
        }
    }
    
    /**
     * 删除验证码
     */
    public void deleteVerificationCode(String phone, Integer type) {
        String key = generateKey(phone, type);
        verificationCodes.remove(key);
    }
    
    /**
     * 生成存储key
     */
    private String generateKey(String phone, Integer type) {
        return phone + "_" + type;
    }
    
    /**
     * 清理过期的验证码
     */
    private void cleanExpiredCodes() {
        Date now = new Date();
        verificationCodes.entrySet().removeIf(entry -> 
            entry.getValue().getExpiresAt().before(now)
        );
    }
    
    /**
     * 获取所有验证码数量（用于调试）
     */
    public int getCodeCount() {
        cleanExpiredCodes();
        return verificationCodes.size();
    }
}
