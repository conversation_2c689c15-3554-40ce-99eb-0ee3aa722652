package com.icss.springbootbig.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icss.springbootbig.entity.Account;
import com.icss.springbootbig.entity.BankCard;
import com.icss.springbootbig.entity.Transaction;
import com.icss.springbootbig.entity.User;
import com.icss.springbootbig.exception.ApiException;
import com.icss.springbootbig.mapper.AccountMapper;
import com.icss.springbootbig.mapper.BankCardMapper;
import com.icss.springbootbig.mapper.TransactionMapper;
import com.icss.springbootbig.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.UUID;

@Service
public class WalletService {
    
    @Autowired
    private AccountMapper accountMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private BankCardMapper bankCardMapper;
    
    @Autowired
    private TransactionMapper transactionMapper;

    /**
     * 获取钱包余额信息
     */
    public Map<String, Object> getWalletBalance(Integer userId) {
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        
        if (account == null) {
            throw new ApiException("钱包账户不存在");
        }
        
        Map<String, Object> balanceInfo = new HashMap<>();
        balanceInfo.put("balance", account.getBalance());
        balanceInfo.put("dailyLimit", account.getDailyLimit());
        balanceInfo.put("singleLimit", account.getSingleLimit());
        balanceInfo.put("monthlyLimit", account.getMonthlyLimit());
        balanceInfo.put("status", account.getStatus());
        
        return balanceInfo;
    }

    /**
     * 为现有用户创建钱包账户
     */
    @Transactional
    public void createWalletAccount(Integer userId) {
        // 1. 验证用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 2. 检查是否已经有钱包账户
        Account existingAccount = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        if (existingAccount != null) {
            throw new ApiException("该用户已有钱包账户");
        }

        // 3. 创建新的钱包账户
        Account account = new Account();
        account.setUserId(userId);
        account.setBalance(BigDecimal.ZERO);
        account.setDailyLimit(new BigDecimal("50000.00"));
        account.setSingleLimit(new BigDecimal("5000.00"));
        account.setMonthlyLimit(new BigDecimal("200000.00"));
        account.setStatus(1); // 正常状态

        accountMapper.insert(account);
    }

    /**
     * 批量为所有用户初始化钱包和银行卡
     */
    @Transactional
    public Map<String, Object> initAllUsers() {
        Map<String, Object> result = new HashMap<>();
        int walletCreated = 0;
        int cardCreated = 0;
        List<String> errors = new ArrayList<>();

        // 获取所有用户
        List<User> allUsers = userMapper.selectList(null);

        for (User user : allUsers) {
            try {
                // 检查并创建钱包账户
                Account existingAccount = accountMapper.selectOne(
                    new QueryWrapper<Account>().eq("user_id", user.getUserId())
                );

                if (existingAccount == null) {
                    Account account = new Account();
                    account.setUserId(user.getUserId());
                    account.setBalance(BigDecimal.ZERO);
                    account.setDailyLimit(new BigDecimal("50000.00"));
                    account.setSingleLimit(new BigDecimal("5000.00"));
                    account.setMonthlyLimit(new BigDecimal("200000.00"));
                    account.setStatus(1);

                    accountMapper.insert(account);
                    walletCreated++;
                }

                // 检查并创建默认银行卡
                List<BankCard> existingCards = bankCardMapper.selectList(
                    new QueryWrapper<BankCard>().eq("user_id", user.getUserId())
                );

                if (existingCards.isEmpty()) {
                    BankCard defaultCard = new BankCard();
                    defaultCard.setUserId(user.getUserId());
                    defaultCard.setBankName("中国银行");
                    defaultCard.setCardNumber(generateDefaultCardNumber());
                    defaultCard.setCardType(1); // 借记卡
                    defaultCard.setCardHolder(user.getUsername() != null ? user.getUsername() : "用户" + user.getUserId());
                    defaultCard.setPhone(user.getPhone());
                    defaultCard.setIsDefault(1); // 默认卡
                    defaultCard.setStatus(1); // 正常状态
                    defaultCard.setCreatedAt(new Date());
                    defaultCard.setUpdatedAt(new Date());

                    bankCardMapper.insert(defaultCard);
                    cardCreated++;
                }

            } catch (Exception e) {
                errors.add("用户ID " + user.getUserId() + ": " + e.getMessage());
            }
        }

        result.put("totalUsers", allUsers.size());
        result.put("walletsCreated", walletCreated);
        result.put("cardsCreated", cardCreated);
        result.put("errors", errors);

        return result;
    }

    /**
     * 生成默认银行卡号
     */
    private String generateDefaultCardNumber() {
        StringBuilder cardNumber = new StringBuilder("6217");
        Random random = new Random();
        for (int i = 0; i < 15; i++) {
            cardNumber.append(random.nextInt(10));
        }
        return cardNumber.toString();
    }

    /**
     * 钱包充值（从银行卡充值到钱包）
     */
    @Transactional
    public void recharge(Integer userId, BigDecimal amount, Integer cardId, String payPassword) {
        // 1. 验证用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 验证银行卡支付密码
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null || !card.getUserId().equals(userId)) {
            throw new ApiException("银行卡不存在或不属于该用户");
        }
        if (card.getStatus() != 1) {
            throw new ApiException("银行卡已被冻结");
        }
        if (!payPassword.equals(card.getPayPassword())) {
            throw new ApiException("支付密码错误");
        }
        
        // 2. 验证账户
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        if (account == null) {
            throw new ApiException("钱包账户不存在");
        }
        if (account.getStatus() != 1) {
            throw new ApiException("钱包账户已冻结");
        }
        
        // 4. 验证充值金额
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApiException("充值金额必须大于0");
        }
        if (amount.compareTo(account.getSingleLimit()) > 0) {
            throw new ApiException("超过单笔充值限额");
        }

        // 验证银行卡余额是否足够
        if (amount.compareTo(card.getBalance()) > 0) {
            throw new ApiException("银行卡余额不足");
        }

        // 5. 更新钱包余额
        BigDecimal newBalance = account.getBalance().add(amount);
        account.setBalance(newBalance);
        account.setUpdatedAt(new Date());
        accountMapper.updateById(account);

        // 6. 更新银行卡余额（从银行卡扣除）
        BigDecimal newCardBalance = card.getBalance().subtract(amount);
        card.setBalance(newCardBalance);
        card.setUpdatedAt(new Date());
        bankCardMapper.updateById(card);

        // 7. 创建交易记录
        Transaction transaction = new Transaction();
        transaction.setTxnNo(generateTxnNo());
        transaction.setUserId(userId);
        transaction.setAccountId(account.getAccountId());
        transaction.setType(1); // 1-充值
        transaction.setAmount(amount);
        transaction.setBalance(newBalance);
        transaction.setCardId(cardId);
        transaction.setPaymentMethod(2); // 2-银行卡
        transaction.setStatus(1); // 1-成功
        transaction.setRemark("银行卡充值");
        transaction.setCreatedAt(new Date());
        transaction.setUpdatedAt(new Date());
        
        transactionMapper.insert(transaction);
    }

    /**
     * 钱包提现（从钱包提现到银行卡）
     */
    @Transactional
    public void withdraw(Integer userId, BigDecimal amount, Integer cardId, String payPassword) {
        // 1. 验证用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 2. 验证银行卡和支付密码
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null || !card.getUserId().equals(userId)) {
            throw new ApiException("银行卡不存在或不属于该用户");
        }
        if (card.getStatus() != 1) {
            throw new ApiException("银行卡未连接或已冻结");
        }
        if (!payPassword.equals(card.getPayPassword())) {
            throw new ApiException("支付密码错误");
        }
        
        // 3. 验证账户
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        if (account == null) {
            throw new ApiException("钱包账户不存在");
        }
        if (account.getStatus() != 1) {
            throw new ApiException("钱包账户已冻结");
        }
        
        // 4. 验证提现金额
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApiException("提现金额必须大于0");
        }
        if (amount.compareTo(account.getBalance()) > 0) {
            throw new ApiException("提现失败：余额不足，当前余额：¥" + account.getBalance() + "，提现金额：¥" + amount);
        }
        if (amount.compareTo(account.getSingleLimit()) > 0) {
            throw new ApiException("超过单笔提现限额：¥" + account.getSingleLimit());
        }
        
        // 5. 更新钱包余额
        BigDecimal newBalance = account.getBalance().subtract(amount);
        account.setBalance(newBalance);
        account.setUpdatedAt(new Date());
        accountMapper.updateById(account);

        // 6. 更新银行卡余额（提现到银行卡）
        BigDecimal newCardBalance = card.getBalance().add(amount);
        card.setBalance(newCardBalance);
        card.setUpdatedAt(new Date());
        bankCardMapper.updateById(card);

        // 7. 创建交易记录
        Transaction transaction = new Transaction();
        transaction.setTxnNo(generateTxnNo());
        transaction.setUserId(userId);
        transaction.setAccountId(account.getAccountId());
        transaction.setType(5); // 5-提现
        transaction.setAmount(amount);
        transaction.setBalance(newBalance);
        transaction.setCardId(cardId);
        transaction.setPaymentMethod(2); // 2-银行卡
        transaction.setStatus(1); // 1-成功
        transaction.setRemark("提现到银行卡");
        transaction.setCreatedAt(new Date());
        transaction.setUpdatedAt(new Date());
        
        transactionMapper.insert(transaction);
    }

    /**
     * 设置钱包限额
     */
    public void setWalletLimits(Integer userId, Map<String, Object> limits) {
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        
        if (account == null) {
            throw new ApiException("钱包账户不存在");
        }
        
        if (limits.containsKey("dailyLimit")) {
            BigDecimal dailyLimit = new BigDecimal(limits.get("dailyLimit").toString());
            if (dailyLimit.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ApiException("日限额必须大于0");
            }
            account.setDailyLimit(dailyLimit);
        }
        
        if (limits.containsKey("singleLimit")) {
            BigDecimal singleLimit = new BigDecimal(limits.get("singleLimit").toString());
            if (singleLimit.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ApiException("单笔限额必须大于0");
            }
            account.setSingleLimit(singleLimit);
        }
        
        if (limits.containsKey("monthlyLimit")) {
            BigDecimal monthlyLimit = new BigDecimal(limits.get("monthlyLimit").toString());
            if (monthlyLimit.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ApiException("月限额必须大于0");
            }
            account.setMonthlyLimit(monthlyLimit);
        }
        
        account.setUpdatedAt(new Date());
        accountMapper.updateById(account);
    }

    /**
     * 银行卡转账（银行卡对银行卡转账）
     */
    @Transactional
    public void bankTransfer(Integer fromUserId, Integer fromCardId, String toPhone, Integer toCardId, BigDecimal amount, String payPassword, String remark) {
        // 1. 验证发送方用户
        User fromUser = userMapper.selectById(fromUserId);
        if (fromUser == null) {
            throw new ApiException("用户不存在");
        }

        // 2. 验证发送方银行卡和支付密码
        BankCard fromCard = bankCardMapper.selectById(fromCardId);
        if (fromCard == null || !fromCard.getUserId().equals(fromUserId)) {
            throw new ApiException("银行卡不存在或不属于该用户");
        }
        if (fromCard.getStatus() != 1) {
            throw new ApiException("银行卡已被冻结");
        }
        if (!payPassword.equals(fromCard.getPayPassword())) {
            throw new ApiException("支付密码错误");
        }

        // 3. 查找接收方用户
        User toUser = userMapper.selectOne(
            new QueryWrapper<User>().eq("phone", toPhone)
        );
        if (toUser == null) {
            throw new ApiException("接收方用户不存在");
        }
        if (toUser.getUserId().equals(fromUserId)) {
            throw new ApiException("不能向自己转账");
        }

        // 4. 验证接收方银行卡
        BankCard toCard = bankCardMapper.selectById(toCardId);
        if (toCard == null || !toCard.getUserId().equals(toUser.getUserId())) {
            throw new ApiException("接收方银行卡不存在或不属于该用户");
        }
        if (toCard.getStatus() != 1) {
            throw new ApiException("接收方银行卡已被冻结");
        }

        // 5. 验证转账金额
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApiException("转账金额必须大于0");
        }

        // 验证发送方银行卡余额是否足够
        if (amount.compareTo(fromCard.getBalance()) > 0) {
            throw new ApiException("银行卡余额不足");
        }

        // 6. 获取发送方账户（用于限额验证）
        Account fromAccount = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", fromUserId)
        );
        if (fromAccount != null && amount.compareTo(fromAccount.getSingleLimit()) > 0) {
            throw new ApiException("超过单笔转账限额");
        }

        // 7. 更新银行卡余额
        // 发送方银行卡扣除金额
        BigDecimal fromNewCardBalance = fromCard.getBalance().subtract(amount);
        fromCard.setBalance(fromNewCardBalance);
        fromCard.setUpdatedAt(new Date());
        bankCardMapper.updateById(fromCard);

        // 接收方银行卡增加金额
        BigDecimal toNewCardBalance = toCard.getBalance().add(amount);
        toCard.setBalance(toNewCardBalance);
        toCard.setUpdatedAt(new Date());
        bankCardMapper.updateById(toCard);

        // 8. 创建发送方交易记录
        Transaction fromTransaction = new Transaction();
        fromTransaction.setTxnNo(generateTxnNo());
        fromTransaction.setUserId(fromUserId);
        fromTransaction.setAccountId(fromAccount != null ? fromAccount.getAccountId() : null);
        fromTransaction.setType(2); // 2-转账
        fromTransaction.setAmount(amount);
        fromTransaction.setBalance(fromAccount != null ? fromAccount.getBalance() : BigDecimal.ZERO);
        fromTransaction.setCardId(fromCardId);
        fromTransaction.setCounterparty(toUser.getUsername());
        fromTransaction.setCounterpartyPhone(toPhone);
        fromTransaction.setPaymentMethod(2); // 2-银行卡
        fromTransaction.setStatus(1); // 1-成功
        fromTransaction.setRemark(remark != null ? remark : "银行卡转账");
        fromTransaction.setCreatedAt(new Date());
        fromTransaction.setUpdatedAt(new Date());
        transactionMapper.insert(fromTransaction);

        // 9. 创建接收方交易记录
        Account toAccount = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", toUser.getUserId())
        );

        Transaction toTransaction = new Transaction();
        toTransaction.setTxnNo(generateTxnNo());
        toTransaction.setUserId(toUser.getUserId());
        toTransaction.setAccountId(toAccount != null ? toAccount.getAccountId() : null);
        toTransaction.setType(3); // 3-收款
        toTransaction.setAmount(amount);
        toTransaction.setBalance(toAccount != null ? toAccount.getBalance() : BigDecimal.ZERO);
        toTransaction.setCardId(toCardId);
        toTransaction.setCounterparty(fromUser.getUsername());
        toTransaction.setCounterpartyPhone(fromUser.getPhone());
        toTransaction.setPaymentMethod(2); // 2-银行卡
        toTransaction.setStatus(1); // 1-成功
        toTransaction.setRemark(remark != null ? remark : "收到银行卡转账");
        toTransaction.setCreatedAt(new Date());
        toTransaction.setUpdatedAt(new Date());
        transactionMapper.insert(toTransaction);
    }

    /**
     * 钱包转账（通过银行账号）
     */
    @Transactional
    public void walletTransferByAccount(Integer fromUserId, String toAccount, BigDecimal amount, String payPassword, String remark) {
        // 1. 验证发送方用户
        User fromUser = userMapper.selectById(fromUserId);
        if (fromUser == null) {
            throw new ApiException("用户不存在");
        }

        // 2. 验证支付密码（从默认银行卡获取）
        BankCard defaultCard = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("user_id", fromUserId)
                        .eq("is_default", 1)
                        .eq("status", 1));

        if (defaultCard == null) {
            throw new ApiException("请先设置默认银行卡");
        }

        if (!payPassword.equals(defaultCard.getPayPassword())) {
            throw new ApiException("支付密码错误");
        }

        // 3. 根据银行账号查找收款人
        BankCard toCard = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("card_number", toAccount)
                        .eq("status", 1));

        if (toCard == null) {
            throw new ApiException("收款银行账号不存在或已停用");
        }

        User toUser = userMapper.selectById(toCard.getUserId());
        if (toUser == null) {
            throw new ApiException("收款用户不存在");
        }

        if (fromUserId.equals(toUser.getUserId())) {
            throw new ApiException("不能向自己转账");
        }

        // 4. 验证转账金额
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApiException("转账金额必须大于0");
        }

        // 5. 获取发送方钱包账户
        Account fromAccount = accountMapper.selectOne(
                new QueryWrapper<Account>().eq("user_id", fromUserId));

        if (fromAccount == null) {
            throw new ApiException("发送方钱包账户不存在");
        }

        // 6. 验证余额
        if (fromAccount.getBalance().compareTo(amount) < 0) {
            throw new ApiException("钱包余额不足");
        }

        // 7. 验证转账限额
        if (amount.compareTo(fromAccount.getSingleLimit()) > 0) {
            throw new ApiException("超出单笔转账限额");
        }

        // 8. 获取或创建接收方钱包账户
        Account toAccount_obj = accountMapper.selectOne(
                new QueryWrapper<Account>().eq("user_id", toUser.getUserId()));

        if (toAccount_obj == null) {
            // 自动创建钱包账户
            toAccount_obj = new Account();
            toAccount_obj.setUserId(toUser.getUserId());
            toAccount_obj.setBalance(BigDecimal.ZERO);
            toAccount_obj.setSingleLimit(new BigDecimal("5000.00"));
            toAccount_obj.setDailyLimit(new BigDecimal("20000.00"));
            toAccount_obj.setMonthlyLimit(new BigDecimal("50000.00"));
            toAccount_obj.setStatus(1);
            toAccount_obj.setCreatedAt(new Date());
            toAccount_obj.setUpdatedAt(new Date());
            accountMapper.insert(toAccount_obj);
        }

        // 9. 执行转账
        // 扣减发送方钱包余额
        fromAccount.setBalance(fromAccount.getBalance().subtract(amount));
        fromAccount.setUpdatedAt(new Date());
        accountMapper.updateById(fromAccount);

        // 增加接收方钱包余额
        toAccount_obj.setBalance(toAccount_obj.getBalance().add(amount));
        toAccount_obj.setUpdatedAt(new Date());
        accountMapper.updateById(toAccount_obj);

        // 增加接收方银行卡余额（钱包转账到银行账号时，银行卡余额也应该增加）
        BigDecimal toNewCardBalance = toCard.getBalance().add(amount);
        toCard.setBalance(toNewCardBalance);
        toCard.setUpdatedAt(new Date());
        bankCardMapper.updateById(toCard);

        // 10. 记录交易
        Transaction transaction = new Transaction();
        transaction.setTxnNo(generateTxnNo()); // 生成交易流水号
        transaction.setUserId(fromUserId);
        transaction.setAccountId(fromAccount.getAccountId());
        transaction.setType(2); // 2-转账
        transaction.setAmount(amount);
        transaction.setBalance(fromAccount.getBalance());
        transaction.setCounterparty(toUser.getUsername());
        transaction.setCounterpartyPhone(toUser.getPhone());
        transaction.setPaymentMethod(1); // 1-钱包
        transaction.setStatus(1); // 1-成功
        transaction.setRemark(remark != null && !remark.isEmpty() ? remark : "Wallet transfer to account: " + toAccount);
        transaction.setCreatedAt(new Date());
        transaction.setUpdatedAt(new Date());
        transactionMapper.insert(transaction);

        // 为收款方也记录一笔交易
        try {
            Transaction receiverTransaction = new Transaction();
            receiverTransaction.setTxnNo(generateTxnNo()); // 生成交易流水号
            receiverTransaction.setUserId(toUser.getUserId());
            receiverTransaction.setAccountId(toAccount_obj.getAccountId());
            receiverTransaction.setType(3); // 3-收款
            receiverTransaction.setAmount(amount);
            receiverTransaction.setBalance(toAccount_obj.getBalance());
            receiverTransaction.setCounterparty(fromUser.getUsername());
            receiverTransaction.setCounterpartyPhone(fromUser.getPhone());
            receiverTransaction.setPaymentMethod(1); // 1-钱包
            receiverTransaction.setStatus(1); // 1-成功
            receiverTransaction.setRemark(remark != null && !remark.isEmpty() ? remark : "Wallet transfer received");
            receiverTransaction.setCreatedAt(new Date());
            receiverTransaction.setUpdatedAt(new Date());
            transactionMapper.insert(receiverTransaction);
        } catch (Exception e) {
            // 如果收款方交易记录插入失败，记录日志但不影响主要转账流程
            System.err.println("Failed to insert receiver transaction record: " + e.getMessage());
        }
    }

    /**
     * 钱包转账（钱包间转账）
     */
    @Transactional
    public void walletTransfer(Integer fromUserId, String toPhone, BigDecimal amount, String payPassword, String remark) {
        // 1. 验证发送方用户
        User fromUser = userMapper.selectById(fromUserId);
        if (fromUser == null) {
            throw new ApiException("用户不存在");
        }

        // 2. 验证支付密码（从默认银行卡获取）
        BankCard defaultCard = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("user_id", fromUserId)
                        .eq("is_default", 1)
                        .eq("status", 1));

        if (defaultCard == null) {
            throw new ApiException("请先设置默认银行卡");
        }

        if (!payPassword.equals(defaultCard.getPayPassword())) {
            throw new ApiException("支付密码错误");
        }

        // 2. 查找接收方用户 - 改进查找逻辑，支持自动创建用户
        User toUser = userMapper.selectOne(
            new QueryWrapper<User>().eq("phone", toPhone)
        );
        if (toUser == null) {
            // 如果接收方用户不存在，提供更友好的错误信息
            throw new ApiException("钱包转账失败：接收方用户不存在，请确认手机号码是否正确");
        }
        if (toUser.getUserId().equals(fromUserId)) {
            throw new ApiException("不能向自己转账");
        }

        // 3. 验证发送方账户
        Account fromAccount = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", fromUserId)
        );
        if (fromAccount == null) {
            throw new ApiException("发送方钱包账户不存在");
        }
        if (fromAccount.getStatus() != 1) {
            throw new ApiException("发送方钱包账户已冻结");
        }

        // 4. 验证接收方账户，如果不存在则自动创建
        Account toAccount = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", toUser.getUserId())
        );
        if (toAccount == null) {
            // 自动为接收方创建钱包账户
            toAccount = new Account();
            toAccount.setUserId(toUser.getUserId());
            toAccount.setBalance(BigDecimal.ZERO);
            toAccount.setDailyLimit(new BigDecimal("50000"));
            toAccount.setSingleLimit(new BigDecimal("10000"));
            toAccount.setMonthlyLimit(new BigDecimal("200000"));
            toAccount.setStatus(1);
            toAccount.setCreatedAt(new Date());
            toAccount.setUpdatedAt(new Date());
            accountMapper.insert(toAccount);
        }
        if (toAccount.getStatus() != 1) {
            throw new ApiException("接收方钱包账户已冻结");
        }

        // 5. 验证转账金额
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApiException("转账金额必须大于0");
        }
        if (amount.compareTo(fromAccount.getBalance()) > 0) {
            throw new ApiException("钱包转账失败：余额不足，当前余额：¥" + fromAccount.getBalance() + "，转账金额：¥" + amount);
        }
        if (amount.compareTo(fromAccount.getSingleLimit()) > 0) {
            throw new ApiException("超过单笔转账限额");
        }
        
        // 6. 更新发送方余额
        BigDecimal fromNewBalance = fromAccount.getBalance().subtract(amount);
        fromAccount.setBalance(fromNewBalance);
        fromAccount.setUpdatedAt(new Date());
        accountMapper.updateById(fromAccount);
        
        // 7. 更新接收方余额
        BigDecimal toNewBalance = toAccount.getBalance().add(amount);
        toAccount.setBalance(toNewBalance);
        toAccount.setUpdatedAt(new Date());
        accountMapper.updateById(toAccount);
        
        // 8. 创建发送方交易记录
        Transaction fromTransaction = new Transaction();
        fromTransaction.setTxnNo(generateTxnNo());
        fromTransaction.setUserId(fromUserId);
        fromTransaction.setAccountId(fromAccount.getAccountId());
        fromTransaction.setType(2); // 2-转账
        fromTransaction.setAmount(amount);
        fromTransaction.setBalance(fromNewBalance);
        fromTransaction.setCounterparty(toUser.getUsername());
        fromTransaction.setCounterpartyPhone(toPhone);
        fromTransaction.setPaymentMethod(1); // 1-钱包
        fromTransaction.setStatus(1); // 1-成功
        fromTransaction.setRemark(remark != null ? remark : "钱包转账");
        fromTransaction.setCreatedAt(new Date());
        fromTransaction.setUpdatedAt(new Date());
        transactionMapper.insert(fromTransaction);
        
        // 9. 创建接收方交易记录
        Transaction toTransaction = new Transaction();
        toTransaction.setTxnNo(generateTxnNo());
        toTransaction.setUserId(toUser.getUserId());
        toTransaction.setAccountId(toAccount.getAccountId());
        toTransaction.setType(3); // 3-收款
        toTransaction.setAmount(amount);
        toTransaction.setBalance(toNewBalance);
        toTransaction.setCounterparty(fromUser.getUsername());
        toTransaction.setCounterpartyPhone(fromUser.getPhone());
        toTransaction.setPaymentMethod(1); // 1-钱包
        toTransaction.setStatus(1); // 1-成功
        toTransaction.setRemark(remark != null ? remark : "收到转账");
        toTransaction.setCreatedAt(new Date());
        toTransaction.setUpdatedAt(new Date());
        transactionMapper.insert(toTransaction);
    }

    /**
     * 生成收款码
     */
    public Map<String, Object> generateReceiveQR(Integer userId, BigDecimal amount, String remark) {
        // 验证用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 验证账户
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        if (account == null) {
            throw new ApiException("钱包账户不存在");
        }
        if (account.getStatus() != 1) {
            throw new ApiException("钱包账户已冻结");
        }

        // 生成收款码（简单格式：RC_用户ID_时间戳_金额）
        String qrCode = "RC_" + userId + "_" + System.currentTimeMillis();
        if (amount != null) {
            qrCode += "_" + amount.toString();
        }

        Map<String, Object> qrInfo = new HashMap<>();
        qrInfo.put("qrCode", qrCode);
        qrInfo.put("userId", userId);
        qrInfo.put("username", user.getUsername());
        qrInfo.put("phone", user.getPhone());
        qrInfo.put("amount", amount);
        qrInfo.put("remark", remark);
        qrInfo.put("createTime", new Date());
        qrInfo.put("expireTime", new Date(System.currentTimeMillis() + 30 * 60 * 1000)); // 30分钟过期

        return qrInfo;
    }

    /**
     * 扫码支付
     */
    @Transactional
    public void scanPay(String qrCode, Integer payerUserId, String payPassword) {
        // 解析收款码
        String[] parts = qrCode.split("_");
        if (parts.length < 3 || !parts[0].equals("RC")) {
            throw new ApiException("无效的收款码");
        }

        Integer receiverUserId = Integer.parseInt(parts[1]);
        long timestamp = Long.parseLong(parts[2]);
        BigDecimal amount = null;

        if (parts.length > 3) {
            amount = new BigDecimal(parts[3]);
        }

        // 检查收款码是否过期（30分钟）
        if (System.currentTimeMillis() - timestamp > 30 * 60 * 1000) {
            throw new ApiException("收款码已过期");
        }

        if (amount == null) {
            throw new ApiException("收款码中未包含金额信息");
        }

        // 验证支付方用户
        User payerUser = userMapper.selectById(payerUserId);
        if (payerUser == null) {
            throw new ApiException("支付用户不存在");
        }

        // 验证收款方用户
        User receiverUser = userMapper.selectById(receiverUserId);
        if (receiverUser == null) {
            throw new ApiException("收款用户不存在");
        }

        if (payerUserId.equals(receiverUserId)) {
            throw new ApiException("不能向自己付款");
        }

        // 验证支付密码（从默认银行卡获取）
        BankCard defaultCard = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("user_id", payerUserId)
                        .eq("is_default", 1)
                        .eq("status", 1));

        if (defaultCard == null) {
            throw new ApiException("请先设置默认银行卡");
        }

        if (!payPassword.equals(defaultCard.getPayPassword())) {
            throw new ApiException("支付密码错误");
        }

        // 执行钱包转账
        walletTransfer(payerUserId, receiverUser.getPhone(), amount, payPassword, "扫码支付");
    }

    /**
     * 收款
     */
    @Transactional
    public void receiveMoney(Integer userId, BigDecimal amount, String fromPhone, String remark) {
        // 验证收款用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 验证收款账户
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        if (account == null) {
            throw new ApiException("钱包账户不存在");
        }
        if (account.getStatus() != 1) {
            throw new ApiException("钱包账户已冻结");
        }

        // 验证付款用户（通过手机号）
        User fromUser = userMapper.selectOne(
            new QueryWrapper<User>().eq("phone", fromPhone)
        );
        if (fromUser == null) {
            throw new ApiException("付款用户不存在");
        }

        if (userId.equals(fromUser.getUserId())) {
            throw new ApiException("不能向自己收款");
        }

        // 更新收款账户余额
        account.setBalance(account.getBalance().add(amount));
        account.setUpdatedAt(new Date());
        accountMapper.updateById(account);

        // 记录收款交易
        Transaction transaction = new Transaction();
        transaction.setTxnNo(generateTxnNo());
        transaction.setUserId(userId);
        transaction.setAccountId(account.getAccountId());
        transaction.setType(2); // 2-收入
        transaction.setAmount(amount);
        transaction.setBalance(account.getBalance());
        transaction.setPaymentMethod(1); // 1-钱包
        transaction.setStatus(1); // 1-成功
        transaction.setRemark(remark != null ? remark : "收款");
        transaction.setCreatedAt(new Date());
        transaction.setUpdatedAt(new Date());
        transactionMapper.insert(transaction);
    }

    /**
     * 生成交易流水号
     */
    private String generateTxnNo() {
        return "TXN" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    /**
     * 测试用：为用户钱包添加余额
     */
    @Transactional
    public void addTestBalance(Integer userId, BigDecimal amount) {
        // 验证用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 获取或创建钱包账户
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );

        if (account == null) {
            // 创建钱包账户
            account = new Account();
            account.setUserId(userId);
            account.setBalance(amount);
            account.setDailyLimit(new BigDecimal("50000"));
            account.setSingleLimit(new BigDecimal("10000"));
            account.setMonthlyLimit(new BigDecimal("200000"));
            account.setStatus(1);
            account.setCreatedAt(new Date());
            account.setUpdatedAt(new Date());
            accountMapper.insert(account);
        } else {
            // 更新余额
            BigDecimal newBalance = account.getBalance().add(amount);
            account.setBalance(newBalance);
            account.setUpdatedAt(new Date());
            accountMapper.updateById(account);
        }

        // 创建交易记录
        Transaction transaction = new Transaction();
        transaction.setTxnNo(generateTxnNo());
        transaction.setUserId(userId);
        transaction.setAccountId(account.getAccountId());
        transaction.setType(6); // 6-测试充值
        transaction.setAmount(amount);
        transaction.setBalance(account.getBalance());
        transaction.setPaymentMethod(1); // 1-钱包
        transaction.setStatus(1); // 1-成功
        transaction.setRemark("测试余额充值");
        transaction.setCreatedAt(new Date());
        transaction.setUpdatedAt(new Date());

        transactionMapper.insert(transaction);
    }

    /**
     * 测试用：初始化测试数据
     */
    @Transactional
    public void initTestData() {
        // 创建测试用户
        String[] testPhones = {"***********", "***********"};

        for (String phone : testPhones) {
            // 检查用户是否存在
            User existingUser = userMapper.selectOne(
                new QueryWrapper<User>().eq("phone", phone)
            );

            if (existingUser == null) {
                // 创建用户
                User user = new User();
                user.setPhone(phone);
                user.setPassword("123456");
                user.setUsername("测试用户" + phone.substring(7));
                user.setStatus(1);
                user.setCreatedAt(new Date());
                user.setUpdatedAt(new Date());
                userMapper.insert(user);
                existingUser = user;
            }

            // 为用户添加钱包余额
            addTestBalance(existingUser.getUserId(), new BigDecimal("20000"));

            // 为用户创建银行卡
            BankCard existingCard = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                    .eq("user_id", existingUser.getUserId())
                    .eq("card_number", "6222024" + phone.substring(4))
            );

            if (existingCard == null) {
                BankCard card = new BankCard();
                card.setUserId(existingUser.getUserId());
                card.setBankName("中国银行");
                card.setCardNumber("6222024" + phone.substring(4));
                card.setCardType(1); // 1-借记卡
                card.setExpiryDate("12/28");
                card.setCvv("123");
                card.setPhone(phone);
                card.setPayPassword("123456");
                card.setBalance(new BigDecimal("50000"));
                card.setIsDefault(1);
                card.setStatus(1);
                card.setCreatedAt(new Date());
                card.setUpdatedAt(new Date());
                bankCardMapper.insert(card);
            }
        }
    }
}
