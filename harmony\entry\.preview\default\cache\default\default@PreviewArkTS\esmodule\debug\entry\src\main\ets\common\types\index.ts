/**
 * 通用类型定义文件
 * 统一管理所有接口和数据模型的类型定义
 */
// ==================== 基础类型 ====================
/**
 * API响应基础接口 - 匹配SpringBoot3后端R类格式
 */
export interface ApiResponse<T> {
    code: number; // 0-成功，其他-失败
    msg: string; // 消息
    data: T; // 数据
}
/**
 * 分页查询参数基础接口
 */
export interface BasePageParams {
    page?: number;
    size?: number;
}
/**
 * 分页结果基础接口
 */
export interface PageResult<T> {
    records: T[];
    total: number;
    size: number;
    current: number;
    pages: number;
}
/**
 * 操作结果枚举
 */
export enum OperationResult {
    SUCCESS = "success",
    FAILED = "failed",
    PENDING = "pending"
}
/**
 * 加载状态枚举
 */
export enum LoadingState {
    IDLE = "idle",
    LOADING = "loading",
    SUCCESS = "success",
    ERROR = "error"
}
// ==================== 用户相关类型 ====================
/**
 * 用户信息接口
 */
export interface UserInfo {
    userId: number;
    phone: string;
    realName: string;
    idCard: string;
    payPassword?: string;
    payLimit: number;
    hasPayPassword: boolean; // 是否已设置支付密码
    createTime: string;
    updateTime: string;
}
/**
 * 用户登录请求
 */
export interface UserLoginRequest {
    phone: string;
    password: string;
    loginType: 'password' | 'sms';
}
/**
 * 用户登录响应
 */
export interface UserLoginResponse {
    token: string;
    userInfo: UserInfo;
}
/**
 * 用户注册请求
 */
export interface UserRegisterRequest {
    phone: string;
    password: string;
    realName: string;
    idCard: string;
    payPassword?: string;
    payLimit?: number;
}
/**
 * 修改支付密码请求
 */
export interface UpdatePayPasswordRequest {
    oldPassword?: string; // 首次设置时可以为空
    newPassword: string;
}
/**
 * 设置支付限额请求
 */
export interface UpdatePayLimitRequest {
    payLimit: number;
}
/**
 * 发送短信验证码请求
 */
export interface SendSmsCodeRequest {
    phone: string;
    type: string;
}
/**
 * 短信验证码登录请求
 */
export interface SmsLoginRequest {
    phone: string;
    code: string;
}
/**
 * 发送验证码请求（SpringBoot3格式）
 */
export interface SendCodeRequest {
    phone: string;
    type: number;
}
/**
 * SpringBoot3登录请求（密码登录）
 */
export interface SpringBootPasswordLoginRequest {
    phone: string;
    password: string;
    loginType: string;
}
/**
 * SpringBoot3登录请求（验证码登录）
 */
export interface SpringBootSmsLoginRequest {
    phone: string;
    verificationCode: string;
    loginType: string;
}
/**
 * SpringBoot3用户响应
 */
export interface SpringBootUserResponse {
    userId: number;
    phone: string;
    password?: string;
    realName?: string;
    idCard?: string;
    payPassword?: string;
    payLimit?: number;
    status: number;
    lastLogin?: string;
    createdAt?: string;
    updatedAt?: string;
    createTime?: string;
    updateTime?: string;
    username?: string;
}
/**
 * SpringBoot3账户响应
 */
export interface SpringBootAccountResponse {
    accountId: number;
    userId: number;
    accountNo: string;
    balance: number;
    status: number;
    createdAt?: string;
    updatedAt?: string;
    createTime?: string;
    updateTime?: string;
}
/**
 * SpringBoot3交易响应
 */
export interface SpringBootTransactionResponse {
    transactionId: number;
    transactionNo: string;
    fromUserId?: number;
    toUserId?: number;
    userId?: number;
    amount: number;
    type: number;
    paymentMethod?: number;
    description?: string;
    status: number;
    createdAt?: string;
    updatedAt?: string;
    createTime?: string;
    updateTime?: string;
    relatedCardId?: number;
    cardNo?: string;
    fromUserName?: string;
    toUserName?: string;
    targetUserId?: number;
    targetPhone?: string;
}
/**
 * SpringBoot3银行卡响应
 */
export interface SpringBootBankCardResponse {
    cardId: number;
    userId: number;
    cardNumber: string;
    cardType?: string | number;
    bankName: string;
    cardHolder: string;
    phone?: string;
    status: number;
    isDefault?: boolean | number; // 支持boolean或number类型（0/1）
    balance?: number; // 银行卡余额
    expiryDate?: string; // 信用卡到期日期
    cvv?: string; // 信用卡CVV
    createdAt?: string;
    updatedAt?: string;
    createTime?: string;
    updateTime?: string;
}
/**
 * SpringBoot3添加银行卡请求
 */
export interface SpringBootAddBankCardRequest {
    userId: number;
    cardNumber: string;
    cardType: number;
    bankName: string;
    cardHolder: string;
    phone: string;
    status: number;
    isDefault: number;
    balance?: number;
    expiryDate?: string; // 信用卡到期日期 (MM/YY格式)
    cvv?: string; // 信用卡CVV
}
/**
 * SpringBoot3解绑银行卡请求（空对象）
 */
export interface SpringBootUnbindBankCardRequest {
}
/**
 * SpringBoot3设置默认银行卡请求（空对象）
 */
export interface SpringBootSetDefaultCardRequest {
}
/**
 * SpringBoot3绑定银行卡请求（空对象）
 */
export interface SpringBootBindCardRequest {
}
/**
 * 对话框按钮接口
 */
export interface DialogButton {
    text: string;
    color: string;
}
/**
 * 银行卡页面路由参数
 */
export interface BankCardPageParams {
    selectMode?: boolean;
    selectedCardId?: number;
}
/**
 * 充值页面路由参数
 */
export interface RechargePageParams {
    selectedCard?: BankCard;
}
/**
 * 验证重置密码验证码请求
 */
export interface VerifyResetCodeRequest {
    phone: string;
    code: string;
}
/**
 * 重置密码请求
 */
export interface ResetPasswordRequest {
    phone: string;
    code: string;
    newPassword: string;
}
// ==================== 钱包相关类型 ====================
/**
 * 钱包信息接口
 */
export interface WalletInfo {
    walletId: number;
    userId: number;
    walletNo: string;
    balance: number;
    createTime: string;
    updateTime: string;
}
/**
 * 钱包充值请求
 */
export interface WalletRechargeRequest {
    userId: number;
    amount: number;
    cardId: number;
    payPassword: string;
}
/**
 * 钱包提现请求
 */
export interface WalletWithdrawRequest {
    userId: number;
    amount: number;
    cardId: number;
    payPassword: string;
}
/**
 * 钱包转账请求
 */
export interface WalletTransferRequest {
    toPhone: string;
    amount: number;
    payPassword: string;
    description?: string;
}
// ==================== 银行卡相关类型 ====================
/**
 * 银行卡类型枚举
 */
export enum BankCardType {
    DEBIT = "\u50A8\u84C4\u5361",
    CREDIT = "\u4FE1\u7528\u5361"
}
/**
 * 银行卡绑定状态枚举
 */
export enum BankCardStatus {
    UNBOUND = 0,
    BOUND = 1
}
/**
 * 银行卡信息
 */
export interface BankCard {
    cardId: number;
    userId: number;
    cardNo: string;
    cardType: BankCardType;
    bankName: string;
    holderName: string;
    isBound: BankCardStatus;
    createTime: string;
    updateTime: string;
    // 显示用的脱敏卡号
    maskedCardNo?: string;
    // 是否为默认卡
    isDefault?: boolean;
}
/**
 * 银行卡绑定请求
 */
export interface BankCardBindRequest {
    cardNo: string;
    cardType: BankCardType;
    bankName: string;
    holderName: string;
}
/**
 * 银行卡查询参数
 */
export interface BankCardQueryParams {
    bound?: string;
}
// ==================== 交易相关类型 ====================
/**
 * 交易类型枚举
 */
export enum TransactionType {
    RECHARGE = "\u5145\u503C",
    WITHDRAW = "\u63D0\u73B0",
    TRANSFER = "\u8F6C\u8D26",
    RECEIVE = "\u6536\u94B1",
    PAYMENT = "\u652F\u4ED8",
    REFUND = "\u9000\u6B3E"
}
/**
 * 支付方式枚举
 */
export enum PaymentMethod {
    BANK_CARD = "\u94F6\u884C\u5361",
    WALLET = "\u94B1\u5305\u4F59\u989D",
    THIRD_PARTY = "\u7B2C\u4E09\u65B9\u652F\u4ED8"
}
/**
 * 交易状态枚举
 */
export enum TransactionStatus {
    PENDING = "\u5904\u7406\u4E2D",
    SUCCESS = "\u6210\u529F",
    FAILED = "\u5931\u8D25",
    CANCELLED = "\u5DF2\u53D6\u6D88"
}
/**
 * 交易记录
 */
export interface Transaction {
    transactionId: number;
    transactionNo: string;
    fromUserId: number;
    toUserId: number;
    amount: number;
    transactionType: TransactionType;
    paymentMethod: PaymentMethod;
    description: string;
    status: TransactionStatus;
    createTime: string;
    relatedCardId?: number;
    cardNo?: string;
    statusDesc?: string;
    // 显示用的脱敏信息
    maskedCardNo?: string;
    fromUserName?: string;
    toUserName?: string;
}
/**
 * 交易记录查询参数
 */
export interface TransactionQueryParams {
    page?: string;
    size?: string;
    type?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
}
// ==================== 错误处理相关类型 ====================
/**
 * 错误类型枚举
 */
export enum ErrorType {
    NETWORK_ERROR = "NETWORK_ERROR",
    API_ERROR = "API_ERROR",
    VALIDATION_ERROR = "VALIDATION_ERROR",
    AUTH_ERROR = "AUTH_ERROR",
    BUSINESS_ERROR = "BUSINESS_ERROR",
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
}
/**
 * 错误信息接口
 */
export interface ErrorInfo {
    type: ErrorType;
    code: number;
    message: string;
    details?: string;
    timestamp: string;
}
/**
 * API错误类
 */
export class ApiError extends Error {
    public code: number;
    public type: ErrorType;
    public details?: string;
    constructor(code: number, message: string, type?: ErrorType, details?: string) {
        super(message);
        this.code = code;
        this.type = type || ErrorType.API_ERROR;
        this.details = details;
        this.name = 'ApiError';
    }
    toErrorInfo(): ErrorInfo {
        return {
            type: this.type,
            code: this.code,
            message: this.message,
            details: this.details,
            timestamp: new Date().toISOString()
        };
    }
}
// ==================== 表单验证相关类型 ====================
/**
 * 表单验证规则接口
 */
export interface ValidationRule {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    validator?: (value: string) => boolean | string;
}
/**
 * 表单字段验证结果
 */
export interface ValidationResult {
    isValid: boolean;
    errorMessage?: string;
}
/**
 * 表单验证状态
 */
export class FormValidationState {
    private validationResults: Map<string, ValidationResult> = new Map();
    set(fieldName: string, result: ValidationResult): void {
        this.validationResults.set(fieldName, result);
    }
    get(fieldName: string): ValidationResult | undefined {
        return this.validationResults.get(fieldName);
    }
    getAll(): Map<string, ValidationResult> {
        return this.validationResults;
    }
}
