<template>
  <div class="login-page">
    <div class="login-background">
      <div class="bg-pattern"></div>
    </div>

    <div class="login-container">
      <div class="login-card">
        <div class="login-header">
          <div class="bank-logo">
            <div class="logo-icon">🏦</div>
            <h1>银行管理系统</h1>
            <p>Bank Management System</p>
          </div>
        </div>

        <div class="login-form">
          <!-- 登录方式切换 -->
          <div class="login-tabs">
            <div
              class="tab-item"
              :class="{ active: loginType === 'password' }"
              @click="switchLoginType('password')"
            >
              <el-icon><Lock /></el-icon>
              密码登录
            </div>
            <div
              class="tab-item"
              :class="{ active: loginType === 'sms' }"
              @click="switchLoginType('sms')"
            >
              <el-icon><Message /></el-icon>
              验证码登录
            </div>
          </div>

          <el-form
            :model="loginForm"
            ref="loginFormRef"
            :rules="currentRules"
            @keyup.enter="handleLogin"
          >
            <el-form-item prop="phone">
              <el-input
                v-model="loginForm.phone"
                placeholder="请输入手机号"
                size="large"
                prefix-icon="User"
                class="login-input"
              />
            </el-form-item>

            <!-- 密码登录 -->
            <el-form-item prop="password" v-if="loginType === 'password'">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                size="large"
                prefix-icon="Lock"
                show-password
                class="login-input"
              />
            </el-form-item>

            <!-- 验证码登录 -->
            <el-form-item prop="verificationCode" v-if="loginType === 'sms'">
              <div class="verification-input">
                <el-input
                  v-model="loginForm.verificationCode"
                  placeholder="请输入验证码"
                  size="large"
                  prefix-icon="Message"
                  class="login-input code-input"
                  maxlength="6"
                />
                <el-button
                  type="primary"
                  size="large"
                  @click="sendVerificationCode"
                  :disabled="countdown > 0 || sendingCode"
                  :loading="sendingCode"
                  class="send-code-btn"
                >
                  {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
                </el-button>
              </div>

              <!-- 验证码显示区域 -->
              <div v-if="verificationCodeDisplay" class="verification-code-tip">
                当前验证码 {{ verificationCodeDisplay }}
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                size="large"
                @click="handleLogin"
                :loading="loading"
                class="login-btn"
              >
                {{ loading ? '登录中...' : '登录系统' }}
              </el-button>
            </el-form-item>
          </el-form>

          <div class="login-footer">
            <div class="demo-info">
              <p><strong>演示账号信息：</strong></p>
              <p>手机号：13800138000</p>
              <p>密码：123456</p>
              <p v-if="loginType === 'sms'" style="color: #9c27b0; font-weight: 600;">
                💡 验证码登录：发送验证码后，验证码将显示在输入框下方
              </p>
            </div>

            <div class="register-link">
              <p>还没有账户？ <router-link to="/register" class="link">立即注册</router-link></p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
<script setup>
import { ref, computed, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Lock, Message } from '@element-plus/icons-vue';
import { login, sendVerificationCode as sendSmsCode, loginWithSms } from '@/api/user';
import { setUserInfo, setToken } from '@/stores/user';

const router = useRouter();
const loading = ref(false);
const sendingCode = ref(false);
const countdown = ref(0);
const loginType = ref('password'); // 'password' 或 'sms'
const verificationCodeDisplay = ref(''); // 用于显示验证码
let countdownTimer = null;

const loginForm = ref({
  phone: '',
  password: '',
  verificationCode: ''
});

// 密码登录验证规则
const passwordRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
};

// 验证码登录验证规则
const smsRules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  verificationCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码为6位数字', trigger: 'blur' }
  ]
};

// 当前使用的验证规则
const currentRules = computed(() => {
  return loginType.value === 'password' ? passwordRules : smsRules;
});

const loginFormRef = ref(null);

// 切换登录方式
const switchLoginType = (type) => {
  loginType.value = type;
  // 清空表单
  loginForm.value = {
    phone: loginForm.value.phone, // 保留手机号
    password: '',
    verificationCode: ''
  };
  // 清除验证错误
  if (loginFormRef.value) {
    loginFormRef.value.clearValidate();
  }
};

// 发送验证码
const sendVerificationCode = async () => {
  if (!loginForm.value.phone) {
    ElMessage.error('请先输入手机号');
    return;
  }

  if (!/^1[3-9]\d{9}$/.test(loginForm.value.phone)) {
    ElMessage.error('请输入正确的手机号格式');
    return;
  }

  try {
    sendingCode.value = true;

    // 调用发送验证码API
    const response = await sendSmsCode(loginForm.value.phone, 1); // 1表示登录验证码

    if (response.code === 0) {
      // 确保只显示验证码数字，不显示其他信息
      const verificationCode = response.data;
      console.log('后端返回的验证码:', verificationCode);

      // 直接设置验证码数字
      verificationCodeDisplay.value = verificationCode;
      console.log('设置的验证码显示:', verificationCodeDisplay.value);

      startCountdown();
    } else {
      ElMessage.error(response.msg || '验证码发送失败');
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    ElMessage.error('验证码发送失败，请重试');
  } finally {
    sendingCode.value = false;
  }
};

// 格式化验证码消息
const formatVerificationMessage = (message) => {
  console.log('formatVerificationMessage 被调用，参数:', message);
  if (!message) return '';

  // 如果后端直接返回6位数字验证码
  if (/^\d{6}$/.test(message)) {
    console.log('匹配到6位数字验证码:', message);
    return `验证码：${message}`;
  }

  // 提取验证码（6位数字）
  const codeMatch = message.match(/验证码：(\d{6})/);
  if (codeMatch) {
    const code = codeMatch[1];
    console.log('从标准格式提取验证码:', code);
    return `验证码：${code}`;
  }

  // 如果没有匹配到标准格式，尝试提取任何6位数字
  const numberMatch = message.match(/(\d{6})/);
  if (numberMatch) {
    const code = numberMatch[1];
    console.log('从任意格式提取验证码:', code);
    return `验证码：${code}`;
  }

  console.log('没有匹配到验证码格式，返回原始消息:', message);
  return message;
};

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60;
  countdownTimer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer);
      countdownTimer = null;
    }
  }, 1000);
};

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return;

  try {
    // 表单验证
    await loginFormRef.value.validate();

    loading.value = true;

    let response;

    if (loginType.value === 'password') {
      // 密码登录
      response = await login(loginForm.value.phone, loginForm.value.password);
    } else {
      // 验证码登录
      response = await loginWithSms(loginForm.value.phone, loginForm.value.verificationCode);
    }

    if (response.code === 0) {
      // 登录成功，保存用户信息和token
      const user = response.data;
      setUserInfo(user);
      setToken(`user_${user.userId}_${Date.now()}`); // 生成简单的token

      ElMessage.success(response.msg || '登录成功！欢迎使用银行管理系统');

      // 使用 replace 而不是 push，避免用户通过后退按钮回到登录页
      await router.replace('/dashboard');
    } else {
      ElMessage.error(response.msg || '登录失败');
    }
  } catch (error) {
    console.error('登录失败:', error);
    if (error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error('登录失败，请检查网络连接');
    }
  } finally {
    loading.value = false;
  }
};

// 组件卸载时清理定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
});
</script>
  
<style scoped>
.login-page {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
}

.bg-pattern {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

.login-container {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 450px;
  padding: 20px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  overflow: hidden;
  animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  background: linear-gradient(135deg, #1e3c72, #2a5298);
  color: white;
  padding: 40px 30px;
  text-align: center;
  position: relative;
}

.login-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
  opacity: 0.3;
}

.bank-logo {
  position: relative;
  z-index: 2;
}

.logo-icon {
  font-size: 60px;
  margin-bottom: 15px;
  animation: pulse 2s infinite;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.bank-logo h1 {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

.bank-logo p {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
  font-weight: 300;
  letter-spacing: 0.5px;
}

.login-form {
  padding: 40px 30px;
}

.login-tabs {
  display: flex;
  margin-bottom: 30px;
  background: #f5f7fa;
  border-radius: 12px;
  padding: 4px;
  position: relative;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  background: transparent;
}

.tab-item:hover {
  color: #2a5298;
  background: rgba(42, 82, 152, 0.1);
}

.tab-item.active {
  color: #2a5298;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.verification-input {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

/* 验证码显示区域样式 */
.verification-code-tip {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  color: #666;
  font-size: 14px;
  text-align: left;
  border: 1px solid #e0e0e0;
}

.code-input {
  flex: 1;
}

.send-code-btn {
  min-width: 120px;
  height: 40px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.login-form :deep(.el-form-item) {
  margin-bottom: 25px;
}

.login-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid #e1e8ed;
  transition: all 0.3s ease;
  background: #fafbfc;
}

.login-input :deep(.el-input__wrapper:hover) {
  border-color: #2a5298;
  box-shadow: 0 4px 12px rgba(42, 82, 152, 0.15);
  background: #ffffff;
}

.login-input :deep(.el-input__wrapper.is-focus) {
  border-color: #2a5298;
  box-shadow: 0 4px 12px rgba(42, 82, 152, 0.25);
  background: #ffffff;
}

.login-input :deep(.el-input__inner) {
  font-size: 16px;
  padding: 12px 16px;
}

.login-btn {
  width: 100%;
  height: 50px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #1e3c72, #2a5298);
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(42, 82, 152, 0.4);
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(42, 82, 152, 0.5);
  background: linear-gradient(135deg, #2a5298, #1e3c72);
}

.login-btn:active {
  transform: translateY(0);
}

.login-footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e1e8ed;
}

.demo-info {
  background: linear-gradient(135deg, #f8f9ff, #e8f2ff);
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #2a5298;
  text-align: left;
}

.demo-info p {
  margin: 5px 0;
  color: #5a6c7d;
  font-size: 14px;
}

.demo-info p:first-child {
  color: #2a5298;
  font-weight: 600;
  margin-bottom: 10px;
}

.register-link {
  text-align: center;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e1e8ed;
}

.register-link p {
  margin: 0;
  color: #5a6c7d;
  font-size: 14px;
}

.register-link .link {
  color: #2a5298;
  text-decoration: none;
  font-weight: 600;
}

.register-link .link:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 15px;
  }

  .login-card {
    border-radius: 15px;
  }

  .login-header {
    padding: 30px 20px;
  }

  .bank-logo h1 {
    font-size: 24px;
  }

  .logo-icon {
    font-size: 50px;
  }

  .login-form {
    padding: 30px 20px;
  }
}
</style>