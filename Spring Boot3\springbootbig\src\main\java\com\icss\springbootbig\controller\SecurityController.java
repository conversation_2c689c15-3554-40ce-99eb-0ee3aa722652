package com.icss.springbootbig.controller;

import com.icss.springbootbig.result.R;
import com.icss.springbootbig.service.SecurityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@CrossOrigin(origins = {"http://localhost:5173", "http://127.0.0.1:5173"})
@RestController
@RequestMapping("/api/security")
public class SecurityController {
    
    @Autowired
    private SecurityService securityService;

    /**
     * 修改登录密码
     */
    @PutMapping("/change-login-password")
    public R<String> changeLoginPassword(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            String oldPassword = (String) request.get("oldPassword");
            String newPassword = (String) request.get("newPassword");
            
            securityService.changeLoginPassword(userId, oldPassword, newPassword);
            return R.success("登录密码修改成功");
        } catch (Exception e) {
            return R.failure("密码修改失败: " + e.getMessage());
        }
    }

    /**
     * 设置支付密码
     */
    @PostMapping("/set-pay-password")
    public R<String> setPayPassword(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            String payPassword = (String) request.get("payPassword");
            String loginPassword = (String) request.get("loginPassword");
            
            securityService.setPayPassword(userId, payPassword, loginPassword);
            return R.success("支付密码设置成功");
        } catch (Exception e) {
            return R.failure("支付密码设置失败: " + e.getMessage());
        }
    }

    /**
     * 修改支付密码
     */
    @PutMapping("/change-pay-password")
    public R<String> changePayPassword(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            String oldPayPassword = (String) request.get("oldPayPassword");
            String newPayPassword = (String) request.get("newPayPassword");
            
            securityService.changePayPassword(userId, oldPayPassword, newPayPassword);
            return R.success("支付密码修改成功");
        } catch (Exception e) {
            return R.failure("支付密码修改失败: " + e.getMessage());
        }
    }

    /**
     * 重置支付密码
     */
    @PutMapping("/reset-pay-password")
    public R<String> resetPayPassword(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            String loginPassword = (String) request.get("loginPassword");
            String newPayPassword = (String) request.get("newPayPassword");
            
            securityService.resetPayPassword(userId, loginPassword, newPayPassword);
            return R.success("支付密码重置成功");
        } catch (Exception e) {
            return R.failure("支付密码重置失败: " + e.getMessage());
        }
    }

    /**
     * 获取安全设置信息
     */
    @GetMapping("/settings/{userId}")
    public R<Map<String, Object>> getSecuritySettings(@PathVariable Integer userId) {
        try {
            Map<String, Object> settings = securityService.getSecuritySettings(userId);
            return R.success("查询成功", settings);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用指纹登录
     */
    @PutMapping("/fingerprint/{userId}")
    public R<String> setFingerprintEnabled(
            @PathVariable Integer userId, 
            @RequestParam Boolean enabled) {
        try {
            securityService.setFingerprintEnabled(userId, enabled);
            return R.success(enabled ? "指纹登录已启用" : "指纹登录已禁用");
        } catch (Exception e) {
            return R.failure("设置失败: " + e.getMessage());
        }
    }

    /**
     * 获取登录设备记录
     */
    @GetMapping("/devices/{userId}")
    public R<List<Map<String, Object>>> getLoginDevices(@PathVariable Integer userId) {
        try {
            List<Map<String, Object>> devices = securityService.getLoginDevices(userId);
            return R.success("查询成功", devices);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取风险提醒
     */
    @GetMapping("/risk-alerts/{userId}")
    public R<List<Map<String, Object>>> getRiskAlerts(@PathVariable Integer userId) {
        try {
            List<Map<String, Object>> alerts = securityService.getRiskAlerts(userId);
            return R.success("查询成功", alerts);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 账户安全检查
     */
    @GetMapping("/check/{userId}")
    public R<Map<String, Object>> securityCheck(@PathVariable Integer userId) {
        try {
            Map<String, Object> checkResult = securityService.securityCheck(userId);
            return R.success("安全检查完成", checkResult);
        } catch (Exception e) {
            return R.failure("安全检查失败: " + e.getMessage());
        }
    }
}
