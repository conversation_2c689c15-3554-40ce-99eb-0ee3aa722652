import request from '@/utils/request'

// 交易API服务
const transactionApi = {
  // 分页获取交易记录
  getTransactionsByPage(params) {
    return request({
      url: '/api/transaction/query',
      method: 'get',
      params
    })
  },

  // 获取用户最近交易记录
  getRecentTransactions(userId, limit = 5) {
    return request({
      url: `/api/transaction/recent/${userId}`,
      method: 'get',
      params: { limit }
    })
  },

  // 获取交易详情
  getTransactionDetail(txnId, userId) {
    return request({
      url: `/api/transaction/detail/${txnId}`,
      method: 'get',
      params: { userId }
    })
  },

  // 删除交易记录
  deleteTransaction(txnId) {
    return request({
      url: `/api/transaction/${txnId}`,
      method: 'delete'
    })
  },

  // 批量删除交易记录
  batchDeleteTransactions(txnIds) {
    return request({
      url: '/api/transaction/batch-delete',
      method: 'post',
      data: txnIds
    })
  },

  // 获取交易统计
  getTransactionStats(userId, startDate, endDate) {
    return request({
      url: `/api/transaction/stats/${userId}`,
      method: 'get',
      params: { startDate, endDate }
    })
  },

  // 生成收款码
  generateReceiveCode(userId, amount, remark) {
    return request({
      url: '/api/transaction/generate-receive-code',
      method: 'post',
      data: { userId, amount, remark }
    })
  },

  // 扫码收款
  scanCodeReceive(receiveCode, payerUserId, payPassword) {
    return request({
      url: '/api/transaction/scan-code-receive',
      method: 'post',
      data: { receiveCode, payerUserId, payPassword }
    })
  },

  // 导出交易记录
  exportTransactions(params) {
    return request({
      url: '/api/transaction/export',
      method: 'get',
      params,
      responseType: 'blob'
    })
  }
}

// 钱包API服务
const walletApi = {
  // 获取钱包余额
  getWalletBalance(userId) {
    return request({
      url: `/api/wallet/balance/${userId}`,
      method: 'get'
    })
  },

  // 钱包充值
  recharge(data) {
    return request({
      url: '/api/wallet/recharge',
      method: 'post',
      data
    })
  },

  // 钱包提现
  withdraw(data) {
    return request({
      url: '/api/wallet/withdraw',
      method: 'post',
      data
    })
  },

  // 钱包转账
  transfer(data) {
    return request({
      url: '/api/wallet/transfer',
      method: 'post',
      data
    })
  },

  // 设置钱包限额
  setWalletLimits(userId, limits) {
    return request({
      url: `/api/wallet/limits/${userId}`,
      method: 'put',
      data: limits
    })
  },

  // 创建钱包账户
  createWalletAccount(userId) {
    return request({
      url: '/api/wallet/create-account',
      method: 'post',
      data: { userId }
    })
  },

  // 初始化所有用户钱包
  initializeAllWallets() {
    return request({
      url: '/api/wallet/initialize-all',
      method: 'post'
    })
  }
}

// 支付API服务
const paymentApi = {
  // 获取钱包余额
  getWalletBalance(userId) {
    return request({
      url: `/api/wallet/balance/${userId}`,
      method: 'get'
    })
  },

  // 获取银行卡列表
  getBankCards(userId) {
    return request({
      url: `/api/bank-card/user/${userId}`,
      method: 'get'
    })
  },



  // 钱包支付
  walletPayment(data) {
    return request({
      url: '/api/payment/wallet',
      method: 'post',
      params: {
        userId: data.userId,
        amount: data.amount,
        paymentType: data.paymentType || 'payment',
        payPassword: data.payPassword || '123456',
        remark: data.remark,
        merchantId: data.merchantId
      }
    })
  },

  // 银行卡支付
  bankCardPayment(data) {
    return request({
      url: '/api/payment/bank-card',
      method: 'post',
      data: {
        userId: data.userId,
        amount: data.amount,
        cardId: data.cardId,
        merchantId: data.merchantId,
        payPassword: data.payPassword || '123456',
        remark: data.remark
      }
    })
  },

  // 扫码支付
  qrCodePayment(data) {
    return request({
      url: '/api/payment/qrcode',
      method: 'post',
      data
    })
  },

  // NFC支付
  nfcPayment(data) {
    return request({
      url: '/api/payment/nfc',
      method: 'post',
      data
    })
  }
}

// 安全API服务
const securityApi = {
  // 设置支付密码
  setPayPassword(data) {
    return request({
      url: '/api/security/set-pay-password',
      method: 'post',
      data
    })
  },

  // 修改支付密码
  changePayPassword(data) {
    return request({
      url: '/api/security/change-pay-password',
      method: 'put',
      data
    })
  },

  // 重置支付密码
  resetPayPassword(data) {
    return request({
      url: '/api/security/reset-pay-password',
      method: 'put',
      data
    })
  },

  // 修改登录密码
  changeLoginPassword(data) {
    return request({
      url: '/api/security/change-login-password',
      method: 'put',
      data
    })
  },

  // 获取安全设置
  getSecuritySettings(userId) {
    return request({
      url: `/api/security/settings/${userId}`,
      method: 'get'
    })
  },

  // 启用/禁用指纹登录
  setFingerprintEnabled(userId, enabled) {
    return request({
      url: `/api/security/fingerprint/${userId}`,
      method: 'put',
      params: { enabled }
    })
  }
}

// 银行管理API服务
const bankApi = {
  // 获取所有银行
  getAllBanks() {
    return request({
      url: '/api/bank/all',
      method: 'get'
    })
  },

  // 分页获取银行列表
  getBanksByPage(params) {
    return request({
      url: '/api/bank/page',
      method: 'get',
      params
    })
  },

  // 添加银行
  addBank(data) {
    return request({
      url: '/api/bank',
      method: 'post',
      data
    })
  },

  // 更新银行信息
  updateBank(bankId, data) {
    return request({
      url: `/api/bank/${bankId}`,
      method: 'put',
      data
    })
  },

  // 删除银行
  deleteBank(bankId) {
    return request({
      url: `/api/bank/${bankId}`,
      method: 'delete'
    })
  },

  // 批量删除银行
  batchDeleteBanks(bankIds) {
    return request({
      url: '/api/bank/batch-delete',
      method: 'post',
      data: bankIds
    })
  }
}

// 导出所有API
export {
  transactionApi,
  walletApi,
  paymentApi,
  securityApi,
  bankApi
}

export default {
  transaction: transactionApi,
  wallet: walletApi,
  payment: paymentApi,
  security: securityApi,
  bank: bankApi
}
