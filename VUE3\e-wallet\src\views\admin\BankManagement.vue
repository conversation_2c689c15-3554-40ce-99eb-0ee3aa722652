<!-- eslint-disable vue/no-v-model-argument -->
<template>
  <div class="bank-management">
    <div class="header">
      <h2>银行管理</h2>
      <div class="header-buttons">
        <el-button type="success" @click="showBindingDialog = true">
          <el-icon><CreditCard /></el-icon>
          绑定管理
        </el-button>
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加银行
        </el-button>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索银行名称、简称或代码"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.status" placeholder="状态" clearable>
            <el-option label="全部" :value="null" />
            <el-option label="正常" :value="1" />
            <el-option label="停用" :value="0" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 银行列表 -->
    <div class="bank-table">
      <el-table
        :data="bankList"
        v-loading="loading"
        stripe
        style="width: 100%"
        :header-cell-style="{ textAlign: 'center', backgroundColor: '#f5f7fa' }"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column prop="bankId" label="ID" width="80" align="center" />
        <el-table-column prop="bankCode" label="银行代码" width="120" align="center" />
        <el-table-column prop="bankName" label="银行名称" min-width="150" align="center" />
        <el-table-column prop="bankShortName" label="简称" width="120" align="center" />
        <el-table-column prop="contactPhone" label="联系电话" width="140" align="center" />
        <el-table-column prop="sortOrder" label="排序" width="80" align="center" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160" align="center">
          <template #default="scope">
            <span style="font-size: 12px;">
              {{ formatDate(scope.row.createdAt) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right" align="center">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button
                size="small"
                :type="scope.row.status === 1 ? 'warning' : 'success'"
                @click="handleStatusChange(scope.row)"
              >
                {{ scope.row.status === 1 ? '停用' : '启用' }}
              </el-button>
              <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          :current-page="pagination.pageNum"
          :page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加/编辑银行对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="showAddDialog"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="bankFormRef"
        :model="bankForm"
        :rules="bankRules"
        label-width="100px"
      >
        <el-form-item label="银行代码" prop="bankCode">
          <el-input v-model="bankForm.bankCode" placeholder="请输入银行代码" />
        </el-form-item>
        <el-form-item label="银行名称" prop="bankName">
          <el-input v-model="bankForm.bankName" placeholder="请输入银行名称" />
        </el-form-item>
        <el-form-item label="银行简称" prop="bankShortName">
          <el-input v-model="bankForm.bankShortName" placeholder="请输入银行简称" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="bankForm.contactPhone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="总行地址" prop="address">
          <el-input v-model="bankForm.address" placeholder="请输入总行地址" />
        </el-form-item>
        <el-form-item label="官方网站" prop="website">
          <el-input v-model="bankForm.website" placeholder="请输入官方网站" />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="bankForm.sortOrder" :min="1" :max="999" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="bankForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="bankForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入银行描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 绑定管理对话框 -->
    <el-dialog
      title="银行卡绑定管理"
      v-model="showBindingDialog"
      width="1000px"
      @open="loadBankCards"
    >
      <div class="binding-management">
        <!-- 操作按钮 -->
        <div class="binding-header">
          <el-button type="primary" @click="showAddCardDialog = true">
            <el-icon><Plus /></el-icon>
            添加银行卡
          </el-button>
          <el-button @click="loadBankCards">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>

        <!-- 银行卡列表 -->
        <el-table
          :data="bankCards"
          v-loading="bindingLoading"
          stripe
          style="width: 100%; margin-top: 20px;"
          :header-cell-style="{ textAlign: 'center', backgroundColor: '#f5f7fa' }"
          :cell-style="{ textAlign: 'center' }"
        >
          <el-table-column prop="cardId" label="ID" width="80" align="center" />
          <el-table-column prop="bankName" label="银行名称" width="120" align="center" />
          <el-table-column prop="cardNumber" label="卡号" width="180" align="center">
            <template #default="scope">
              <span style="font-family: monospace;">
                **** **** **** {{ scope.row.cardNumber.slice(-4) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="cardHolder" label="持卡人" width="120" align="center" />
          <el-table-column prop="cardType" label="卡片类型" width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.cardType === 1 ? 'success' : 'warning'">
                {{ scope.row.cardType === 1 ? '储蓄卡' : '信用卡' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="手机号" width="120" align="center" />
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? '正常' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="isDefault" label="默认卡" width="100" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.isDefault === 1" type="primary" size="small">
                默认卡
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" width="160" align="center">
            <template #default="scope">
              <span style="font-size: 12px;">
                {{ formatDate(scope.row.createdAt) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220" fixed="right" align="center">
            <template #default="scope">
              <div class="action-buttons">
                <el-button size="small" @click="viewCardDetails(scope.row)">详情</el-button>
                <el-button
                  size="small"
                  :type="scope.row.status === 1 ? 'warning' : 'success'"
                  @click="toggleCardStatus(scope.row)"
                >
                  {{ scope.row.status === 1 ? '解绑' : '绑定' }}
                </el-button>
                <el-button size="small" type="danger" @click="deleteCard(scope.row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <el-button @click="showBindingDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 添加银行卡对话框 -->
    <el-dialog v-model="showAddCardDialog" title="添加银行卡" width="500px">
      <el-form :model="addCardForm" :rules="addCardRules" ref="addCardFormRef" label-width="100px">
        <el-form-item label="银行名称" prop="bankName">
          <el-select v-model="addCardForm.bankName" placeholder="请选择银行" filterable>
            <el-option
              v-for="bank in bankList"
              :key="bank.bankId"
              :label="bank.bankName"
              :value="bank.bankName"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="卡片类型" prop="cardType">
          <el-radio-group v-model="addCardForm.cardType">
            <el-radio :value="1">储蓄卡</el-radio>
            <el-radio :value="2">信用卡</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="卡号" prop="cardNumber">
          <el-input
            v-model="addCardForm.cardNumber"
            placeholder="请输入银行卡号"
            maxlength="19"
            @input="formatCardNumber"
          ></el-input>
        </el-form-item>

        <el-form-item label="持卡人姓名" prop="cardHolder">
          <el-input v-model="addCardForm.cardHolder" placeholder="请输入持卡人姓名"></el-input>
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input v-model="addCardForm.phone" placeholder="请输入预留手机号"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddCardDialog = false">取消</el-button>
        <el-button type="primary" @click="handleAddCard">确认添加</el-button>
      </template>
    </el-dialog>

    <!-- 银行卡详情对话框 -->
    <el-dialog v-model="showCardDetailsDialog" title="银行卡详情" width="400px">
      <div v-if="selectedCard" class="card-details-content">
        <div class="detail-info">
          <div class="info-item">
            <label>银行名称：</label>
            <span>{{ selectedCard.bankName }}</span>
          </div>
          <div class="info-item">
            <label>卡片类型：</label>
            <span>{{ selectedCard.cardType === 1 ? '储蓄卡' : '信用卡' }}</span>
          </div>
          <div class="info-item">
            <label>卡号：</label>
            <span>**** **** **** {{ selectedCard.cardNumber.slice(-4) }}</span>
          </div>
          <div class="info-item">
            <label>持卡人：</label>
            <span>{{ selectedCard.cardHolder }}</span>
          </div>
          <div class="info-item">
            <label>手机号：</label>
            <span>{{ selectedCard.phone }}</span>
          </div>
          <div class="info-item">
            <label>状态：</label>
            <el-tag :type="selectedCard.status === 1 ? 'success' : 'warning'" size="small">
              {{ selectedCard.status === 1 ? '正常' : '停用' }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>是否默认：</label>
            <el-tag v-if="selectedCard.isDefault === 1" type="primary" size="small">
              默认卡
            </el-tag>
            <span v-else>否</span>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showCardDetailsDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, CreditCard, Refresh } from '@element-plus/icons-vue'
import { bankApi } from '@/api/transaction'
import bankCardApi from '@/api/bankCard'
import { getUserInfo } from '@/stores/user'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showAddDialog = ref(false)
const isEdit = ref(false)
const bankList = ref([])

// 绑定管理相关
const showBindingDialog = ref(false)
const bindingLoading = ref(false)
const bankCards = ref([])
const currentUser = getUserInfo()
const currentUserId = ref(currentUser?.userId || 1)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: null
})

// 分页信息
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 银行表单
const bankForm = reactive({
  bankId: null,
  bankCode: '',
  bankName: '',
  bankShortName: '',
  contactPhone: '',
  address: '',
  website: '',
  description: '',
  status: 1,
  sortOrder: 999
})

// 表单验证规则
const bankRules = {
  bankCode: [
    { required: true, message: '请输入银行代码', trigger: 'blur' },
    { min: 2, max: 20, message: '银行代码长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  bankName: [
    { required: true, message: '请输入银行名称', trigger: 'blur' },
    { min: 2, max: 100, message: '银行名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  bankShortName: [
    { required: true, message: '请输入银行简称', trigger: 'blur' },
    { min: 2, max: 50, message: '银行简称长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

const bankFormRef = ref()

// 银行卡相关
const showAddCardDialog = ref(false)
const showCardDetailsDialog = ref(false)
const selectedCard = ref(null)
const addCardFormRef = ref(null)

// 添加银行卡表单
const addCardForm = reactive({
  userId: currentUserId.value || 1,
  bankName: '',
  cardType: 1,
  cardNumber: '',
  cardHolder: '',
  phone: '',
  isDefault: 0,
  status: 1
})

// 银行卡表单验证规则
const addCardRules = {
  bankName: [
    { required: true, message: '请选择银行', trigger: 'change' }
  ],
  cardType: [
    { required: true, message: '请选择卡片类型', trigger: 'change' }
  ],
  cardNumber: [
    { required: true, message: '请输入银行卡号', trigger: 'blur' },
    { min: 16, max: 19, message: '银行卡号长度应为16-19位', trigger: 'blur' }
  ],
  cardHolder: [
    { required: true, message: '请输入持卡人姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑银行' : '添加银行')

// 方法
const loadBankList = async () => {
  loading.value = true
  try {
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword || undefined,
      status: searchForm.status
    }
    
    const response = await bankApi.getBanksByPage(params)
    if (response.code === 0) {
      bankList.value = response.data.records
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.msg || '获取银行列表失败')
    }
  } catch (error) {
    console.error('获取银行列表失败:', error)
    ElMessage.error('获取银行列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.pageNum = 1
  loadBankList()
}

const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.status = null
  pagination.pageNum = 1
  loadBankList()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  loadBankList()
}

const handleCurrentChange = (page) => {
  pagination.pageNum = page
  loadBankList()
}

const handleEdit = (bank) => {
  isEdit.value = true
  Object.assign(bankForm, bank)
  showAddDialog.value = true
}

const handleStatusChange = async (bank) => {
  const newStatus = bank.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '启用' : '停用'
  
  try {
    await ElMessageBox.confirm(`确定要${action}该银行吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await bankApi.updateBank(bank.bankId, { status: newStatus })
    if (response.code === 0) {
      ElMessage.success(`${action}成功`)
      loadBankList()
    } else {
      ElMessage.error(response.msg || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}银行失败:`, error)
      ElMessage.error(`${action}失败`)
    }
  }
}

const handleDelete = async (bank) => {
  try {
    await ElMessageBox.confirm('确定要删除该银行吗？删除后不可恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await bankApi.deleteBank(bank.bankId)
    if (response.code === 0) {
      ElMessage.success('删除成功')
      loadBankList()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除银行失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  if (!bankFormRef.value) return
  
  try {
    await bankFormRef.value.validate()
    submitting.value = true
    
    let response
    if (isEdit.value) {
      response = await bankApi.updateBank(bankForm.bankId, bankForm)
    } else {
      response = await bankApi.addBank(bankForm)
    }
    
    if (response.code === 0) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      showAddDialog.value = false
      loadBankList()
    } else {
      ElMessage.error(response.msg || (isEdit.value ? '更新失败' : '创建失败'))
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  isEdit.value = false
  Object.assign(bankForm, {
    bankId: null,
    bankCode: '',
    bankName: '',
    bankShortName: '',
    contactPhone: '',
    address: '',
    website: '',
    description: '',
    status: 1,
    sortOrder: 999
  })
  if (bankFormRef.value) {
    bankFormRef.value.clearValidate()
  }
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 绑定管理相关方法
const loadBankCards = async () => {
  bindingLoading.value = true
  try {
    const response = await bankCardApi.getAllCards()
    if (response.code === 0) {
      bankCards.value = response.data
    } else {
      ElMessage.error(response.msg || '获取银行卡列表失败')
    }
  } catch (error) {
    console.error('获取银行卡列表失败:', error)
    ElMessage.error('获取银行卡列表失败')
  } finally {
    bindingLoading.value = false
  }
}

const formatCardNumber = () => {
  let value = addCardForm.cardNumber.replace(/\s/g, '')
  value = value.replace(/(.{4})/g, '$1 ').trim()
  addCardForm.cardNumber = value
}

const handleAddCard = async () => {
  if (!addCardFormRef.value) return

  try {
    await addCardFormRef.value.validate()

    const cardData = {
      ...addCardForm,
      cardNumber: addCardForm.cardNumber.replace(/\s/g, ''),
      userId: currentUserId.value || 1
    }

    const response = await bankCardApi.addBankCard(cardData)

    if (response.code === 0) {
      ElMessage.success('银行卡添加成功！')
      showAddCardDialog.value = false
      await loadBankCards()
      resetAddCardForm()
    } else {
      ElMessage.error(response.msg || '添加银行卡失败')
    }
  } catch (error) {
    ElMessage.error('添加银行卡失败')
    console.error('添加银行卡失败:', error)
  }
}

const resetAddCardForm = () => {
  Object.assign(addCardForm, {
    userId: currentUserId.value || 1,
    bankName: '',
    cardType: 1,
    cardNumber: '',
    cardHolder: '',
    phone: '',
    isDefault: 0,
    status: 1
  })
  if (addCardFormRef.value) {
    addCardFormRef.value.clearValidate()
  }
}

const viewCardDetails = async (card) => {
  try {
    const response = await bankCardApi.getCardDetails(card.cardId)
    if (response.code === 0) {
      selectedCard.value = response.data
      showCardDetailsDialog.value = true
    } else {
      ElMessage.error(response.msg || '获取银行卡详情失败')
    }
  } catch (error) {
    ElMessage.error('获取银行卡详情失败')
    console.error('获取详情失败:', error)
  }
}

const toggleCardStatus = async (card) => {
  const newStatus = card.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '绑定' : '解绑'

  try {
    await ElMessageBox.confirm(`确定要${action}该银行卡吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await bankCardApi.updateBankCard(card.cardId, { status: newStatus })
    if (response.code === 0) {
      ElMessage.success(`${action}成功`)
      loadBankCards()
    } else {
      ElMessage.error(response.msg || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}银行卡失败:`, error)
      ElMessage.error(`${action}失败`)
    }
  }
}

const deleteCard = async (card) => {
  try {
    await ElMessageBox.confirm('确定要删除该银行卡吗？删除后不可恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await bankCardApi.deleteBankCard(card.cardId)
    if (response.code === 0) {
      ElMessage.success('删除成功')
      loadBankCards()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除银行卡失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 生命周期
onMounted(() => {
  loadBankList()
})
</script>

<style scoped>
.bank-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.bank-table {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}

.binding-management {
  min-height: 400px;
}

.binding-header {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.card-details-content {
  padding: 20px;
}

.detail-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: bold;
  width: 100px;
  color: #666;
}

.info-item span {
  color: #333;
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #f5f7fa !important;
  color: #606266;
  font-weight: 600;
  text-align: center;
  padding: 12px 0;
}

:deep(.el-table td) {
  text-align: center;
  padding: 10px 0;
  vertical-align: middle;
}

:deep(.el-table .cell) {
  padding: 0 8px;
  word-break: break-word;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 5px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 2px;
  min-width: 50px;
}

/* 状态标签样式 */
:deep(.el-tag) {
  font-weight: 500;
  border-radius: 4px;
}
</style>
