import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
import type { WalletInfo, WalletRechargeRequest, WalletWithdrawRequest, WalletTransferRequest } from '../common/types/index';
/**
 * 钱包相关API接口
 */
export class WalletApi {
    /**
     * 查询钱包余额
     */
    static async getBalance(): Promise<WalletInfo> {
        const response = await httpClient.get<WalletInfo>('/wallet/balance');
        return response.data;
    }
    /**
     * 钱包充值
     */
    static async recharge(data: WalletRechargeRequest): Promise<void> {
        await httpClient.post<void>('/wallet/recharge', data);
    }
    /**
     * 钱包提现（钱包余额转到银行卡）
     */
    static async withdraw(data: WalletWithdrawRequest): Promise<void> {
        await httpClient.post<void>('/wallet/withdraw', data);
    }
    /**
     * 钱包转账
     */
    static async transfer(data: WalletTransferRequest): Promise<void> {
        await httpClient.post<void>('/wallet/transfer', data);
    }
}
