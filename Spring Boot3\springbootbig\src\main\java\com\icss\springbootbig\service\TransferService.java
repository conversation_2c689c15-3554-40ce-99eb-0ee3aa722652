package com.icss.springbootbig.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icss.springbootbig.entity.Account;
import com.icss.springbootbig.entity.Transaction;
import com.icss.springbootbig.enums.TransactionType;
import com.icss.springbootbig.mapper.AccountMapper;
import com.icss.springbootbig.mapper.TransactionMapper;
import com.icss.springbootbig.exception.ApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

@Service
public class TransferService {
    @Autowired
    private AccountMapper accountMapper;
    @Autowired
    private TransactionMapper transactionMapper;

    @Transactional
    public void transfer(Integer fromUserId, Integer toUserId, BigDecimal amount) {
        // 验证转出账户
        Account fromAccount = accountMapper.selectOne(
                new QueryWrapper<Account>().eq("user_id", fromUserId));
        if (fromAccount == null) {
            throw new ApiException("转出账户不存在");
        }

        // 验证转入账户
        Account toAccount = accountMapper.selectOne(
                new QueryWrapper<Account>().eq("user_id", toUserId));
        if (toAccount == null) {
            throw new ApiException("转入账户不存在");
        }

        // 检查余额
        if (fromAccount.getBalance().compareTo(amount) < 0) {
            throw new ApiException("余额不足");
        }

        // 检查限额
        if (amount.compareTo(fromAccount.getSingleLimit()) > 0) {
            throw new ApiException("超过单笔转账限额");
        }

        // 执行转账
        fromAccount.setBalance(fromAccount.getBalance().subtract(amount));
        toAccount.setBalance(toAccount.getBalance().add(amount));

        accountMapper.updateById(fromAccount);
        accountMapper.updateById(toAccount);

        // 生成基础交易编号（取UUID前20个字符）
        String baseTxnNo = UUID.randomUUID().toString().replace("-", "").substring(0, 20);
        Date now = new Date();

        // 转出记录
        Transaction fromTxn = new Transaction();
        fromTxn.setTxnNo(baseTxnNo + "O");  // O代表OUT
        fromTxn.setUserId(fromUserId);
        fromTxn.setAccountId(fromAccount.getAccountId());
        fromTxn.setType(TransactionType.TRANSFER.getCode());
        fromTxn.setAmount(amount.negate());
        fromTxn.setBalance(fromAccount.getBalance());
        fromTxn.setCounterparty(toUserId.toString());
        fromTxn.setStatus(1);
        fromTxn.setRemark("转账给用户" + toUserId);
        fromTxn.setCreatedAt(now);
        fromTxn.setUpdatedAt(now);
        transactionMapper.insert(fromTxn);

        // 转入记录
        Transaction toTxn = new Transaction();
        toTxn.setTxnNo(baseTxnNo + "I");  // I代表IN
        toTxn.setUserId(toUserId);
        toTxn.setAccountId(toAccount.getAccountId());
        toTxn.setType(TransactionType.RECEIVE.getCode());
        toTxn.setAmount(amount);
        toTxn.setBalance(toAccount.getBalance());
        toTxn.setCounterparty(fromUserId.toString());
        toTxn.setStatus(1);
        toTxn.setRemark("收款自用户" + fromUserId);
        toTxn.setCreatedAt(now);
        toTxn.setUpdatedAt(now);
        transactionMapper.insert(toTxn);
    }
}