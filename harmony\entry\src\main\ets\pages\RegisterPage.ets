import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { UserApi } from '../api/UserApi';
import { UserRegisterRequest } from '../common/types/index';

@Entry
@Component
struct RegisterPage {
  @State phone: string = '';
  @State password: string = '';
  @State confirmPassword: string = '';
  @State realName: string = '';
  @State idCard: string = '';
  @State isLoading: boolean = false;

  build() {
    Column() {
      // 顶部导航
      Row() {
        Button() {
          Text('←')
            .fontSize(24)
            .fontColor('#333333')
        }
        .width(40)
        .height(40)
        .borderRadius(20)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          router.back();
        })

        Text('用户注册')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        // 占位，保持标题居中
        Row().width(40).height(40)
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })

      Scroll() {
        Column() {
          // 注册表单
          Column() {
            // 手机号
            this.InputField('手机号', '请输入手机号', this.phone, (value: string) => {
              this.phone = value;
            }, InputType.PhoneNumber, 11)

            // 真实姓名
            this.InputField('真实姓名', '请输入真实姓名', this.realName, (value: string) => {
              this.realName = value;
            })

            // 身份证号
            this.InputField('身份证号', '请输入身份证号', this.idCard, (value: string) => {
              this.idCard = value;
            }, InputType.Normal, 18)

            // 密码
            this.PasswordField('登录密码', '请输入6-20位密码', this.password, (value: string) => {
              this.password = value;
            })

            // 确认密码
            this.PasswordField('确认密码', '请再次输入密码', this.confirmPassword, (value: string) => {
              this.confirmPassword = value;
            })

            // 注册按钮
            Button('注册')
              .width('100%')
              .height(48)
              .fontSize(16)
              .fontColor(Color.White)
              .backgroundColor('#1976D2')
              .borderRadius(8)
              .margin({ top: 30 })
              .enabled(!this.isLoading && this.isFormValid())
              .opacity((!this.isLoading && this.isFormValid()) ? 1 : 0.5)
              .onClick(() => {
                this.handleRegister();
              })

            // 登录链接
            Row() {
              Text('已有账号？')
                .fontSize(14)
                .fontColor('#666666')

              Text('立即登录')
                .fontSize(14)
                .fontColor('#1976D2')
                .onClick(() => {
                  router.back();
                })
            }
            .margin({ top: 20 })
          }
          .width('100%')
          .padding({ left: 24, right: 24, top: 20, bottom: 40 })
        }
      }
      .layoutWeight(1)
      .scrollable(ScrollDirection.Vertical)
      .scrollBar(BarState.Off)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#FFFFFF')
  }

  @Builder
  InputField(
    label: string,
    placeholder: string,
    value: string,
    onChange: (value: string) => void,
    inputType: InputType = InputType.Normal,
    maxLength?: number
  ) {
    Column() {
      Text(label)
        .fontSize(14)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })

      TextInput({ placeholder: placeholder })
        .type(inputType)
        .maxLength(maxLength)
        .fontSize(16)
        .height(48)
        .borderRadius(8)
        .backgroundColor('#F8F9FA')
        .border({ width: 1, color: '#E0E0E0' })
        .onChange(onChange)
    }
    .alignItems(HorizontalAlign.Start)
    .margin({ bottom: 20 })
  }

  @Builder
  PasswordField(
    label: string,
    placeholder: string,
    value: string,
    onChange: (value: string) => void
  ) {
    Column() {
      Text(label)
        .fontSize(14)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })

      TextInput({ placeholder: placeholder })
        .type(InputType.Password)
        .fontSize(16)
        .height(48)
        .borderRadius(8)
        .backgroundColor('#F8F9FA')
        .border({ width: 1, color: '#E0E0E0' })
        .showPasswordIcon(true)
        .onChange(onChange)
    }
    .alignItems(HorizontalAlign.Start)
    .margin({ bottom: 20 })
  }

  /**
   * 表单验证
   */
  isFormValid(): boolean {
    return this.phone.length === 11 &&
           this.realName.length >= 2 &&
           this.idCard.length === 18 &&
           this.password.length >= 6 &&
           this.password === this.confirmPassword;
  }

  /**
   * 处理注册
   */
  async handleRegister() {
    if (this.isLoading) return;

    // 表单验证
    if (!this.validateForm()) return;

    this.isLoading = true;

    try {
      const registerData: UserRegisterRequest = {
        phone: this.phone,
        password: this.password,
        realName: this.realName,
        idCard: this.idCard
      };

      await UserApi.register(registerData);
      
      promptAction.showToast({ message: '注册成功，请登录' });
      
      // 返回登录页
      router.back();

    } catch (error) {
      console.error('注册失败:', error);
      promptAction.showToast({ message: '注册失败，请重试' });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 表单验证
   */
  validateForm(): boolean {
    if (this.phone.length !== 11) {
      promptAction.showToast({ message: '请输入正确的手机号' });
      return false;
    }

    if (this.realName.length < 2) {
      promptAction.showToast({ message: '请输入真实姓名' });
      return false;
    }

    if (this.idCard.length !== 18) {
      promptAction.showToast({ message: '请输入正确的身份证号' });
      return false;
    }

    if (this.password.length < 6) {
      promptAction.showToast({ message: '密码长度不能少于6位' });
      return false;
    }

    if (this.password !== this.confirmPassword) {
      promptAction.showToast({ message: '两次输入的密码不一致' });
      return false;
    }

    return true;
  }
}
