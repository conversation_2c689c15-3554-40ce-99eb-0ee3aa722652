import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { BankCard, BankCardType, BankCardStatus, SpringBootBankCardResponse } from '../common/types/index';
import { httpClient } from '../common/http/HttpClient';
import { storageManager } from '../common/storage/StorageManager';

@Entry
@Component
struct PaymentPage {
  @State selectedCard: BankCard | null = null;
  @State bankCards: BankCard[] = [];
  @State payPassword: string = '';
  @State amount: string = '';
  @State description: string = '';
  @State isLoading: boolean = false;
  @State showCardSelector: boolean = false;
  @State showPasswordInput: boolean = false;
  @State paymentMethod: string = 'wallet'; // 'wallet' | 'bankcard'
  @State paymentChannel: string = 'merchant'; // 'merchant' | 'qr' | 'nfc'
  @State walletBalance: number = 0;
  @State merchantName: string = '';
  @State showPaymentDialog: boolean = false;

  aboutToAppear() {
    // 检查路由参数
    const params = router.getParams() as Record<string, Object>;
    if (params?.selectedCard) {
      this.selectedCard = params.selectedCard as BankCard;
    }
    if (params?.amount) {
      this.amount = params.amount as string;
    }
    if (params?.description) {
      this.description = params.description as string;
    }
    
    this.loadBankCards();
  }

  async loadBankCards() {
    try {
      // 从本地存储获取当前用户ID
      const cachedUserInfo = await storageManager.getUserInfo();
      if (!cachedUserInfo || !cachedUserInfo.userId) {
        console.error('无法获取用户ID');
        return;
      }

      // 调用SpringBoot3后端API获取银行卡列表
      const response = await httpClient.get<SpringBootBankCardResponse[]>(`/bank-card/user/${cachedUserInfo.userId}`);
      const bankCardList: SpringBootBankCardResponse[] = response.data;

      // 转换为本地格式，只显示已绑定的银行卡
      this.bankCards = this.convertSpringBootBankCardsToLocal(bankCardList)
        .filter(card => card.isBound === BankCardStatus.BOUND);
      
      // 如果没有选中的银行卡，默认选择第一张或默认卡
      if (!this.selectedCard && this.bankCards.length > 0) {
        this.selectedCard = this.bankCards.find(card => card.isDefault) || this.bankCards[0];
      }
      
      console.log('银行卡列表加载完成，数量:', this.bankCards.length);
    } catch (error) {
      console.error('加载银行卡列表失败:', error);
    }
  }

  build() {
    Column() {
      // 顶部导航
      Row() {
        Button() {
          Image($r('app.media.ic_back'))
            .width(24)
            .height(24)
            .fillColor('#333333')
        }
        .width(40)
        .height(40)
        .borderRadius(20)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          router.back();
        })

        Text('支付')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(40) // 占位
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })

      Scroll() {
        Column() {
          // 支付途径选择
          this.PaymentChannelSection()

          // 支付方式选择
          this.PaymentMethodSection()

          // 支付金额
          this.PaymentAmountSection()

          // 支付密码
          this.PaymentPasswordSection()

          // 确认支付按钮
          this.ConfirmPaymentButton()
        }
        .padding({ left: 16, right: 16, top: 16, bottom: 20 })
      }
      .layoutWeight(1)
      .scrollable(ScrollDirection.Vertical)
      .scrollBar(BarState.Off)

      // 银行卡选择器弹窗
      if (this.showCardSelector) {
        this.BankCardSelectorDialog()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  BankCardSection() {
    Column() {
      Text('银行卡信息')
        .fontSize(16)
        .fontColor('#333333')
        .fontWeight(FontWeight.Medium)
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })

      if (this.selectedCard) {
        // 显示选中的银行卡
        Column() {
          Row() {
            // 银行卡缩略图
            Column() {
              Text(this.selectedCard.bankName.substring(0, 2))
                .fontSize(16)
                .fontColor('#FFFFFF')
                .fontWeight(FontWeight.Bold)
            }
            .width(48)
            .height(32)
            .borderRadius(4)
            .justifyContent(FlexAlign.Center)
            .linearGradient({
              direction: GradientDirection.Right,
              colors: this.getBankCardGradient(this.selectedCard.bankName)
            })
            .margin({ right: 12 })

            Column() {
              Text(this.selectedCard.bankName)
                .fontSize(16)
                .fontColor('#333333')
                .fontWeight(FontWeight.Medium)
                .alignSelf(ItemAlign.Start)

              Text(`${this.getCardTypeText(this.selectedCard.cardType)} • ${this.formatCardNumber(this.selectedCard.cardNo)}`)
                .fontSize(14)
                .fontColor('#666666')
                .margin({ top: 2 })
                .alignSelf(ItemAlign.Start)
            }
            .alignItems(HorizontalAlign.Start)
            .layoutWeight(1)

            if (this.bankCards.length > 1) {
              Text('更换')
                .fontSize(14)
                .fontColor('#1976D2')
                .onClick(() => {
                  this.showCardSelector = true;
                })
            }
          }
          .width('100%')
          .padding(16)
          .backgroundColor('#FFFFFF')
          .borderRadius(8)
          .border({ width: 1, color: '#E0E0E0' })
          .onClick(() => {
            if (this.bankCards.length > 1) {
              this.showCardSelector = true;
            }
          })
        }
      } else {
        // 选择银行卡
        Row() {
          Text('请选择银行卡')
            .fontSize(16)
            .fontColor('#999999')
            .layoutWeight(1)

          Image($r('app.media.ic_arrow_right'))
            .width(16)
            .height(16)
            .fillColor('#999999')
        }
        .width('100%')
        .height(48)
        .padding({ left: 12, right: 12 })
        .backgroundColor('#FFFFFF')
        .borderRadius(8)
        .border({ width: 1, color: '#E0E0E0' })
        .onClick(() => {
          this.showCardSelector = true;
        })
      }
    }
    .alignItems(HorizontalAlign.Start)
    .margin({ bottom: 24 })
  }

  @Builder
  PaymentAmountSection() {
    Column() {
      Text('支付金额')
        .fontSize(16)
        .fontColor('#333333')
        .fontWeight(FontWeight.Medium)
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })

      Row() {
        Text('¥')
          .fontSize(24)
          .fontColor('#333333')
          .fontWeight(FontWeight.Bold)
          .margin({ right: 8 })

        TextInput({ 
          placeholder: '0.00',
          text: this.amount
        })
          .type(InputType.Number)
          .fontSize(24)
          .fontWeight(FontWeight.Bold)
          .backgroundColor(Color.Transparent)
          .border({ width: 0 })
          .layoutWeight(1)
          .onChange((value: string) => {
            this.amount = value;
          })
      }
      .width('100%')
      .padding(16)
      .backgroundColor('#FFFFFF')
      .borderRadius(8)
      .border({ width: 1, color: '#E0E0E0' })
    }
    .alignItems(HorizontalAlign.Start)
    .margin({ bottom: 24 })
  }

  @Builder
  PaymentPasswordSection() {
    Column() {
      Text('支付密码')
        .fontSize(16)
        .fontColor('#333333')
        .fontWeight(FontWeight.Medium)
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })

      Row() {
        TextInput({ 
          placeholder: '请输入6位支付密码'
        })
          .type(InputType.Password)
          .maxLength(6)
          .fontSize(16)
          .backgroundColor(Color.Transparent)
          .border({ width: 0 })
          .layoutWeight(1)
          .onChange((value: string) => {
            this.payPassword = value;
          })

        Button() {
          Image($r('app.media.ic_eye_off'))
            .width(20)
            .height(20)
            .fillColor('#999999')
        }
        .width(40)
        .height(40)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          this.showPasswordInput = !this.showPasswordInput;
        })
      }
      .width('100%')
      .padding(16)
      .backgroundColor('#FFFFFF')
      .borderRadius(8)
      .border({ width: 1, color: '#E0E0E0' })
    }
    .alignItems(HorizontalAlign.Start)
    .margin({ bottom: 24 })
  }

  @Builder
  ConfirmPaymentButton() {
    Button('确认支付')
      .fontSize(16)
      .fontColor('#FFFFFF')
      .backgroundColor(this.canPay() ? '#1976D2' : '#CCCCCC')
      .borderRadius(8)
      .width('100%')
      .height(48)
      .enabled(this.canPay())
      .onClick(() => {
        this.confirmPayment();
      })
  }

  @Builder
  BankCardSelectorDialog() {
    Stack({ alignContent: Alignment.Bottom }) {
      // 遮罩层
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0,0,0,0.5)')
        .onClick(() => {
          this.showCardSelector = false;
        })

      // 银行卡选择面板
      Column() {
        // 顶部标题栏
        Row() {
          Button('取消')
            .fontSize(16)
            .fontColor('#666666')
            .backgroundColor(Color.Transparent)
            .onClick(() => {
              this.showCardSelector = false;
            })

          Text('选择银行卡')
            .fontSize(18)
            .fontColor('#333333')
            .fontWeight(FontWeight.Medium)
            .layoutWeight(1)
            .textAlign(TextAlign.Center)

          Text('')
            .width(60) // 占位
        }
        .width('100%')
        .height(56)
        .padding({ left: 16, right: 16 })
        .border({
          width: { bottom: 1 },
          color: '#E0E0E0'
        })

        // 银行卡列表
        if (this.bankCards.length > 0) {
          List() {
            ForEach(this.bankCards, (card: BankCard) => {
              ListItem() {
                this.BankCardItem(card)
              }
            })
          }
          .height(300)
          .scrollBar(BarState.Off)
          .padding({ left: 16, right: 16, top: 16, bottom: 16 })
        } else {
          Column() {
            Text('暂无可用银行卡')
              .fontSize(16)
              .fontColor('#999999')
              .margin({ top: 40, bottom: 40 })
          }
          .width('100%')
          .justifyContent(FlexAlign.Center)
        }
      }
      .width('100%')
      .backgroundColor('#FFFFFF')
      .borderRadius({ topLeft: 16, topRight: 16 })
      .animation({
        duration: 300,
        curve: Curve.EaseInOut
      })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
    .zIndex(1000)
  }

  @Builder
  BankCardItem(card: BankCard) {
    Row() {
      // 银行卡缩略图
      Column() {
        Text(card.bankName.substring(0, 2))
          .fontSize(16)
          .fontColor('#FFFFFF')
          .fontWeight(FontWeight.Bold)
      }
      .width(48)
      .height(32)
      .borderRadius(4)
      .justifyContent(FlexAlign.Center)
      .linearGradient({
        direction: GradientDirection.Right,
        colors: this.getBankCardGradient(card.bankName)
      })
      .margin({ right: 12 })

      // 银行卡信息
      Column() {
        Row() {
          Text(card.bankName)
            .fontSize(16)
            .fontColor('#333333')
            .fontWeight(FontWeight.Medium)
            .layoutWeight(1)

          if (card.isDefault) {
            Text('默认')
              .fontSize(10)
              .fontColor('#FFFFFF')
              .backgroundColor('#FF9800')
              .borderRadius(8)
              .padding({ left: 6, right: 6, top: 2, bottom: 2 })
          }
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)

        Text(`${this.getCardTypeText(card.cardType)} • ${this.formatCardNumber(card.cardNo)}`)
          .fontSize(14)
          .fontColor('#666666')
          .margin({ top: 4 })
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)

      // 选择状态
      if (this.selectedCard?.cardId === card.cardId) {
        Text('✓')
          .fontSize(20)
          .fontColor('#4CAF50')
          .fontWeight(FontWeight.Bold)
      }
    }
    .width('100%')
    .padding(16)
    .backgroundColor(this.selectedCard?.cardId === card.cardId ? '#E8F5E8' : '#FFFFFF')
    .borderRadius(8)
    .border({
      width: this.selectedCard?.cardId === card.cardId ? 2 : 1,
      color: this.selectedCard?.cardId === card.cardId ? '#4CAF50' : '#E0E0E0'
    })
    .margin({ bottom: 12 })
    .onClick(() => {
      this.selectedCard = card;
      this.showCardSelector = false;
    })
  }

  // 辅助方法
  private canPay(): boolean {
    return this.selectedCard !== null && 
           this.amount !== '' && 
           parseFloat(this.amount) > 0 && 
           this.payPassword.length === 6 &&
           !this.isLoading;
  }

  private async confirmPayment() {
    if (!this.canPay()) return;

    try {
      this.isLoading = true;

      // 获取当前用户信息
      const cachedUserInfo = await storageManager.getUserInfo();
      if (!cachedUserInfo || !cachedUserInfo.userId) {
        promptAction.showToast({ message: '用户信息获取失败' });
        return;
      }

      // 构建支付请求
      const paymentRequest = {
        userId: cachedUserInfo.userId,
        amount: parseFloat(this.amount),
        paymentMethod: this.paymentMethod,
        paymentChannel: this.paymentChannel,
        merchantName: this.merchantName || '商户支付',
        description: `${this.paymentChannel}支付 - ${this.merchantName || '商户'}`,
        payPassword: this.payPassword
      };

      // 如果是银行卡支付，添加银行卡信息
      if (this.paymentMethod === 'card' && this.selectedCard) {
        paymentRequest['cardId'] = this.selectedCard.cardId;
        paymentRequest['cardNo'] = this.selectedCard.cardNo;
      }

      // 调用SpringBoot3后端支付API
      const response = await httpClient.post('/payment/pay', paymentRequest);

      if (response.data) {
        promptAction.showToast({ message: '支付成功' });

        // 支付成功后，可以跳转到支付结果页面或返回
        setTimeout(() => {
          router.back();
        }, 1500);
      } else {
        promptAction.showToast({ message: '支付失败，请重试' });
      }
    } catch (error) {
      console.error('支付失败:', error);
      let errorMessage = '支付失败，请重试';
      if (error instanceof Error) {
        errorMessage = `支付失败: ${error.message}`;
      }
      promptAction.showToast({ message: errorMessage });
    } finally {
      this.isLoading = false;
    }
  }

  private convertSpringBootBankCardsToLocal(bankCardList: SpringBootBankCardResponse[]): BankCard[] {
    return bankCardList.map((card: SpringBootBankCardResponse): BankCard => ({
      cardId: card.cardId || 0,
      userId: card.userId || 0,
      cardNo: card.cardNumber || '',
      cardType: this.mapBankCardType(card.cardType),
      bankName: card.bankName || '',
      holderName: card.cardHolder || '',
      isBound: card.status === 1 ? BankCardStatus.BOUND : BankCardStatus.UNBOUND,
      createTime: card.createdAt || card.createTime || '',
      updateTime: card.updatedAt || card.updateTime || '',
      maskedCardNo: card.cardNumber ? this.maskCardNo(card.cardNumber) : undefined,
      isDefault: card.isDefault === 1 || card.isDefault === true
    }));
  }

  private mapBankCardType(cardType: string | number | undefined): BankCardType {
    if (typeof cardType === 'number') {
      return cardType === 2 ? BankCardType.CREDIT : BankCardType.DEBIT;
    } else if (typeof cardType === 'string') {
      if (cardType === '信用卡' || cardType === 'CREDIT') {
        return BankCardType.CREDIT;
      }
    }
    return BankCardType.DEBIT;
  }

  private maskCardNo(cardNo: string): string {
    if (!cardNo || cardNo.length < 8) return cardNo;
    return cardNo.replace(/(\d{4})\d*(\d{4})/, '$1****$2');
  }

  private getCardTypeText(cardType: BankCardType): string {
    switch (cardType) {
      case BankCardType.CREDIT:
        return '信用卡';
      case BankCardType.DEBIT:
        return '储蓄卡';
      default:
        return '储蓄卡';
    }
  }

  private formatCardNumber(cardNo: string): string {
    if (!cardNo) return '';
    return `**** **** **** ${cardNo.slice(-4)}`;
  }

  private getBankCardGradient(bankName: string): Array<[string, number]> {
    const gradients: Record<string, Array<[string, number]>> = {
      '中国工商银行': [['#C41E3A', 0], ['#8B0000', 1]],
      '中国建设银行': [['#003DA5', 0], ['#001F5C', 1]],
      '中国农业银行': [['#00A651', 0], ['#006B35', 1]],
      '中国银行': [['#B8860B', 0], ['#8B6914', 1]],
      '招商银行': [['#DC143C', 0], ['#8B0000', 1]],
      '交通银行': [['#0066CC', 0], ['#003D7A', 1]],
      '中信银行': [['#FF6B35', 0], ['#CC4125', 1]],
      '光大银行': [['#9932CC', 0], ['#6A1B9A', 1]],
      '华夏银行': [['#FF1744', 0], ['#C62828', 1]],
      '民生银行': [['#00BCD4', 0], ['#0097A7', 1]],
      '广发银行': [['#FF5722', 0], ['#D84315', 1]],
      '平安银行': [['#FF9800', 0], ['#F57C00', 1]]
    };

    return gradients[bankName] || [['#1976D2', 0], ['#0D47A1', 1]];
  }

  @Builder
  PaymentChannelSection() {
    Column() {
      Text('选择支付途径')
        .fontSize(18)
        .fontColor('#333333')
        .fontWeight(FontWeight.Bold)
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })

      Text('请选择您的付款方式')
        .fontSize(14)
        .fontColor('#666666')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 20 })

      // 支付途径选项
      Column() {
        this.ChannelOptionCard('merchant', '商户付款', '🏪', '线下商户扫码收款')
        this.ChannelOptionCard('qr', '扫码付款', '📱', '扫描二维码完成支付')
        this.ChannelOptionCard('nfc', 'NFC支付', '📡', '近场通信快速支付')
      }
      .width('100%')
    }
    .width('100%')
    .padding(20)
    .margin({ bottom: 16 })
    .borderRadius(12)
    .backgroundColor('#FFFFFF')
    .shadow({
      radius: 4,
      color: 'rgba(0,0,0,0.1)',
      offsetX: 0,
      offsetY: 2
    })
  }

  @Builder
  ChannelOptionCard(channel: string, title: string, icon: string, description: string) {
    Row() {
      // 图标区域
      Column() {
        Text(icon)
          .fontSize(32)
          .margin({ bottom: 4 })
      }
      .width(60)
      .height(60)
      .justifyContent(FlexAlign.Center)
      .borderRadius(30)
      .backgroundColor(this.paymentChannel === channel ? '#1976D2' : '#F0F0F0')
      .margin({ right: 16 })

      // 内容区域
      Column() {
        Text(title)
          .fontSize(16)
          .fontColor('#333333')
          .fontWeight(FontWeight.Bold)
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 4 })

        Text(description)
          .fontSize(12)
          .fontColor('#666666')
          .alignSelf(ItemAlign.Start)
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)

      // 选择指示器
      if (this.paymentChannel === channel) {
        Text('✓')
          .fontSize(20)
          .fontColor('#1976D2')
          .fontWeight(FontWeight.Bold)
      }
    }
    .width('100%')
    .height(80)
    .padding(16)
    .margin({ bottom: 12 })
    .borderRadius(12)
    .backgroundColor(this.paymentChannel === channel ? '#E3F2FD' : '#F8F9FA')
    .border({
      width: 2,
      color: this.paymentChannel === channel ? '#1976D2' : 'transparent'
    })
    .onClick(() => {
      this.paymentChannel = channel;
      if (channel === 'merchant') {
        this.merchantName = '示例商户';
        promptAction.showToast({ message: '已选择商户付款' });
      } else if (channel === 'qr') {
        this.merchantName = '扫码商户';
        this.handleQRPayment();
      } else if (channel === 'nfc') {
        this.merchantName = 'NFC商户';
        this.handleNFCPayment();
      }
    })
  }

  @Builder
  PaymentMethodSection() {
    Column() {
      Text('选择支付方式')
        .fontSize(18)
        .fontColor('#333333')
        .fontWeight(FontWeight.Bold)
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 8 })

      Text('请选择您的资金来源')
        .fontSize(14)
        .fontColor('#666666')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 20 })

      // 钱包支付选项
      this.PaymentMethodCard('wallet', '钱包余额', '💰', `可用余额：¥${this.walletBalance.toFixed(2)}`)

      // 银行卡支付选项
      this.PaymentMethodCard('bankcard', '银行卡支付', '💳', this.getBankCardDescription())
    }
    .width('100%')
    .padding(20)
    .margin({ bottom: 16 })
    .borderRadius(12)
    .backgroundColor('#FFFFFF')
    .shadow({
      radius: 4,
      color: 'rgba(0,0,0,0.1)',
      offsetX: 0,
      offsetY: 2
    })
  }

  @Builder
  PaymentMethodCard(method: string, title: string, icon: string, description: string) {
    Row() {
      // 选择指示器
      Radio({ value: method, group: 'paymentMethod' })
        .checked(this.paymentMethod === method)
        .onChange((isChecked: boolean) => {
          if (isChecked) {
            this.paymentMethod = method;
          }
        })
        .margin({ right: 16 })

      // 图标区域
      Column() {
        Text(icon)
          .fontSize(28)
      }
      .width(50)
      .height(50)
      .justifyContent(FlexAlign.Center)
      .borderRadius(25)
      .backgroundColor(this.paymentMethod === method ? '#1976D2' : '#F0F0F0')
      .margin({ right: 16 })

      // 内容区域
      Column() {
        Text(title)
          .fontSize(16)
          .fontColor('#333333')
          .fontWeight(FontWeight.Bold)
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 4 })

        Text(description)
          .fontSize(12)
          .fontColor('#666666')
          .alignSelf(ItemAlign.Start)
          .maxLines(1)
          .textOverflow({ overflow: TextOverflow.Ellipsis })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)

      // 操作按钮
      if (method === 'bankcard') {
        Button('选择')
          .fontSize(12)
          .fontColor('#1976D2')
          .backgroundColor('#E3F2FD')
          .borderRadius(6)
          .padding({ left: 12, right: 12, top: 6, bottom: 6 })
          .onClick(() => {
            this.showCardSelector = true;
          })
      }
    }
    .width('100%')
    .height(80)
    .padding(16)
    .margin({ bottom: 12 })
    .borderRadius(12)
    .backgroundColor(this.paymentMethod === method ? '#E3F2FD' : '#F8F9FA')
    .border({
      width: 2,
      color: this.paymentMethod === method ? '#1976D2' : 'transparent'
    })
    .onClick(() => {
      this.paymentMethod = method;
    })
  }

      // 银行卡支付选项
      Row() {
        Radio({ value: 'bankcard', group: 'paymentMethod' })
          .checked(this.paymentMethod === 'bankcard')
          .onChange((isChecked: boolean) => {
            if (isChecked) {
              this.paymentMethod = 'bankcard';
            }
          })

        Column() {
          Text('银行卡支付')
            .fontSize(16)
            .fontColor('#333333')
            .fontWeight(FontWeight.Medium)
            .alignSelf(ItemAlign.Start)

          if (this.selectedCard) {
            Text(`${this.selectedCard.bankName} **** ${this.selectedCard.cardNo.slice(-4)}`)
              .fontSize(14)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
              .margin({ top: 4 })
          } else {
            Text('请选择银行卡')
              .fontSize(14)
              .fontColor('#999999')
              .alignSelf(ItemAlign.Start)
              .margin({ top: 4 })
          }
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Start)
        .margin({ left: 12 })

        Text('💳')
          .fontSize(24)
      }
      .width('100%')
      .padding(12)
      .borderRadius(8)
      .backgroundColor(this.paymentMethod === 'bankcard' ? '#E3F2FD' : '#F8F9FA')
      .border({
        width: 1,
        color: this.paymentMethod === 'bankcard' ? '#1976D2' : '#E0E0E0'
      })
      .onClick(() => {
        this.paymentMethod = 'bankcard';
        if (!this.selectedCard && this.bankCards.length > 0) {
          this.showCardSelector = true;
        }
      })
    }
    .width('100%')
    .padding(20)
    .margin({ bottom: 16 })
    .borderRadius(12)
    .backgroundColor('#FFFFFF')
  }

  /**
   * 处理扫码支付
   */
  private handleQRPayment() {
    try {
      // 模拟扫码功能
      promptAction.showToast({ message: '正在启动扫码功能...' });

      // 这里可以调用相机API进行二维码扫描
      // 现在模拟扫码结果
      setTimeout(() => {
        this.merchantName = '扫码商户 - 星巴克咖啡';
        this.amount = '35.00';
        promptAction.showToast({ message: '扫码成功，已获取商户信息' });
      }, 1500);

    } catch (error) {
      console.error('扫码支付失败:', error);
      promptAction.showToast({ message: '扫码失败，请重试' });
    }
  }

  /**
   * 处理NFC支付
   */
  private handleNFCPayment() {
    try {
      // 模拟NFC功能
      promptAction.showToast({ message: '请将设备靠近NFC标签...' });

      // 这里可以调用NFC API
      // 现在模拟NFC读取结果
      setTimeout(() => {
        this.merchantName = 'NFC商户 - 便利店';
        this.amount = '12.50';
        promptAction.showToast({ message: 'NFC读取成功，已获取商户信息' });
      }, 2000);

    } catch (error) {
      console.error('NFC支付失败:', error);
      promptAction.showToast({ message: 'NFC读取失败，请重试' });
    }
  }

  /**
   * 获取支付途径显示文本
   */
  private getPaymentChannelText(channel: string): string {
    switch (channel) {
      case 'merchant': return '商户付款';
      case 'qr': return '扫码付款';
      case 'nfc': return 'NFC支付';
      default: return '未知途径';
    }
  }

  /**
   * 获取支付方式显示文本
   */
  private getPaymentMethodText(method: string): string {
    switch (method) {
      case 'wallet': return '钱包支付';
      case 'card': return '银行卡支付';
      default: return '未知方式';
    }
  }

  /**
   * 获取银行卡描述文本
   */
  getBankCardDescription(): string {
    if (this.selectedCard) {
      return `${this.selectedCard.bankName} **** ${this.selectedCard.cardNo.slice(-4)}`;
    } else if (this.bankCards.length > 0) {
      return `已绑定 ${this.bankCards.length} 张银行卡`;
    } else {
      return '请先添加银行卡';
    }
  }
}
