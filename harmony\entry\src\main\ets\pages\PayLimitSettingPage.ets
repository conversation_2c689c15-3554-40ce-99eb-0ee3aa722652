import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { UserApi } from '../api/UserApi';
import { storageManager, LocalUserInfo } from '../common/storage/StorageManager';
import { UpdatePayLimitRequest } from '../common/types/index';

interface LimitOption {
  value: number;
  label: string;
  description: string;
}

@Entry
@Component
struct PayLimitSettingPage {
  @State userInfo: LocalUserInfo | null = null;
  @State isUpdating: boolean = false;
  @State selectedLimit: number = 0;

  private limitOptions: LimitOption[] = [
    { value: 500, label: '¥500', description: '日常小额支付' },
    { value: 1000, label: '¥1,000', description: '一般消费支付' },
    { value: 2000, label: '¥2,000', description: '中等金额支付' },
    { value: 5000, label: '¥5,000', description: '大额消费支付' },
    { value: 10000, label: '¥10,000', description: '最高限额支付' }
  ];

  aboutToAppear() {
    this.loadUserInfo();
  }

  async loadUserInfo() {
    try {
      const cachedUserInfo = await storageManager.getUserInfo();
      if (cachedUserInfo) {
        this.userInfo = cachedUserInfo;
        this.selectedLimit = cachedUserInfo.payLimit || 0;
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button('返回')
          .fontSize(16)
          .fontColor('#1976D2')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            router.back();
          })

        Text('支付限额设置')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(60) // 占位，保持标题居中
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')

      Scroll() {
        Column() {
          // 当前限额显示
          Column() {
            Text('当前支付限额')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 12 })

            Text(`¥${(this.userInfo?.payLimit ?? 0).toFixed(2)}`)
              .fontSize(32)
              .fontColor('#1976D2')
              .fontWeight(FontWeight.Bold)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 })

            Text('单笔支付限额，保护您的资金安全')
              .fontSize(14)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
          }
          .width('100%')
          .padding(20)
          .borderRadius(12)
          .backgroundColor('#FFFFFF')
          .margin({ top: 16 })

          // 安全提示
          Column() {
            Text('安全提示')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 12 })

            Text('• 支付限额用于控制单笔转账和提现的最大金额')
              .fontSize(14)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 })

            Text('• 建议根据您的实际需求选择合适的限额')
              .fontSize(14)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 })

            Text('• 较低的限额可以更好地保护您的资金安全')
              .fontSize(14)
              .fontColor('#666666')
              .alignSelf(ItemAlign.Start)
          }
          .width('100%')
          .padding(20)
          .borderRadius(12)
          .backgroundColor('#FFF3E0')
          .margin({ top: 16 })

          // 限额选择
          Column() {
            Text('选择新的支付限额')
              .fontSize(16)
              .fontColor('#333333')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 20 })

            ForEach(this.limitOptions, (option: LimitOption, index: number) => {
              Row() {
                Column() {
                  Text(option.label)
                    .fontSize(18)
                    .fontColor('#333333')
                    .fontWeight(FontWeight.Medium)
                    .alignSelf(ItemAlign.Start)

                  Text(option.description)
                    .fontSize(14)
                    .fontColor('#666666')
                    .alignSelf(ItemAlign.Start)
                    .margin({ top: 4 })
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Start)

                if (this.selectedLimit === option.value) {
                  Text('✓')
                    .fontSize(20)
                    .fontColor('#1976D2')
                    .fontWeight(FontWeight.Bold)
                }
              }
              .width('100%')
              .height(70)
              .padding({ left: 16, right: 16 })
              .backgroundColor(this.selectedLimit === option.value ? '#E3F2FD' : '#F8F9FA')
              .borderRadius(8)
              .border({
                width: this.selectedLimit === option.value ? 2 : 1,
                color: this.selectedLimit === option.value ? '#1976D2' : '#E0E0E0'
              })
              .margin({ bottom: 12 })
              .onClick(() => {
                this.selectedLimit = option.value;
              })
            })

            // 确认按钮
            Button(this.isUpdating ? '设置中...' : '确认设置')
              .width('100%')
              .height(48)
              .fontSize(16)
              .fontColor(Color.White)
              .backgroundColor(this.canSubmit() && !this.isUpdating ? '#1976D2' : '#CCCCCC')
              .borderRadius(8)
              .margin({ top: 20 })
              .enabled(this.canSubmit() && !this.isUpdating)
              .onClick(() => {
                this.updatePayLimit();
              })
          }
          .width('100%')
          .padding(20)
          .borderRadius(12)
          .backgroundColor('#FFFFFF')
          .margin({ top: 16 })
        }
        .padding({ left: 16, right: 16, bottom: 20 })
      }
      .layoutWeight(1)
      .backgroundColor('#F5F5F5')
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  // 检查是否可以提交
  canSubmit(): boolean {
    return this.selectedLimit > 0 && this.selectedLimit !== (this.userInfo?.payLimit || 0);
  }

  // 更新支付限额
  async updatePayLimit() {
    if (!this.canSubmit() || this.isUpdating) return;

    this.isUpdating = true;

    try {
      const data: UpdatePayLimitRequest = {
        payLimit: this.selectedLimit
      };

      console.log('正在设置支付限额:', this.selectedLimit);
      await UserApi.updatePayLimit(data);
      console.log('API调用成功');

      // 更新本地用户信息
      if (this.userInfo) {
        this.userInfo.payLimit = this.selectedLimit;
        await storageManager.saveUserInfo(this.userInfo);
        console.log('本地数据已保存，新限额:', this.userInfo.payLimit);
      }

      promptAction.showToast({ message: `支付限额已设置为 ¥${this.selectedLimit.toFixed(2)}` });

      // 返回设置页面
      router.back();

    } catch (error) {
      console.error('设置支付限额失败:', error);
      promptAction.showToast({ message: '设置支付限额失败，请重试' });
    } finally {
      this.isUpdating = false;
    }
  }
}
