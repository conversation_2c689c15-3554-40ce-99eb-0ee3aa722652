<template>
  <div class="pay-password-manager">
    <el-card class="manager-card">
      <template #header>
        <div class="card-header">
          <h3>支付密码管理</h3>
          <el-tag :type="hasPayPassword ? 'success' : 'warning'">
            {{ hasPayPassword ? '已设置' : '未设置' }}
          </el-tag>
        </div>
      </template>

      <!-- 当前用户信息 -->
      <div class="user-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ currentUser?.userId }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ currentUser?.username || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ currentUser?.phone }}</el-descriptions-item>
          <el-descriptions-item label="支付密码状态">
            <el-tag :type="hasPayPassword ? 'success' : 'danger'">
              {{ hasPayPassword ? '已设置' : '未设置' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 支付密码操作 -->
      <div class="password-actions">
        <el-button 
          v-if="!hasPayPassword" 
          type="primary" 
          @click="showSetPasswordDialog = true"
          size="large"
        >
          设置支付密码
        </el-button>
        
        <div v-else class="action-buttons">
          <el-button type="warning" @click="showChangePasswordDialog = true">
            修改支付密码
          </el-button>
          <el-button type="info" @click="showResetPasswordDialog = true">
            重置支付密码
          </el-button>
          <el-button type="success" @click="testPayPassword">
            测试支付密码
          </el-button>
        </div>
      </div>

      <!-- 常用支付密码提示 -->
      <div class="password-tips">
        <el-alert
          title="支付密码提示"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p><strong>支付密码格式：</strong>必须是6位数字</p>
            <p><strong>常用默认密码：</strong></p>
            <ul>
              <li>123456 (最常用)</li>
              <li>000000</li>
              <li>111111</li>
              <li>888888</li>
              <li>666666</li>
            </ul>
            <p><strong>建议：</strong>如果忘记密码，可以尝试上述常用密码，或重置支付密码</p>
          </template>
        </el-alert>
      </div>
    </el-card>

    <!-- 设置支付密码对话框 -->
    <el-dialog v-model="showSetPasswordDialog" title="设置支付密码" width="400px">
      <el-form :model="setPasswordForm" :rules="passwordRules" ref="setPasswordFormRef">
        <el-form-item label="登录密码" prop="loginPassword">
          <el-input 
            v-model="setPasswordForm.loginPassword" 
            type="password" 
            placeholder="请输入登录密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="支付密码" prop="payPassword">
          <el-input 
            v-model="setPasswordForm.payPassword" 
            type="password" 
            placeholder="请输入6位数字支付密码"
            maxlength="6"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="setPasswordForm.confirmPassword" 
            type="password" 
            placeholder="请再次输入支付密码"
            maxlength="6"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showSetPasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSetPassword" :loading="loading">确定</el-button>
      </template>
    </el-dialog>

    <!-- 修改支付密码对话框 -->
    <el-dialog v-model="showChangePasswordDialog" title="修改支付密码" width="400px">
      <el-form :model="changePasswordForm" :rules="changePasswordRules" ref="changePasswordFormRef">
        <el-form-item label="原支付密码" prop="oldPassword">
          <el-input 
            v-model="changePasswordForm.oldPassword" 
            type="password" 
            placeholder="请输入原支付密码"
            maxlength="6"
            show-password
          />
        </el-form-item>
        <el-form-item label="新支付密码" prop="newPassword">
          <el-input 
            v-model="changePasswordForm.newPassword" 
            type="password" 
            placeholder="请输入新的6位数字支付密码"
            maxlength="6"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="changePasswordForm.confirmPassword" 
            type="password" 
            placeholder="请再次输入新支付密码"
            maxlength="6"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showChangePasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="handleChangePassword" :loading="loading">确定</el-button>
      </template>
    </el-dialog>

    <!-- 重置支付密码对话框 -->
    <el-dialog v-model="showResetPasswordDialog" title="重置支付密码" width="400px">
      <el-form :model="resetPasswordForm" :rules="resetPasswordRules" ref="resetPasswordFormRef">
        <el-form-item label="登录密码" prop="loginPassword">
          <el-input 
            v-model="resetPasswordForm.loginPassword" 
            type="password" 
            placeholder="请输入登录密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新支付密码" prop="newPassword">
          <el-input 
            v-model="resetPasswordForm.newPassword" 
            type="password" 
            placeholder="请输入新的6位数字支付密码"
            maxlength="6"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input 
            v-model="resetPasswordForm.confirmPassword" 
            type="password" 
            placeholder="请再次输入新支付密码"
            maxlength="6"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showResetPasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="handleResetPassword" :loading="loading">确定</el-button>
      </template>
    </el-dialog>

    <!-- 测试支付密码对话框 -->
    <el-dialog v-model="showTestPasswordDialog" title="测试支付密码" width="400px">
      <el-form :model="testPasswordForm" ref="testPasswordFormRef">
        <el-form-item label="支付密码">
          <el-input 
            v-model="testPasswordForm.password" 
            type="password" 
            placeholder="请输入支付密码"
            maxlength="6"
            show-password
            @keyup.enter="handleTestPassword"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showTestPasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="handleTestPassword" :loading="loading">测试</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getUserInfo } from '@/stores/user'
import payPasswordApi from '@/api/payPassword'

// 响应式数据
const loading = ref(false)
const currentUser = ref(null)
const hasPayPassword = ref(false)

// 对话框显示状态
const showSetPasswordDialog = ref(false)
const showChangePasswordDialog = ref(false)
const showResetPasswordDialog = ref(false)
const showTestPasswordDialog = ref(false)

// 表单数据
const setPasswordForm = ref({
  loginPassword: '',
  payPassword: '',
  confirmPassword: ''
})

const changePasswordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const resetPasswordForm = ref({
  loginPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const testPasswordForm = ref({
  password: ''
})

// 表单引用
const setPasswordFormRef = ref(null)
const changePasswordFormRef = ref(null)
const resetPasswordFormRef = ref(null)
const testPasswordFormRef = ref(null)

// 验证规则
const passwordRules = {
  loginPassword: [
    { required: true, message: '请输入登录密码', trigger: 'blur' }
  ],
  payPassword: [
    { required: true, message: '请输入支付密码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '支付密码必须是6位数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认支付密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== setPasswordForm.value.payPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const changePasswordRules = {
  oldPassword: [
    { required: true, message: '请输入原支付密码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '支付密码必须是6位数字', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新支付密码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '支付密码必须是6位数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新支付密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== changePasswordForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const resetPasswordRules = {
  loginPassword: [
    { required: true, message: '请输入登录密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新支付密码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '支付密码必须是6位数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新支付密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== resetPasswordForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 初始化
onMounted(() => {
  currentUser.value = getUserInfo()
  if (currentUser.value) {
    checkPayPasswordStatus()
  } else {
    ElMessage.error('请先登录')
  }
})

// 检查支付密码状态
const checkPayPasswordStatus = async () => {
  try {
    const response = await payPasswordApi.getPayPasswordStatus(currentUser.value.userId)

    if (response && response.code === 0) {
      hasPayPassword.value = response.data.hasDefaultCardPassword
    }
  } catch (error) {
    console.error('检查支付密码状态失败:', error)
  }
}

// 设置支付密码
const handleSetPassword = async () => {
  if (!setPasswordFormRef.value) return

  try {
    await setPasswordFormRef.value.validate()
    loading.value = true

    const response = await payPasswordApi.setDefaultCardPayPassword({
      userId: currentUser.value.userId,
      payPassword: setPasswordForm.value.payPassword,
      loginPassword: setPasswordForm.value.loginPassword
    })

    if (response && response.code === 0) {
      ElMessage.success('支付密码设置成功！')
      showSetPasswordDialog.value = false
      hasPayPassword.value = true
      resetForm('set')
    } else {
      ElMessage.error(response?.msg || '设置失败')
    }
  } catch (error) {
    ElMessage.error('设置失败')
    console.error('设置支付密码失败:', error)
  } finally {
    loading.value = false
  }
}

// 修改支付密码
const handleChangePassword = async () => {
  if (!changePasswordFormRef.value) return

  try {
    await changePasswordFormRef.value.validate()
    loading.value = true

    // 注意：这里需要指定银行卡ID，暂时使用默认银行卡
    const response = await payPasswordApi.changeCardPayPassword({
      userId: currentUser.value.userId,
      cardId: null, // 后端会自动获取默认银行卡
      oldPayPassword: changePasswordForm.value.oldPassword,
      newPayPassword: changePasswordForm.value.newPassword
    })

    if (response && response.code === 0) {
      ElMessage.success('支付密码修改成功！')
      showChangePasswordDialog.value = false
      resetForm('change')
    } else {
      ElMessage.error(response?.msg || '修改失败')
    }
  } catch (error) {
    ElMessage.error('修改失败')
    console.error('修改支付密码失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置支付密码
const handleResetPassword = async () => {
  if (!resetPasswordFormRef.value) return

  try {
    await resetPasswordFormRef.value.validate()
    loading.value = true

    const response = await payPasswordApi.resetCardPayPassword({
      userId: currentUser.value.userId,
      cardId: null, // 后端会自动获取默认银行卡
      loginPassword: resetPasswordForm.value.loginPassword,
      newPayPassword: resetPasswordForm.value.newPassword
    })

    if (response && response.code === 0) {
      ElMessage.success('支付密码重置成功！')
      showResetPasswordDialog.value = false
      resetForm('reset')
    } else {
      ElMessage.error(response?.msg || '重置失败')
    }
  } catch (error) {
    ElMessage.error('重置失败')
    console.error('重置支付密码失败:', error)
  } finally {
    loading.value = false
  }
}

// 测试支付密码
const testPayPassword = () => {
  testPasswordForm.value.password = ''
  showTestPasswordDialog.value = true
}

const handleTestPassword = async () => {
  if (!testPasswordForm.value.password) {
    ElMessage.warning('请输入支付密码')
    return
  }

  loading.value = true

  try {
    const response = await payPasswordApi.testPayPassword({
      userId: currentUser.value.userId,
      payPassword: testPasswordForm.value.password
    })

    if (response && response.code === 0) {
      const result = response.data
      if (result.passwordValid) {
        ElMessage.success('支付密码正确！')
        showTestPasswordDialog.value = false
        testPasswordForm.value.password = ''
      } else {
        ElMessage.error('支付密码错误')
      }

      // 显示详细信息
      console.log('测试结果:', result)
    } else {
      ElMessage.error(response?.msg || '测试失败')
    }
  } catch (error) {
    ElMessage.error('测试失败')
    console.error('测试支付密码失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = (type) => {
  switch (type) {
    case 'set':
      setPasswordForm.value = {
        loginPassword: '',
        payPassword: '',
        confirmPassword: ''
      }
      break
    case 'change':
      changePasswordForm.value = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      break
    case 'reset':
      resetPasswordForm.value = {
        loginPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      break
  }
}
</script>

<style scoped>
.pay-password-manager {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.manager-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #2c3e50;
}

.user-info {
  margin: 20px 0;
}

.password-actions {
  margin: 30px 0;
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.password-tips {
  margin-top: 30px;
}

.password-tips ul {
  margin: 10px 0;
  padding-left: 20px;
}

.password-tips li {
  margin: 5px 0;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
