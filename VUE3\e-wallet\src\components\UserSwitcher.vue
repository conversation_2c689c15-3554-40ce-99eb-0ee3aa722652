<template>
  <div class="user-switcher">
    <el-card class="switcher-card">
      <template #header>
        <div class="card-header">
          <span>用户切换 (权限测试)</span>
          <el-tag :type="currentUser ? 'success' : 'danger'" size="small">
            {{ currentUser ? '已登录' : '未登录' }}
          </el-tag>
        </div>
      </template>
      
      <div class="user-info" v-if="currentUser">
        <p><strong>当前用户:</strong> {{ currentUser.username }}</p>
        <p><strong>用户ID:</strong> {{ currentUser.userId }}</p>
        <p><strong>手机号:</strong> {{ currentUser.phone }}</p>
      </div>
      
      <div class="user-actions">
        <el-select 
          v-model="selectedUserId" 
          placeholder="选择用户身份"
          style="width: 200px; margin-right: 10px;"
        >
          <el-option
            v-for="user in mockUsers"
            :key="user.userId"
            :label="`${user.username} (ID: ${user.userId})`"
            :value="user.userId"
          />
        </el-select>
        
        <el-button 
          type="primary" 
          @click="switchUser"
          :disabled="!selectedUserId"
        >
          切换用户
        </el-button>
        
        <el-button 
          type="warning" 
          @click="logout"
          v-if="currentUser"
        >
          退出登录
        </el-button>
      </div>
      
      <div class="permission-info">
        <el-alert
          title="权限说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>• 每个用户只能看到自己的银行卡和交易记录</p>
            <p>• 切换用户后数据会自动更新</p>
            <p>• 这是权限控制的演示功能</p>
          </template>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { setUserInfo, getUserInfo, clearUserInfo } from '@/stores/user'

// 响应式数据
const selectedUserId = ref(null)
const currentUser = ref(null)

// 模拟用户数据
const mockUsers = [
  {
    userId: 1,
    username: '张三',
    phone: '13800138001',
    email: '<EMAIL>'
  },
  {
    userId: 2,
    username: '李四',
    phone: '13800138002',
    email: '<EMAIL>'
  },
  {
    userId: 3,
    username: '王五',
    phone: '13800138003',
    email: '<EMAIL>'
  },
  {
    userId: 4,
    username: '赵六',
    phone: '13800138004',
    email: '<EMAIL>'
  }
]

// 计算属性
const isLoggedIn = computed(() => !!currentUser.value)

// 初始化
onMounted(() => {
  currentUser.value = getUserInfo()
  if (currentUser.value) {
    selectedUserId.value = currentUser.value.userId
  }
})

// 切换用户
const switchUser = () => {
  if (!selectedUserId.value) {
    ElMessage.warning('请选择用户')
    return
  }

  const user = mockUsers.find(u => u.userId === selectedUserId.value)
  if (user) {
    // 设置用户信息
    setUserInfo(user)
    currentUser.value = user
    
    ElMessage.success(`已切换到用户: ${user.username}`)
    
    // 刷新页面以更新数据
    setTimeout(() => {
      window.location.reload()
    }, 1000)
  } else {
    ElMessage.error('用户不存在')
  }
}

// 退出登录
const logout = () => {
  clearUserInfo()
  currentUser.value = null
  selectedUserId.value = null
  ElMessage.success('已退出登录')
  
  // 刷新页面
  setTimeout(() => {
    window.location.reload()
  }, 1000)
}
</script>

<style scoped>
.user-switcher {
  margin-bottom: 20px;
}

.switcher-card {
  max-width: 600px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.user-info p {
  margin: 5px 0;
  color: #606266;
}

.user-actions {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.permission-info {
  margin-top: 15px;
}

.permission-info :deep(.el-alert__content) {
  padding-left: 0;
}

.permission-info p {
  margin: 2px 0;
  font-size: 13px;
}

@media (max-width: 768px) {
  .user-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .user-actions .el-select {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 10px;
  }
}
</style>
