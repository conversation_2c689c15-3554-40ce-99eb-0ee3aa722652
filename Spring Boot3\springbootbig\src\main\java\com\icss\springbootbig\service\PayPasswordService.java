package com.icss.springbootbig.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icss.springbootbig.entity.BankCard;
import com.icss.springbootbig.entity.User;
import com.icss.springbootbig.exception.ApiException;
import com.icss.springbootbig.mapper.BankCardMapper;
import com.icss.springbootbig.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付密码管理服务
 * 支付密码存储在银行卡表中，每张银行卡都有独立的支付密码
 */
@Service
public class PayPasswordService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private BankCardMapper bankCardMapper;

    /**
     * 验证支付密码格式
     */
    private boolean isValidPayPassword(String payPassword) {
        return payPassword != null && payPassword.matches("^\\d{6}$");
    }

    /**
     * 设置银行卡支付密码
     */
    @Transactional
    public void setCardPayPassword(Integer userId, Integer cardId, String payPassword, String loginPassword) {
        // 验证用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }
        
        // 验证登录密码
        if (!loginPassword.equals(user.getPassword())) {
            throw new ApiException("登录密码错误");
        }
        
        // 验证银行卡
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null || !card.getUserId().equals(userId)) {
            throw new ApiException("银行卡不存在或不属于该用户");
        }
        
        // 验证支付密码格式
        if (!isValidPayPassword(payPassword)) {
            throw new ApiException("支付密码必须是6位数字");
        }
        
        // 更新银行卡支付密码
        card.setPayPassword(payPassword);
        card.setUpdatedAt(new Date());
        bankCardMapper.updateById(card);
    }

    /**
     * 修改银行卡支付密码
     */
    @Transactional
    public void changeCardPayPassword(Integer userId, Integer cardId, String oldPayPassword, String newPayPassword) {
        BankCard card;

        if (cardId == null) {
            // 如果没有指定银行卡ID，使用默认银行卡
            card = bankCardMapper.selectOne(
                    new QueryWrapper<BankCard>()
                            .eq("user_id", userId)
                            .eq("is_default", 1)
                            .eq("status", 1));
            if (card == null) {
                throw new ApiException("请先设置默认银行卡");
            }
        } else {
            // 验证指定的银行卡
            card = bankCardMapper.selectById(cardId);
            if (card == null || !card.getUserId().equals(userId)) {
                throw new ApiException("银行卡不存在或不属于该用户");
            }
        }
        
        // 验证原支付密码
        if (!oldPayPassword.equals(card.getPayPassword())) {
            throw new ApiException("原支付密码错误");
        }
        
        // 验证新支付密码格式
        if (!isValidPayPassword(newPayPassword)) {
            throw new ApiException("新支付密码必须是6位数字");
        }
        
        // 更新支付密码
        card.setPayPassword(newPayPassword);
        card.setUpdatedAt(new Date());
        bankCardMapper.updateById(card);
    }

    /**
     * 重置银行卡支付密码（通过登录密码）
     */
    @Transactional
    public void resetCardPayPassword(Integer userId, Integer cardId, String loginPassword, String newPayPassword) {
        // 验证用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 验证登录密码
        if (!loginPassword.equals(user.getPassword())) {
            throw new ApiException("登录密码错误");
        }

        BankCard card;

        if (cardId == null) {
            // 如果没有指定银行卡ID，使用默认银行卡
            card = bankCardMapper.selectOne(
                    new QueryWrapper<BankCard>()
                            .eq("user_id", userId)
                            .eq("is_default", 1)
                            .eq("status", 1));
            if (card == null) {
                throw new ApiException("请先设置默认银行卡");
            }
        } else {
            // 验证指定的银行卡
            card = bankCardMapper.selectById(cardId);
            if (card == null || !card.getUserId().equals(userId)) {
                throw new ApiException("银行卡不存在或不属于该用户");
            }
        }
        
        // 验证新支付密码格式
        if (!isValidPayPassword(newPayPassword)) {
            throw new ApiException("新支付密码必须是6位数字");
        }
        
        // 更新支付密码
        card.setPayPassword(newPayPassword);
        card.setUpdatedAt(new Date());
        bankCardMapper.updateById(card);
    }

    /**
     * 设置默认银行卡支付密码
     */
    @Transactional
    public void setDefaultCardPayPassword(Integer userId, String payPassword, String loginPassword) {
        // 验证用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }
        
        // 验证登录密码
        if (!loginPassword.equals(user.getPassword())) {
            throw new ApiException("登录密码错误");
        }
        
        // 获取默认银行卡
        BankCard defaultCard = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("user_id", userId)
                        .eq("is_default", 1)
                        .eq("status", 1));
        
        if (defaultCard == null) {
            throw new ApiException("请先设置默认银行卡");
        }
        
        // 验证支付密码格式
        if (!isValidPayPassword(payPassword)) {
            throw new ApiException("支付密码必须是6位数字");
        }
        
        // 更新支付密码
        defaultCard.setPayPassword(payPassword);
        defaultCard.setUpdatedAt(new Date());
        bankCardMapper.updateById(defaultCard);
    }

    /**
     * 验证支付密码
     */
    public boolean verifyPayPassword(Integer userId, String payPassword) {
        // 获取默认银行卡
        BankCard defaultCard = bankCardMapper.selectOne(
                new QueryWrapper<BankCard>()
                        .eq("user_id", userId)
                        .eq("is_default", 1)
                        .eq("status", 1));
        
        if (defaultCard == null) {
            return false;
        }
        
        return payPassword.equals(defaultCard.getPayPassword());
    }

    /**
     * 验证指定银行卡的支付密码
     */
    public boolean verifyCardPayPassword(Integer cardId, String payPassword) {
        BankCard card = bankCardMapper.selectById(cardId);
        if (card == null || card.getStatus() != 1) {
            return false;
        }
        
        return payPassword.equals(card.getPayPassword());
    }

    /**
     * 获取支付密码设置状态
     */
    public Map<String, Object> getPayPasswordStatus(Integer userId) {
        Map<String, Object> status = new HashMap<>();
        
        // 获取用户所有银行卡
        var cards = bankCardMapper.selectList(
                new QueryWrapper<BankCard>()
                        .eq("user_id", userId)
                        .eq("status", 1));
        
        int totalCards = cards.size();
        int cardsWithPassword = 0;
        boolean hasDefaultCardPassword = false;
        
        for (BankCard card : cards) {
            if (card.getPayPassword() != null && !card.getPayPassword().isEmpty()) {
                cardsWithPassword++;
                if (card.getIsDefault() == 1) {
                    hasDefaultCardPassword = true;
                }
            }
        }
        
        status.put("totalCards", totalCards);
        status.put("cardsWithPassword", cardsWithPassword);
        status.put("hasDefaultCardPassword", hasDefaultCardPassword);
        status.put("allCardsHavePassword", totalCards > 0 && cardsWithPassword == totalCards);
        
        return status;
    }

    /**
     * 批量设置银行卡支付密码
     */
    @Transactional
    public void setBatchCardPayPassword(Integer userId, String payPassword, String loginPassword) {
        // 验证用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }
        
        // 验证登录密码
        if (!loginPassword.equals(user.getPassword())) {
            throw new ApiException("登录密码错误");
        }
        
        // 验证支付密码格式
        if (!isValidPayPassword(payPassword)) {
            throw new ApiException("支付密码必须是6位数字");
        }
        
        // 获取用户所有银行卡
        var cards = bankCardMapper.selectList(
                new QueryWrapper<BankCard>()
                        .eq("user_id", userId)
                        .eq("status", 1));
        
        if (cards.isEmpty()) {
            throw new ApiException("用户没有可用的银行卡");
        }
        
        // 批量更新支付密码
        for (BankCard card : cards) {
            card.setPayPassword(payPassword);
            card.setUpdatedAt(new Date());
            bankCardMapper.updateById(card);
        }
    }
}
