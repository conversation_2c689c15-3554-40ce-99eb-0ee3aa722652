package com.icss.springbootbig.enums;

import com.icss.springbootbig.exception.ApiException;
import com.icss.springbootbig.exception.DataNotFoundException;
import com.icss.springbootbig.result.R;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(ApiException.class)
    public R<String> handleApiException(ApiException e) {
        return R.failure(e.getMessage());
    }

    @ExceptionHandler(DataNotFoundException.class)
    public R<String> handleDataNotFoundException(DataNotFoundException e) {
        return R.failure(e.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public R<String> handleException(Exception e) {
        e.printStackTrace(); // 打印堆栈信息用于调试
        return R.failure("服务器内部错误: " + e.getMessage());
    }
}