package com.icss.springbootbig.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icss.springbootbig.entity.Transaction;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Repository
public interface TransactionMapper extends BaseMapper<Transaction> {

    /**
     * 根据用户ID查询交易记录
     * @param userId 用户ID
     * @return 交易记录列表
     */
    @Select("SELECT * FROM transactions WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<Transaction> findByUserId(@Param("userId") Integer userId);

    /**
     * 多条件查询交易记录
     * @param userId 用户ID
     * @param type 交易类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 符合条件的交易记录列表
     */
    @Select("<script>" +
            "SELECT * FROM transactions WHERE user_id = #{userId} " +
            "<if test='type != null'> AND type = #{type} </if>" +
            "<if test='startTime != null'> AND created_at &gt;= #{startTime} </if>" +
            "<if test='endTime != null'> AND created_at &lt;= #{endTime} </if>" +
            "ORDER BY created_at DESC" +
            "</script>")
    List<Transaction> findByConditions(
            @Param("userId") Integer userId,
            @Param("type") Integer type,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime);

    /**
     * 根据手机号查询用户交易记录
     * @param phone 手机号
     * @param type 交易类型(可选)
     * @return 交易记录列表
     */
    @Select("<script>" +
            "SELECT t.* FROM transactions t " +
            "JOIN users u ON t.user_id = u.user_id " +
            "WHERE u.phone = #{phone} " +
            "<if test='type != null'> AND t.type = #{type} </if>" +
            "ORDER BY t.created_at DESC" +
            "</script>")
    List<Transaction> findByPhone(
            @Param("phone") String phone,
            @Param("type") Integer type);

    /**
     * 查询指定时间范围内的交易总额
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 交易总额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM transactions " +
            "WHERE user_id = #{userId} " +
            "AND created_at BETWEEN #{startTime} AND #{endTime}")
    BigDecimal sumAmountByPeriod(
            @Param("userId") Integer userId,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime);

    /**
     * 获取最近N条交易记录
     * @param userId 用户ID
     * @param limit 记录条数
     * @return 最近交易记录
     */
    @Select("SELECT * FROM transactions " +
            "WHERE user_id = #{userId} " +
            "ORDER BY created_at DESC LIMIT #{limit}")
    List<Transaction> findRecentTransactions(
            @Param("userId") Integer userId,
            @Param("limit") Integer limit);
}