package com.icss.springbootbig.result;

import lombok.Data;

/**
 * 统一响应封装类
 * @param <T> 泛型类型
 */
@Data
public class R<T> {
    private Integer code; // 编码：1成功，0和其它数字为失败
    private String msg;   // 错误信息
    private T data;       // 数据

    private R() {}

    // 成功响应（带数据和消息）
    public static <T> R<T> success(String msg, T object) {
        R<T> r = new R<>();
        r.data = object;
        r.msg = msg;
        r.code = 0; // 约定0为成功
        return r;
    }

    public static <T> R<T> success(String msg) {
        R<T> r = new R<>();
        r.data = null;
        r.msg = msg;
        r.code = 0;
        return r;
    }

    public static <T> R<T> failure(String msg) {
        R<T> r = new R<>();
        r.data = null;
        r.msg = msg;
        r.code = 1; // 1为失败
        return r;
    }
}