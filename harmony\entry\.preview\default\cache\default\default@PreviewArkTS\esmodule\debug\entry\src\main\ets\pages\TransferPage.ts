if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface TransferPage_Params {
    toPhone?: string;
    amount?: string;
    payPassword?: string;
    description?: string;
    payLimit?: number;
    isLoading?: boolean;
    selectedCard?: BankCard | null;
    bankCards?: BankCard[];
    transferMethod?: string;
    showCardSelector?: boolean;
}
import router from "@ohos:router";
import promptAction from "@ohos:promptAction";
import { WalletApi } from "@normalized:N&&&entry/src/main/ets/api/WalletApi&";
import { storageManager } from "@normalized:N&&&entry/src/main/ets/common/storage/StorageManager&";
import { globalStateManager, RefreshTypes } from "@normalized:N&&&entry/src/main/ets/common/utils/EventManager&";
import { BankCardType, BankCardStatus } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import type { WalletTransferRequest, BankCard, SpringBootBankCardResponse } from "@normalized:N&&&entry/src/main/ets/common/types/index&";
import { httpClient } from "@normalized:N&&&entry/src/main/ets/common/http/HttpClient&";
class TransferPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__toPhone = new ObservedPropertySimplePU('', this, "toPhone");
        this.__amount = new ObservedPropertySimplePU('', this, "amount");
        this.__payPassword = new ObservedPropertySimplePU('', this, "payPassword");
        this.__description = new ObservedPropertySimplePU('', this, "description");
        this.__payLimit = new ObservedPropertySimplePU(0, this, "payLimit");
        this.__isLoading = new ObservedPropertySimplePU(false, this, "isLoading");
        this.__selectedCard = new ObservedPropertyObjectPU(null, this, "selectedCard");
        this.__bankCards = new ObservedPropertyObjectPU([], this, "bankCards");
        this.__transferMethod = new ObservedPropertySimplePU('wallet', this, "transferMethod");
        this.__showCardSelector = new ObservedPropertySimplePU(false, this, "showCardSelector");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: TransferPage_Params) {
        if (params.toPhone !== undefined) {
            this.toPhone = params.toPhone;
        }
        if (params.amount !== undefined) {
            this.amount = params.amount;
        }
        if (params.payPassword !== undefined) {
            this.payPassword = params.payPassword;
        }
        if (params.description !== undefined) {
            this.description = params.description;
        }
        if (params.payLimit !== undefined) {
            this.payLimit = params.payLimit;
        }
        if (params.isLoading !== undefined) {
            this.isLoading = params.isLoading;
        }
        if (params.selectedCard !== undefined) {
            this.selectedCard = params.selectedCard;
        }
        if (params.bankCards !== undefined) {
            this.bankCards = params.bankCards;
        }
        if (params.transferMethod !== undefined) {
            this.transferMethod = params.transferMethod;
        }
        if (params.showCardSelector !== undefined) {
            this.showCardSelector = params.showCardSelector;
        }
    }
    updateStateVars(params: TransferPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__toPhone.purgeDependencyOnElmtId(rmElmtId);
        this.__amount.purgeDependencyOnElmtId(rmElmtId);
        this.__payPassword.purgeDependencyOnElmtId(rmElmtId);
        this.__description.purgeDependencyOnElmtId(rmElmtId);
        this.__payLimit.purgeDependencyOnElmtId(rmElmtId);
        this.__isLoading.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedCard.purgeDependencyOnElmtId(rmElmtId);
        this.__bankCards.purgeDependencyOnElmtId(rmElmtId);
        this.__transferMethod.purgeDependencyOnElmtId(rmElmtId);
        this.__showCardSelector.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__toPhone.aboutToBeDeleted();
        this.__amount.aboutToBeDeleted();
        this.__payPassword.aboutToBeDeleted();
        this.__description.aboutToBeDeleted();
        this.__payLimit.aboutToBeDeleted();
        this.__isLoading.aboutToBeDeleted();
        this.__selectedCard.aboutToBeDeleted();
        this.__bankCards.aboutToBeDeleted();
        this.__transferMethod.aboutToBeDeleted();
        this.__showCardSelector.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __toPhone: ObservedPropertySimplePU<string>;
    get toPhone() {
        return this.__toPhone.get();
    }
    set toPhone(newValue: string) {
        this.__toPhone.set(newValue);
    }
    private __amount: ObservedPropertySimplePU<string>;
    get amount() {
        return this.__amount.get();
    }
    set amount(newValue: string) {
        this.__amount.set(newValue);
    }
    private __payPassword: ObservedPropertySimplePU<string>;
    get payPassword() {
        return this.__payPassword.get();
    }
    set payPassword(newValue: string) {
        this.__payPassword.set(newValue);
    }
    private __description: ObservedPropertySimplePU<string>;
    get description() {
        return this.__description.get();
    }
    set description(newValue: string) {
        this.__description.set(newValue);
    }
    private __payLimit: ObservedPropertySimplePU<number>;
    get payLimit() {
        return this.__payLimit.get();
    }
    set payLimit(newValue: number) {
        this.__payLimit.set(newValue);
    }
    private __isLoading: ObservedPropertySimplePU<boolean>;
    get isLoading() {
        return this.__isLoading.get();
    }
    set isLoading(newValue: boolean) {
        this.__isLoading.set(newValue);
    }
    private __selectedCard: ObservedPropertyObjectPU<BankCard | null>;
    get selectedCard() {
        return this.__selectedCard.get();
    }
    set selectedCard(newValue: BankCard | null) {
        this.__selectedCard.set(newValue);
    }
    private __bankCards: ObservedPropertyObjectPU<BankCard[]>;
    get bankCards() {
        return this.__bankCards.get();
    }
    set bankCards(newValue: BankCard[]) {
        this.__bankCards.set(newValue);
    }
    private __transferMethod: ObservedPropertySimplePU<string>; // 'wallet' 或 'bankCard'
    get transferMethod() {
        return this.__transferMethod.get();
    }
    set transferMethod(newValue: string) {
        this.__transferMethod.set(newValue);
    }
    private __showCardSelector: ObservedPropertySimplePU<boolean>;
    get showCardSelector() {
        return this.__showCardSelector.get();
    }
    set showCardSelector(newValue: boolean) {
        this.__showCardSelector.set(newValue);
    }
    aboutToAppear() {
        // 检查路由参数
        const params = router.getParams() as Record<string, Object>;
        if (params?.selectedCard) {
            this.selectedCard = params.selectedCard as BankCard;
            this.transferMethod = 'bankCard';
        }
        if (params?.transferMethod) {
            this.transferMethod = params.transferMethod as string;
        }
        this.loadPayLimit();
        this.loadBankCards();
    }
    async loadPayLimit(): Promise<void> {
        try {
            const userInfo = await storageManager.getUserInfo();
            this.payLimit = userInfo?.payLimit || 0;
        }
        catch (error) {
            console.error('获取支付限额失败:', error);
        }
    }
    async loadBankCards() {
        try {
            // 从本地存储获取当前用户ID
            const cachedUserInfo = await storageManager.getUserInfo();
            if (!cachedUserInfo || !cachedUserInfo.userId) {
                console.error('无法获取用户ID');
                return;
            }
            // 调用SpringBoot3后端API获取银行卡列表
            const response = await httpClient.get<SpringBootBankCardResponse[]>(`/bank-card/user/${cachedUserInfo.userId}`);
            const bankCardList: SpringBootBankCardResponse[] = response.data;
            // 转换为本地格式
            this.bankCards = this.convertSpringBootBankCardsToLocal(bankCardList);
            console.log('银行卡列表加载完成，数量:', this.bankCards.length);
        }
        catch (error) {
            console.error('加载银行卡列表失败:', error);
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(69:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#FFFFFF');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(71:7)", "entry");
            // 顶部导航
            Row.width('100%');
            // 顶部导航
            Row.padding({ left: 16, right: 16, top: 12, bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.debugLine("entry/src/main/ets/pages/TransferPage.ets(72:9)", "entry");
            Button.width(40);
            Button.height(40);
            Button.borderRadius(20);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777238, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/TransferPage.ets(73:11)", "entry");
            Image.width(24);
            Image.height(24);
            Image.fillColor('#333333');
        }, Image);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('转账');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(86:9)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(93:9)", "entry");
            Row.width(40);
            Row.height(40);
        }, Row);
        Row.pop();
        // 顶部导航
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/TransferPage.ets(98:7)", "entry");
            Scroll.layoutWeight(1);
            Scroll.scrollable(ScrollDirection.Vertical);
            Scroll.scrollBar(BarState.Off);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(99:9)", "entry");
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转账表单
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(101:11)", "entry");
            // 转账表单
            Column.width('100%');
            // 转账表单
            Column.padding({ left: 24, right: 24, top: 20, bottom: 40 });
        }, Column);
        // 转账方式选择
        this.TransferMethodSelector.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 银行卡选择（仅在银行卡转账模式下显示）
            if (this.transferMethod === 'bankCard') {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.BankCardSelector.bind(this)();
                });
            }
            // 收款人手机号
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        // 收款人手机号
        this.InputField.bind(this)('收款人手机号', '请输入收款人手机号', this.toPhone, (value: string) => {
            this.toPhone = value;
        }, InputType.PhoneNumber, 11);
        // 转账金额
        this.InputField.bind(this)('转账金额', '请输入转账金额', this.amount, (value: string) => {
            this.amount = value;
        }, InputType.Number);
        // 转账说明
        this.InputField.bind(this)('转账说明（可选）', '请输入转账说明', this.description, (value: string) => {
            this.description = value;
        });
        // 支付密码
        this.PasswordField.bind(this)('支付密码', '请输入支付密码', this.payPassword, (value: string) => {
            this.payPassword = value;
        });
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 转账按钮
            Button.createWithLabel('确认转账');
            Button.debugLine("entry/src/main/ets/pages/TransferPage.ets(131:13)", "entry");
            // 转账按钮
            Button.width('100%');
            // 转账按钮
            Button.height(48);
            // 转账按钮
            Button.fontSize(16);
            // 转账按钮
            Button.fontColor(Color.White);
            // 转账按钮
            Button.backgroundColor('#1976D2');
            // 转账按钮
            Button.borderRadius(8);
            // 转账按钮
            Button.margin({ top: 30 });
            // 转账按钮
            Button.enabled(!this.isLoading && this.isFormValid());
            // 转账按钮
            Button.opacity((!this.isLoading && this.isFormValid()) ? 1 : 0.5);
            // 转账按钮
            Button.onClick(() => {
                this.handleTransfer();
            });
        }, Button);
        // 转账按钮
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 温馨提示
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(146:13)", "entry");
            // 温馨提示
            Column.width('100%');
            // 温馨提示
            Column.padding(16);
            // 温馨提示
            Column.backgroundColor('#F8F9FA');
            // 温馨提示
            Column.borderRadius(8);
            // 温馨提示
            Column.margin({ top: 20 });
            // 温馨提示
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('温馨提示');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(147:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`• 请确认收款人手机号无误\n• 转账完成后无法撤销\n• 单笔转账限额：¥${this.payLimit.toFixed(2)}`);
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(154:15)", "entry");
            Text.fontSize(12);
            Text.fontColor('#666666');
            Text.lineHeight(18);
        }, Text);
        Text.pop();
        // 温馨提示
        Column.pop();
        // 转账表单
        Column.pop();
        Column.pop();
        Scroll.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 银行卡选择器弹窗
            if (this.showCardSelector) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.BankCardSelectorDialog.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Column.pop();
    }
    TransferMethodSelector(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(185:5)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('转账方式');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(186:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(192:7)", "entry");
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('钱包转账');
            Button.debugLine("entry/src/main/ets/pages/TransferPage.ets(193:9)", "entry");
            Button.fontSize(14);
            Button.fontColor(this.transferMethod === 'wallet' ? '#FFFFFF' : '#1976D2');
            Button.backgroundColor(this.transferMethod === 'wallet' ? '#1976D2' : '#E3F2FD');
            Button.borderRadius(6);
            Button.layoutWeight(1);
            Button.height(40);
            Button.margin({ right: 8 });
            Button.onClick(() => {
                this.transferMethod = 'wallet';
                this.selectedCard = null;
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('银行卡转账');
            Button.debugLine("entry/src/main/ets/pages/TransferPage.ets(206:9)", "entry");
            Button.fontSize(14);
            Button.fontColor(this.transferMethod === 'bankCard' ? '#FFFFFF' : '#1976D2');
            Button.backgroundColor(this.transferMethod === 'bankCard' ? '#1976D2' : '#E3F2FD');
            Button.borderRadius(6);
            Button.layoutWeight(1);
            Button.height(40);
            Button.margin({ left: 8 });
            Button.onClick(() => {
                this.transferMethod = 'bankCard';
                if (this.bankCards.length > 0 && !this.selectedCard) {
                    this.selectedCard = this.bankCards.find(card => card.isDefault) || this.bankCards[0];
                }
            });
        }, Button);
        Button.pop();
        Row.pop();
        Column.pop();
    }
    BankCardSelector(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(229:5)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择银行卡');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(230:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(236:7)", "entry");
            Row.width('100%');
            Row.height(48);
            Row.padding({ left: 12, right: 12 });
            Row.backgroundColor('#F8F9FA');
            Row.borderRadius(8);
            Row.border({ width: 1, color: '#E0E0E0' });
            Row.onClick(() => {
                this.showBankCardSelector();
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.selectedCard) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 显示已选择的银行卡
                        Row.create();
                        Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(239:11)", "entry");
                        // 显示已选择的银行卡
                        Row.layoutWeight(1);
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // 银行卡缩略图
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(241:13)", "entry");
                        // 银行卡缩略图
                        Column.width(40);
                        // 银行卡缩略图
                        Column.height(28);
                        // 银行卡缩略图
                        Column.borderRadius(4);
                        // 银行卡缩略图
                        Column.justifyContent(FlexAlign.Center);
                        // 银行卡缩略图
                        Column.linearGradient({
                            direction: GradientDirection.Right,
                            colors: this.getBankCardGradient(this.selectedCard.bankName)
                        });
                        // 银行卡缩略图
                        Column.margin({ right: 12 });
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.selectedCard.bankName.substring(0, 2));
                        Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(242:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#FFFFFF');
                        Text.fontWeight(FontWeight.Bold);
                    }, Text);
                    Text.pop();
                    // 银行卡缩略图
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(257:13)", "entry");
                        Column.alignItems(HorizontalAlign.Start);
                        Column.layoutWeight(1);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(this.selectedCard.bankName);
                        Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(258:15)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#333333');
                        Text.fontWeight(FontWeight.Medium);
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(`${this.getCardTypeText(this.selectedCard.cardType)} • ${this.formatCardNumber(this.selectedCard.cardNo)}`);
                        Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(264:15)", "entry");
                        Text.fontSize(14);
                        Text.fontColor('#666666');
                        Text.margin({ top: 2 });
                        Text.alignSelf(ItemAlign.Start);
                    }, Text);
                    Text.pop();
                    Column.pop();
                    // 显示已选择的银行卡
                    Row.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('请选择银行卡');
                        Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(275:11)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#999999');
                        Text.layoutWeight(1);
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": ********, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/TransferPage.ets(281:9)", "entry");
            Image.width(16);
            Image.height(16);
            Image.fillColor('#999999');
        }, Image);
        Row.pop();
        Column.pop();
    }
    BankCardSelectorDialog(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create({ alignContent: Alignment.Bottom });
            Stack.debugLine("entry/src/main/ets/pages/TransferPage.ets(302:5)", "entry");
            Stack.width('100%');
            Stack.height('100%');
            Stack.position({ x: 0, y: 0 });
            Stack.zIndex(1000);
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 遮罩层
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(304:7)", "entry");
            // 遮罩层
            Column.width('100%');
            // 遮罩层
            Column.height('100%');
            // 遮罩层
            Column.backgroundColor('rgba(0,0,0,0.5)');
            // 遮罩层
            Column.onClick(() => {
                this.showCardSelector = false;
            });
        }, Column);
        // 遮罩层
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡选择面板
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(313:7)", "entry");
            Context.animation({
                duration: 300,
                curve: Curve.EaseInOut
            });
            // 银行卡选择面板
            Column.width('100%');
            // 银行卡选择面板
            Column.backgroundColor('#FFFFFF');
            // 银行卡选择面板
            Column.borderRadius({ topLeft: 16, topRight: 16 });
            Context.animation(null);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部标题栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(315:9)", "entry");
            // 顶部标题栏
            Row.width('100%');
            // 顶部标题栏
            Row.height(56);
            // 顶部标题栏
            Row.padding({ left: 16, right: 16 });
            // 顶部标题栏
            Row.border({
                width: { bottom: 1 },
                color: '#E0E0E0'
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('取消');
            Button.debugLine("entry/src/main/ets/pages/TransferPage.ets(316:11)", "entry");
            Button.fontSize(16);
            Button.fontColor('#666666');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                this.showCardSelector = false;
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('选择银行卡');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(324:11)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(331:11)", "entry");
            Text.width(60);
        }, Text);
        Text.pop();
        // 顶部标题栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 银行卡列表
            if (this.bankCards.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        List.create();
                        List.debugLine("entry/src/main/ets/pages/TransferPage.ets(344:11)", "entry");
                        List.height(300);
                        List.scrollBar(BarState.Off);
                        List.padding({ left: 16, right: 16, top: 16, bottom: 16 });
                    }, List);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        ForEach.create();
                        const forEachItemGenFunction = _item => {
                            const card = _item;
                            {
                                const itemCreation = (elmtId, isInitialRender) => {
                                    ViewStackProcessor.StartGetAccessRecordingFor(elmtId);
                                    itemCreation2(elmtId, isInitialRender);
                                    if (!isInitialRender) {
                                        ListItem.pop();
                                    }
                                    ViewStackProcessor.StopGetAccessRecording();
                                };
                                const itemCreation2 = (elmtId, isInitialRender) => {
                                    ListItem.create(deepRenderFunction, true);
                                    ListItem.debugLine("entry/src/main/ets/pages/TransferPage.ets(346:15)", "entry");
                                };
                                const deepRenderFunction = (elmtId, isInitialRender) => {
                                    itemCreation(elmtId, isInitialRender);
                                    this.BankCardItem.bind(this)(card);
                                    ListItem.pop();
                                };
                                this.observeComponentCreation2(itemCreation2, ListItem);
                                ListItem.pop();
                            }
                        };
                        this.forEachUpdateFunction(elmtId, this.bankCards, forEachItemGenFunction);
                    }, ForEach);
                    ForEach.pop();
                    List.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(355:11)", "entry");
                        Column.width('100%');
                        Column.justifyContent(FlexAlign.Center);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('暂无可用银行卡');
                        Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(356:13)", "entry");
                        Text.fontSize(16);
                        Text.fontColor('#999999');
                        Text.margin({ top: 40, bottom: 40 });
                    }, Text);
                    Text.pop();
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        // 银行卡选择面板
        Column.pop();
        Stack.pop();
    }
    BankCardItem(card: BankCard, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(381:5)", "entry");
            Row.width('100%');
            Row.padding(16);
            Row.backgroundColor(this.selectedCard?.cardId === card.cardId ? '#E8F5E8' : '#FFFFFF');
            Row.borderRadius(8);
            Row.border({
                width: this.selectedCard?.cardId === card.cardId ? 2 : 1,
                color: this.selectedCard?.cardId === card.cardId ? '#4CAF50' : '#E0E0E0'
            });
            Row.margin({ bottom: 12 });
            Row.onClick(() => {
                this.selectedCard = card;
                this.showCardSelector = false;
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡缩略图
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(383:7)", "entry");
            // 银行卡缩略图
            Column.width(48);
            // 银行卡缩略图
            Column.height(32);
            // 银行卡缩略图
            Column.borderRadius(4);
            // 银行卡缩略图
            Column.justifyContent(FlexAlign.Center);
            // 银行卡缩略图
            Column.linearGradient({
                direction: GradientDirection.Right,
                colors: this.getBankCardGradient(card.bankName)
            });
            // 银行卡缩略图
            Column.margin({ right: 12 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.bankName.substring(0, 2));
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(384:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#FFFFFF');
            Text.fontWeight(FontWeight.Bold);
        }, Text);
        Text.pop();
        // 银行卡缩略图
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 银行卡信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(400:7)", "entry");
            // 银行卡信息
            Column.alignItems(HorizontalAlign.Start);
            // 银行卡信息
            Column.layoutWeight(1);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/TransferPage.ets(401:9)", "entry");
            Row.width('100%');
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(card.bankName);
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(402:11)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (card.isDefault) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('默认');
                        Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(409:13)", "entry");
                        Text.fontSize(10);
                        Text.fontColor('#FFFFFF');
                        Text.backgroundColor('#FF9800');
                        Text.borderRadius(8);
                        Text.padding({ left: 6, right: 6, top: 2, bottom: 2 });
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`${this.getCardTypeText(card.cardType)} • ${this.formatCardNumber(card.cardNo)}`);
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(420:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        // 银行卡信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // 选择状态
            if (this.selectedCard?.cardId === card.cardId) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('✓');
                        Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(430:9)", "entry");
                        Text.fontSize(20);
                        Text.fontColor('#4CAF50');
                        Text.fontWeight(FontWeight.Bold);
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Row.pop();
    }
    InputField(label: string, placeholder: string, value: string, onChange: (value: string) => void, inputType: InputType = InputType.Normal, maxLength?: number, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(460:5)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(461:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: placeholder });
            TextInput.debugLine("entry/src/main/ets/pages/TransferPage.ets(467:7)", "entry");
            TextInput.type(inputType);
            TextInput.maxLength(maxLength);
            TextInput.fontSize(16);
            TextInput.height(48);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.onChange(onChange);
        }, TextInput);
        Column.pop();
    }
    PasswordField(label: string, placeholder: string, value: string, onChange: (value: string) => void, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/TransferPage.ets(488:5)", "entry");
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/TransferPage.ets(489:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            TextInput.create({ placeholder: placeholder });
            TextInput.debugLine("entry/src/main/ets/pages/TransferPage.ets(495:7)", "entry");
            TextInput.type(InputType.Password);
            TextInput.fontSize(16);
            TextInput.height(48);
            TextInput.borderRadius(8);
            TextInput.backgroundColor('#F8F9FA');
            TextInput.border({ width: 1, color: '#E0E0E0' });
            TextInput.showPasswordIcon(true);
            TextInput.onChange(onChange);
        }, TextInput);
        Column.pop();
    }
    /**
     * 表单验证
     */
    isFormValid(): boolean {
        const amountNum = parseFloat(this.amount);
        return this.toPhone.length === 11 &&
            this.amount.length > 0 &&
            !isNaN(amountNum) &&
            amountNum > 0 &&
            amountNum <= this.payLimit &&
            this.payPassword.length >= 6;
    }
    /**
     * 处理转账
     */
    async handleTransfer() {
        if (this.isLoading)
            return;
        // 表单验证
        if (!this.validateForm())
            return;
        this.isLoading = true;
        try {
            const transferData: WalletTransferRequest = {
                toPhone: this.toPhone,
                amount: parseFloat(this.amount),
                payPassword: this.payPassword,
                description: this.description || undefined
            };
            await WalletApi.transfer(transferData);
            promptAction.showToast({ message: '转账成功' });
            // 标记需要刷新交易数据
            globalStateManager.markForRefresh(RefreshTypes.TRANSACTION);
            // 返回主页
            router.back();
        }
        catch (error) {
            console.error('转账失败:', error);
            promptAction.showToast({ message: '转账失败，请重试' });
        }
        finally {
            this.isLoading = false;
        }
    }
    /**
     * 表单验证
     */
    validateForm(): boolean {
        if (this.toPhone.length !== 11) {
            promptAction.showToast({ message: '请输入正确的收款人手机号' });
            return false;
        }
        const amountNum = parseFloat(this.amount);
        if (this.amount.length === 0 || isNaN(amountNum) || amountNum <= 0) {
            promptAction.showToast({ message: '请输入正确的转账金额' });
            return false;
        }
        if (amountNum > this.payLimit) {
            promptAction.showToast({ message: `转账金额不能超过支付限额 ¥${this.payLimit.toFixed(2)}` });
            return false;
        }
        if (this.payPassword.length < 6) {
            promptAction.showToast({ message: '请输入支付密码' });
            return false;
        }
        return true;
    }
    /**
     * 显示银行卡选择器
     */
    showBankCardSelector() {
        this.showCardSelector = true;
    }
    /**
     * 转换SpringBoot3银行卡到本地格式
     */
    private convertSpringBootBankCardsToLocal(bankCardList: SpringBootBankCardResponse[]): BankCard[] {
        return bankCardList.map((card: SpringBootBankCardResponse): BankCard => ({
            cardId: card.cardId || 0,
            userId: card.userId || 0,
            cardNo: card.cardNumber || '',
            cardType: this.mapBankCardType(card.cardType),
            bankName: card.bankName || '',
            holderName: card.cardHolder || '',
            isBound: card.status === 1 ? BankCardStatus.BOUND : BankCardStatus.UNBOUND,
            createTime: card.createdAt || card.createTime || '',
            updateTime: card.updatedAt || card.updateTime || '',
            maskedCardNo: card.cardNumber ? this.maskCardNo(card.cardNumber) : undefined,
            isDefault: card.isDefault === 1 || card.isDefault === true
        }));
    }
    private mapBankCardType(cardType: string | number | undefined): BankCardType {
        if (typeof cardType === 'number') {
            return cardType === 2 ? BankCardType.CREDIT : BankCardType.DEBIT;
        }
        else if (typeof cardType === 'string') {
            if (cardType === '信用卡' || cardType === 'CREDIT') {
                return BankCardType.CREDIT;
            }
        }
        return BankCardType.DEBIT;
    }
    private maskCardNo(cardNo: string): string {
        if (!cardNo || cardNo.length < 8)
            return cardNo;
        return cardNo.replace(/(\d{4})\d*(\d{4})/, '$1****$2');
    }
    private getCardTypeText(cardType: BankCardType): string {
        switch (cardType) {
            case BankCardType.CREDIT:
                return '信用卡';
            case BankCardType.DEBIT:
                return '储蓄卡';
            default:
                return '储蓄卡';
        }
    }
    private formatCardNumber(cardNo: string): string {
        if (!cardNo)
            return '';
        return `**** **** **** ${cardNo.slice(-4)}`;
    }
    private getBankCardGradient(bankName: string): Array<[
        string,
        number
    ]> {
        const gradients: Record<string, Array<[
            string,
            number
        ]>> = {
            '中国工商银行': [['#C41E3A', 0], ['#8B0000', 1]],
            '中国建设银行': [['#003DA5', 0], ['#001F5C', 1]],
            '中国农业银行': [['#00A651', 0], ['#006B35', 1]],
            '中国银行': [['#B8860B', 0], ['#8B6914', 1]],
            '招商银行': [['#DC143C', 0], ['#8B0000', 1]],
            '交通银行': [['#0066CC', 0], ['#003D7A', 1]],
            '中信银行': [['#FF6B35', 0], ['#CC4125', 1]],
            '光大银行': [['#9932CC', 0], ['#6A1B9A', 1]],
            '华夏银行': [['#FF1744', 0], ['#C62828', 1]],
            '民生银行': [['#00BCD4', 0], ['#0097A7', 1]],
            '广发银行': [['#FF5722', 0], ['#D84315', 1]],
            '平安银行': [['#FF9800', 0], ['#F57C00', 1]]
        };
        return gradients[bankName] || [['#1976D2', 0], ['#0D47A1', 1]];
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "TransferPage";
    }
}
registerNamedRoute(() => new TransferPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/TransferPage", pageFullPath: "entry/src/main/ets/pages/TransferPage", integratedHsp: "false", moduleType: "followWithHap" });
