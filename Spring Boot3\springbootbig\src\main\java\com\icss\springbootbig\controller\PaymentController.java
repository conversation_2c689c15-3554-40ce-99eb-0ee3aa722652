package com.icss.springbootbig.controller;

import com.icss.springbootbig.result.R;
import com.icss.springbootbig.service.PaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;
@CrossOrigin(origins = {"http://localhost:5173", "http://127.0.0.1:5173"})
@RestController
@RequestMapping("/api/payment")
public class PaymentController {
    private final PaymentService paymentService;

    @Autowired
    public PaymentController(PaymentService paymentService) {
        this.paymentService = paymentService;
    }

    @PostMapping("/wallet")
    public R<String> walletPayment(
            @RequestParam Integer userId,
            @RequestParam BigDecimal amount,
            @RequestParam String paymentType,
            @RequestParam String payPassword,
            @RequestParam(required = false) String remark,
            @RequestParam(required = false) Integer merchantId) {

        try {
            paymentService.walletPayment(userId, amount, paymentType, remark, payPassword);
            return R.success("支付成功");
        } catch (Exception e) {
            return R.failure(e.getMessage());
        }
    }

    @PostMapping("/bank-card")
    public R<String> bankCardPayment(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            BigDecimal amount = new BigDecimal(request.get("amount").toString());
            Integer cardId = (Integer) request.get("cardId");
            Integer merchantId = (Integer) request.get("merchantId");
            String payPassword = (String) request.get("payPassword");
            String remark = (String) request.get("remark");

            paymentService.bankCardPayment(userId, amount, cardId, merchantId, payPassword, remark);
            return R.success("银行卡支付成功");
        } catch (Exception e) {
            return R.failure("银行卡支付失败: " + e.getMessage());
        }
    }

    @PostMapping("/qr-code")
    public R<String> qrCodePayment(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            String qrCode = (String) request.get("qrCode");
            String payPassword = (String) request.get("payPassword");

            paymentService.qrCodePayment(userId, qrCode, payPassword);
            return R.success("扫码支付成功");
        } catch (Exception e) {
            return R.failure("扫码支付失败: " + e.getMessage());
        }
    }

    @PostMapping("/nfc")
    public R<String> nfcPayment(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            String nfcData = (String) request.get("nfcData");
            String payPassword = (String) request.get("payPassword");

            paymentService.nfcPayment(userId, nfcData, payPassword);
            return R.success("NFC支付成功");
        } catch (Exception e) {
            return R.failure("NFC支付失败: " + e.getMessage());
        }
    }
}