// 权限控制工具函数
import { getCurrentUserId, checkLogin } from '@/stores/user'
import { ElMessage } from 'element-plus'

/**
 * 检查用户是否有权限访问指定用户的数据
 * @param {number|string} targetUserId - 目标用户ID
 * @returns {boolean} - 是否有权限
 */
export function hasPermissionToAccess(targetUserId) {
  // 检查是否已登录
  if (!checkLogin()) {
    ElMessage.error('请先登录')
    return false
  }

  const currentUserId = getCurrentUserId()
  
  // 检查是否为同一用户
  if (currentUserId && targetUserId && currentUserId.toString() === targetUserId.toString()) {
    return true
  }

  // 如果不是同一用户，拒绝访问
  ElMessage.error('您没有权限访问其他用户的数据')
  return false
}

/**
 * 获取当前用户ID，如果未登录则返回null
 * @returns {number|null} - 当前用户ID
 */
export function getCurrentUserIdSafe() {
  if (!checkLogin()) {
    return null
  }
  return getCurrentUserId()
}

/**
 * 过滤数据，只返回属于当前用户的数据
 * @param {Array} dataList - 数据列表
 * @param {string} userIdField - 用户ID字段名，默认为'userId'
 * @returns {Array} - 过滤后的数据
 */
export function filterUserData(dataList, userIdField = 'userId') {
  const currentUserId = getCurrentUserIdSafe()
  
  if (!currentUserId || !Array.isArray(dataList)) {
    return []
  }

  return dataList.filter(item => {
    return item[userIdField] && item[userIdField].toString() === currentUserId.toString()
  })
}

/**
 * 检查数据是否属于当前用户
 * @param {Object} data - 数据对象
 * @param {string} userIdField - 用户ID字段名，默认为'userId'
 * @returns {boolean} - 是否属于当前用户
 */
export function isUserData(data, userIdField = 'userId') {
  const currentUserId = getCurrentUserIdSafe()
  
  if (!currentUserId || !data) {
    return false
  }

  return data[userIdField] && data[userIdField].toString() === currentUserId.toString()
}

/**
 * 为API请求添加用户ID参数
 * @param {Object} params - 原始参数
 * @returns {Object} - 添加用户ID后的参数
 */
export function addUserIdToParams(params = {}) {
  const currentUserId = getCurrentUserIdSafe()
  
  if (currentUserId) {
    return {
      ...params,
      userId: currentUserId
    }
  }
  
  return params
}

/**
 * 验证API响应数据的用户权限
 * @param {Object} response - API响应
 * @param {string} userIdField - 用户ID字段名
 * @returns {boolean} - 是否有权限访问该数据
 */
export function validateResponsePermission(response, userIdField = 'userId') {
  if (!response || !response.data) {
    return true // 空数据不需要验证
  }

  const currentUserId = getCurrentUserIdSafe()
  if (!currentUserId) {
    return false
  }

  // 如果是数组数据，检查所有项目
  if (Array.isArray(response.data)) {
    return response.data.every(item => 
      !item[userIdField] || item[userIdField].toString() === currentUserId.toString()
    )
  }

  // 如果是单个对象，检查用户ID
  if (response.data[userIdField]) {
    return response.data[userIdField].toString() === currentUserId.toString()
  }

  return true // 没有用户ID字段的数据允许访问
}

/**
 * 创建带权限检查的API调用包装器
 * @param {Function} apiFunction - 原始API函数
 * @param {string} userIdField - 用户ID字段名
 * @returns {Function} - 包装后的API函数
 */
export function createPermissionWrapper(apiFunction, userIdField = 'userId') {
  return async (...args) => {
    // 检查登录状态
    if (!checkLogin()) {
      throw new Error('用户未登录')
    }

    try {
      const response = await apiFunction(...args)
      
      // 验证响应数据权限
      if (!validateResponsePermission(response, userIdField)) {
        throw new Error('没有权限访问该数据')
      }

      return response
    } catch (error) {
      console.error('API调用权限检查失败:', error)
      throw error
    }
  }
}

/**
 * 权限检查装饰器
 * @param {Function} fn - 要装饰的函数
 * @param {Object} options - 选项
 * @returns {Function} - 装饰后的函数
 */
export function withPermissionCheck(fn, options = {}) {
  const { 
    requireLogin = true, 
    userIdField = 'userId',
    showError = true 
  } = options

  return async (...args) => {
    if (requireLogin && !checkLogin()) {
      if (showError) {
        ElMessage.error('请先登录')
      }
      return null
    }

    try {
      return await fn(...args)
    } catch (error) {
      if (showError) {
        ElMessage.error(error.message || '操作失败')
      }
      throw error
    }
  }
}

/**
 * 生成用户专属的模拟数据
 * @param {Array} baseData - 基础模拟数据
 * @param {string} userIdField - 用户ID字段名
 * @returns {Array} - 用户专属数据
 */
export function generateUserMockData(baseData, userIdField = 'userId') {
  const currentUserId = getCurrentUserIdSafe()
  
  if (!currentUserId || !Array.isArray(baseData)) {
    return []
  }

  return baseData.map(item => ({
    ...item,
    [userIdField]: currentUserId
  }))
}

export default {
  hasPermissionToAccess,
  getCurrentUserIdSafe,
  filterUserData,
  isUserData,
  addUserIdToParams,
  validateResponsePermission,
  createPermissionWrapper,
  withPermissionCheck,
  generateUserMockData
}
