import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { BankCard, BankCardType, BankCardStatus, SpringBootBankCardResponse, SpringBootAccountResponse, SpringBootUnbindBankCardRequest, SpringBootSetDefaultCardRequest, SpringBootBindCardRequest, DialogButton, BankCardPageParams } from '../common/types/index';
import { tempDataManager } from '../common/storage/TempDataManager';
import { httpClient } from '../common/http/HttpClient';
import { storageManager } from '../common/storage/StorageManager';

@Entry
@Component
struct BankCardPage {
  @State allCards: BankCard[] = []; // 所有银行卡（包括已解绑的历史记录）
  @State boundCards: BankCard[] = []; // 当前有效的绑定卡
  @State isLoading: boolean = true;
  @State selectedCardId: number = 0; // 当前选中的银行卡ID
  private isSelectMode: boolean = false; // 是否为选择模式（从其他页面跳转过来选择银行卡）
  @State viewMode: string = '已绑定'; // 视图模式：'所有卡片' | '已绑定'
  @State selectedCard: BankCard | null = null; // 选中查看详情的银行卡
  @State showCreditCardDetails: boolean = false; // 显示信用卡详情弹窗
  @State selectedCreditCard: BankCard | null = null; // 选中的信用卡

  aboutToAppear() {
    // 检查路由参数，判断是否为选择模式
    const params = router.getParams() as BankCardPageParams;
    this.isSelectMode = params?.selectMode === true;
    this.selectedCardId = params?.selectedCardId || 0;

    console.log('BankCardPage - 选择模式:', this.isSelectMode, '已选中卡片ID:', this.selectedCardId);
    this.loadBankCards();
  }

  onPageShow() {
    // 检查是否有银行卡添加事件
    const cardAdded = tempDataManager.getData('BANK_CARD_ADDED');
    console.log('BankCardPage onPageShow - 检查银行卡添加事件:', cardAdded);
    if (cardAdded) {
      console.log('BankCardPage - 检测到银行卡添加，重新加载银行卡列表');
      // 重新加载银行卡列表
      this.loadBankCards();
      // 不删除事件标志，让MainPage也能处理
    }

    // 检查是否有银行卡解绑事件
    const cardUnbound = tempDataManager.getData('BANK_CARD_UNBOUND');
    console.log('BankCardPage onPageShow - 检查银行卡解绑事件:', cardUnbound);
    if (cardUnbound) {
      console.log('BankCardPage - 检测到银行卡解绑，重新加载银行卡列表');
      // 重新加载银行卡列表
      this.loadBankCards();
      // 不删除事件标志，让MainPage也能处理
    }
  }

  async loadBankCards() {
    try {
      this.isLoading = true;

      // 从本地存储获取当前用户ID
      const cachedUserInfo = await storageManager.getUserInfo();
      if (!cachedUserInfo || !cachedUserInfo.userId) {
        console.error('无法获取用户ID');
        promptAction.showToast({ message: '用户信息获取失败' });
        return;
      }

      // 1. 加载所有银行卡（包括已解绑的历史记录）
      await this.loadAllCards(cachedUserInfo.userId);

      // 2. 加载当前有效的绑定卡
      await this.loadBoundCards(cachedUserInfo.userId);

      console.log('银行卡数据加载完成 - 所有卡片:', this.allCards.length, '已绑定:', this.boundCards.length);
    } catch (error) {
      console.error('加载银行卡列表失败:', error);
      promptAction.showToast({ message: '加载银行卡列表失败' });
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 加载所有银行卡（包括已解绑的历史记录）
   */
  async loadAllCards(userId: number) {
    try {
      const response = await httpClient.get<SpringBootBankCardResponse[]>(`/bank-card/user/${userId}/all`);
      const bankCardList: SpringBootBankCardResponse[] = response.data;
      this.allCards = this.convertSpringBootBankCardsToLocal(bankCardList);
    } catch (error) {
      console.error('加载所有银行卡失败:', error);
      // 如果后端不支持/all接口，回退到原有接口
      const response = await httpClient.get<SpringBootBankCardResponse[]>(`/bank-card/user/${userId}`);
      const bankCardList: SpringBootBankCardResponse[] = response.data;
      this.allCards = this.convertSpringBootBankCardsToLocal(bankCardList);
    }
  }

  /**
   * 加载当前有效的绑定卡
   */
  async loadBoundCards(userId: number) {
    try {
      const response = await httpClient.get<SpringBootBankCardResponse[]>(`/bank-card/user/${userId}/bound`);
      const bankCardList: SpringBootBankCardResponse[] = response.data;
      this.boundCards = this.convertSpringBootBankCardsToLocal(bankCardList);
    } catch (error) {
      console.error('加载已绑定银行卡失败:', error);
      // 如果后端不支持/bound接口，从所有卡片中筛选
      this.boundCards = this.allCards.filter(card => card.status === 1);
    }
  }



  build() {
    Column() {
      // 顶部导航
      Row() {
        Button() {
          Image($r('app.media.ic_back'))
            .width(24)
            .height(24)
            .fillColor('#333333')
        }
        .width(40)
        .height(40)
        .borderRadius(20)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          router.back();
        })

        Text(this.isSelectMode ? '选择银行卡' : '银行卡管理')
          .fontSize(18)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Button() {
          Image($r('app.media.ic_add'))
            .width(24)
            .height(24)
            .fillColor('#1976D2')
        }
        .width(40)
        .height(40)
        .borderRadius(20)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          router.pushUrl({
            url: 'pages/AddBankCardPage'
          });
        })
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })



      // 视图模式切换标签栏
      this.ViewModeTabsView()

      if (this.isLoading) {
        this.LoadingView()
      } else {
        this.CardContentView()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
    .bindSheet($$this.showCreditCardDetails, this.CreditCardDetailsDialog(), {
      height: 600,
      showClose: true,
      dragBar: true,
      onDisappear: () => {
        this.showCreditCardDetails = false;
        this.selectedCreditCard = null;
      }
    })
  }



  @Builder
  ViewModeTabsView() {
    Row() {
      ForEach(['所有卡片', '已绑定'], (tab: string) => {
        Button(tab)
          .fontSize(14)
          .fontColor(this.viewMode === tab ? '#FFFFFF' : '#666666')
          .backgroundColor(this.viewMode === tab ? '#1976D2' : '#F5F5F5')
          .borderRadius(20)
          .padding({ left: 16, right: 16, top: 8, bottom: 8 })
          .margin({ right: 8 })
          .onClick(() => {
            this.viewMode = tab;
          })
      })
    }
    .width('100%')
    .padding({ left: 16, right: 16, bottom: 16 })
  }

  @Builder
  LoadingView() {
    Column() {
      LoadingProgress()
        .width(40)
        .height(40)
        .color('#1976D2')
      
      Text('加载中...')
        .fontSize(14)
        .fontColor('#999999')
        .margin({ top: 16 })
    }
    .width('100%')
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  EmptyView() {
    Column() {
      Image($r('app.media.ic_empty_card'))
        .width(80)
        .height(80)
        .fillColor('#CCCCCC')
        .margin({ bottom: 16 })

      Text('暂无银行卡')
        .fontSize(16)
        .fontColor('#666666')
        .margin({ bottom: 8 })

      Text('添加银行卡后可进行充值、提现等操作')
        .fontSize(14)
        .fontColor('#999999')
        .textAlign(TextAlign.Center)
        .margin({ bottom: 24 })

      Button('添加银行卡')
        .fontSize(16)
        .fontColor(Color.White)
        .backgroundColor('#1976D2')
        .borderRadius(8)
        .padding({ left: 24, right: 24, top: 12, bottom: 12 })
        .onClick(() => {
          router.pushUrl({
            url: 'pages/AddBankCardPage'
          });
        })
    }
    .width('100%')
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .padding(40)
  }

  @Builder
  CardContentView() {
    const currentCards = this.getCurrentCards();

    if (currentCards.length === 0) {
      this.EmptyView()
    } else {
      this.CardListView(currentCards)
    }
  }

  /**
   * 根据当前视图模式获取要显示的卡片列表
   */
  getCurrentCards(): BankCard[] {
    switch (this.viewMode) {
      case '所有卡片':
        return this.allCards;
      case '已绑定':
        return this.boundCards;
      default:
        return this.boundCards;
    }
  }

  @Builder
  CardListView(cards: BankCard[]) {
    Column() {
      // 使用List组件支持右滑删除
      List() {
        ForEach(cards, (card: BankCard, index: number) => {
          this.BankCardItem(card, index)
        })
      }
      .layoutWeight(1)
      .scrollBar(BarState.Off)
      .padding({ left: 16, right: 16, top: 16, bottom: 20 })
      .divider({ strokeWidth: 0 }) // 移除分割线

      // 在选择模式下显示添加银行卡按钮
      if (this.isSelectMode) {
        Row() {
          Button('添加新银行卡')
            .fontSize(16)
            .fontColor('#1976D2')
            .backgroundColor('#E3F2FD')
            .borderRadius(8)
            .width('100%')
            .height(48)
            .onClick(() => {
              router.pushUrl({
                url: 'pages/AddBankCardPage'
              });
            })
        }
        .width('100%')
        .padding({ left: 16, right: 16, bottom: 16 })
        .backgroundColor('#FFFFFF')
      }
    }
    .width('100%')
    .height('100%')
  }



  @Builder
  BankCardItem(card: BankCard, index: number) {
    // 使用ListItem实现右滑删除功能
    ListItem() {
      // 银行卡主体内容
      this.BankCardContent(card)
    }
    .swipeAction({
      end: this.SwipeDeleteButton(card)
    })
    .margin({ bottom: 12 })
  }

  @Builder
  BankCardContent(card: BankCard) {
    Column() {
      // 真实银行卡展示
      this.BankCardDisplay(card)

      // 银行卡基本信息
      this.BankCardBasicInfo(card)

      // 操作按钮区域
      this.CardActionButtons(card)
    }
    .width('100%')
    .backgroundColor('#FFFFFF')
    .borderRadius(12)
    .padding(16)
    .shadow({
      radius: 4,
      color: 'rgba(0,0,0,0.1)',
      offsetX: 0,
      offsetY: 2
    })
    .onClick(() => {
      // 点击卡片查看详细信息
      this.showCardDetails(card);
    })
  }

  /**
   * 显示银行卡详细信息
   */
  showCardDetails(card: BankCard) {
    if (this.isSelectMode) {
      // 选择模式下，选择该卡片并返回
      this.selectCard(card);
    } else {
      // 查看模式下，显示详细信息
      this.selectedCard = card;
      if (card.cardType === BankCardType.CREDIT) {
        // 信用卡显示详细信息弹窗
        this.selectedCreditCard = card;
        this.showCreditCardDetails = true;
      } else {
        // 储蓄卡跳转到详情页面
        this.goToCardDetail(card);
      }
    }
  }

  @Builder
  BankCardBasicInfo(card: BankCard) {
    Column() {
      Row() {
        Text(card.bankName)
          .fontSize(16)
          .fontColor('#333333')
          .fontWeight(FontWeight.Bold)

        Blank()

        Text(card.cardType === BankCardType.CREDIT ? '信用卡' : '储蓄卡')
          .fontSize(12)
          .fontColor('#666666')
          .backgroundColor('#F5F5F5')
          .borderRadius(4)
          .padding({ left: 8, right: 8, top: 4, bottom: 4 })
      }
      .width('100%')
      .margin({ top: 12, bottom: 8 })

      Row() {
        Text(`**** **** **** ${card.cardNo.slice(-4)}`)
          .fontSize(14)
          .fontColor('#666666')

        Blank()

        Text(card.status === 1 ? '已绑定' : '已解绑')
          .fontSize(12)
          .fontColor(card.status === 1 ? '#4CAF50' : '#F44336')
      }
      .width('100%')
      .margin({ bottom: 8 })

      if (card.isDefault) {
        Text('默认卡')
          .fontSize(12)
          .fontColor('#1976D2')
          .backgroundColor('#E3F2FD')
          .borderRadius(4)
          .padding({ left: 8, right: 8, top: 4, bottom: 4 })
          .alignSelf(ItemAlign.Start)
      }
    }
  }

  @Builder
  CardActionButtons(card: BankCard) {
    Row() {
      Button('查看详情')
        .fontSize(12)
        .fontColor('#1976D2')
        .backgroundColor('#E3F2FD')
        .borderRadius(6)
        .padding({ left: 12, right: 12, top: 6, bottom: 6 })
        .onClick(() => {
          this.showCardDetails(card);
        })

      if (card.status === 1) {
        Button('解绑')
          .fontSize(12)
          .fontColor('#F44336')
          .backgroundColor('#FFEBEE')
          .borderRadius(6)
          .padding({ left: 12, right: 12, top: 6, bottom: 6 })
          .margin({ left: 8 })
          .onClick(() => {
            this.confirmUnbindCard(card);
          })
      } else {
        Button('重新绑定')
          .fontSize(12)
          .fontColor('#4CAF50')
          .backgroundColor('#E8F5E8')
          .borderRadius(6)
          .padding({ left: 12, right: 12, top: 6, bottom: 6 })
          .margin({ left: 8 })
          .onClick(() => {
            this.rebindCard(card);
          })
      }
    }
    .width('100%')
    .justifyContent(FlexAlign.End)
    .margin({ top: 12 })
  }

      // 解绑银行卡按钮
      Button('解绑银行卡')
        .fontSize(14)
        .fontColor('#F44336')
        .backgroundColor('#FFEBEE')
        .borderRadius(8)
        .width('100%')
        .height(40)
        .margin({ top: 16 })
        .onClick(() => {
          this.confirmDeleteCard(card);
        })
    }
    .width('100%')
    .backgroundColor('#FFFFFF')
    .borderRadius(12)
    .padding(16)
    .margin({ bottom: 16 })
    .shadow({
      radius: 4,
      color: 'rgba(0,0,0,0.1)',
      offsetX: 0,
      offsetY: 2
    })
  }

  @Builder
  BankCardDetailInfo(card: BankCard) {
    Column() {
      // 银行名称
      Row() {
        Text('银行名称')
          .fontSize(14)
          .fontColor('#666666')
          .width(80)

        Text(card.bankName)
          .fontSize(14)
          .fontColor('#333333')
          .layoutWeight(1)
      }
      .width('100%')
      .margin({ bottom: 8 })

      // 卡片类型
      Row() {
        Text('卡片类型')
          .fontSize(14)
          .fontColor('#666666')
          .width(80)

        Text(this.getCardTypeText(card.cardType))
          .fontSize(14)
          .fontColor('#333333')
          .layoutWeight(1)
      }
      .width('100%')
      .margin({ bottom: 8 })

      // 持卡人姓名
      Row() {
        Text('持卡人姓名')
          .fontSize(14)
          .fontColor('#666666')
          .width(80)

        Text(card.holderName)
          .fontSize(14)
          .fontColor('#333333')
          .layoutWeight(1)
      }
      .width('100%')
      .margin({ bottom: 8 })

      // 卡号
      Row() {
        Text('卡号')
          .fontSize(14)
          .fontColor('#666666')
          .width(80)

        Text(this.formatCardNumber(card.cardNo))
          .fontSize(14)
          .fontColor('#333333')
          .layoutWeight(1)
      }
      .width('100%')
      .margin({ bottom: 8 })

      // 绑定状态
      Row() {
        Text('绑定状态')
          .fontSize(14)
          .fontColor('#666666')
          .width(80)

        Text(card.isBound === BankCardStatus.BOUND ? '已绑定' : '未绑定')
          .fontSize(14)
          .fontColor(card.isBound === BankCardStatus.BOUND ? '#4CAF50' : '#F44336')
          .layoutWeight(1)
      }
      .width('100%')
      .margin({ bottom: 8 })

      // 绑定时间
      Row() {
        Text('绑定时间')
          .fontSize(14)
          .fontColor('#666666')
          .width(80)

        Text(card.createTime || '未绑定')
          .fontSize(14)
          .fontColor('#333333')
          .layoutWeight(1)
      }
      .width('100%')
    }
    .alignItems(HorizontalAlign.Start)
  }

  @Builder
  QuickActionsView(card: BankCard) {
    Row() {
      Button('支付')
        .fontSize(14)
        .fontColor('#FFFFFF')
        .backgroundColor('#4CAF50')
        .borderRadius(8)
        .width(80)
        .height(36)
        .onClick(() => {
          this.useCardForPayment(card);
        })

      Button('转账')
        .fontSize(14)
        .fontColor('#FFFFFF')
        .backgroundColor('#2196F3')
        .borderRadius(8)
        .width(80)
        .height(36)
        .margin({ left: 12 })
        .onClick(() => {
          this.useCardForTransfer(card);
        })
    }
    .width('100%')
    .justifyContent(FlexAlign.Start)
  }

  @Builder
  BankCardDisplay(card: BankCard) {
    // 真实银行卡设计
    Stack({ alignContent: Alignment.TopStart }) {
      Column() {
        // 银行卡顶部信息
        Row() {
          Text(card.bankName)
            .fontSize(16)
            .fontColor('#FFFFFF')
            .fontWeight(FontWeight.Bold)
            .layoutWeight(1)

          Text(`¥${this.getCardBalance(card)}`)
            .fontSize(18)
            .fontColor('#FFFFFF')
            .fontWeight(FontWeight.Bold)
        }
        .width('100%')
        .margin({ bottom: 20 })

        // 银行卡号
        Text(this.formatCardNumber(card.cardNo))
          .fontSize(16)
          .fontColor('#FFFFFF')
          .fontWeight(FontWeight.Medium)
          .letterSpacing(1)
          .alignSelf(ItemAlign.Start)
      }
      .width('100%')
      .height(120)
      .padding(16)
      .borderRadius(8)
      .linearGradient({
        direction: GradientDirection.Right,
        colors: this.getBankCardGradient(card.bankName)
      })

      // 银行卡装饰图案
      Image($r('app.media.ic_bank_card'))
        .width(60)
        .height(60)
        .fillColor('rgba(255,255,255,0.1)')
        .position({ x: '75%', y: '20%' })
    }
    .width('100%')
    .margin({ bottom: 16 })
  }

  @Builder
  BankCardInfoAndActions(card: BankCard) {
    Column() {
      // 银行卡标题和卡号
      Row() {
        Text(`${card.bankName} ${this.getCardTypeText(card.cardType)}`)
          .fontSize(16)
          .fontColor('#333333')
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)

        Text('管理')
          .fontSize(14)
          .fontColor('#1976D2')
          .onClick(() => {
            this.viewCardDetail(card);
          })
      }
      .width('100%')
      .margin({ bottom: 8 })

      Text(this.formatCardNumber(card.cardNo))
        .fontSize(14)
        .fontColor('#666666')
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })

      // 状态标签
      Row() {
        // 连接状态
        Text(card.isBound === BankCardStatus.BOUND ? '已连接' : '未连接')
          .fontSize(12)
          .fontColor('#FFFFFF')
          .backgroundColor(card.isBound === BankCardStatus.BOUND ? '#4CAF50' : '#999999')
          .borderRadius(12)
          .padding({ left: 8, right: 8, top: 4, bottom: 4 })
          .margin({ right: 8 })

        // 激活状态
        Text('已激活')
          .fontSize(12)
          .fontColor('#FFFFFF')
          .backgroundColor('#2196F3')
          .borderRadius(12)
          .padding({ left: 8, right: 8, top: 4, bottom: 4 })
          .margin({ right: 8 })

        // 默认卡标识
        if (card.isDefault) {
          Text('默认卡片')
            .fontSize(12)
            .fontColor('#FFFFFF')
            .backgroundColor('#FF9800')
            .borderRadius(12)
            .padding({ left: 8, right: 8, top: 4, bottom: 4 })
            .margin({ right: 8 })
        }

        // 卡片类型
        Text(this.getCardTypeText(card.cardType))
          .fontSize(12)
          .fontColor('#FFFFFF')
          .backgroundColor(card.cardType === BankCardType.CREDIT ? '#9C27B0' : '#607D8B')
          .borderRadius(12)
          .padding({ left: 8, right: 8, top: 4, bottom: 4 })
      }
      .width('100%')
      .margin({ bottom: 16 })

      // 操作按钮
      Row() {
        Button('详情')
          .fontSize(14)
          .fontColor('#1976D2')
          .backgroundColor('#E3F2FD')
          .borderRadius(6)
          .padding({ left: 12, right: 12, top: 8, bottom: 8 })
          .margin({ right: 8 })
          .onClick(() => {
            this.goToCardDetail(card);
          })

        // 信用卡详情按钮（仅信用卡显示）
        if (card.cardType === BankCardType.CREDIT) {
          Button('信用卡详情')
            .fontSize(14)
            .fontColor('#9C27B0')
            .backgroundColor('#F3E5F5')
            .borderRadius(6)
            .padding({ left: 12, right: 12, top: 8, bottom: 8 })
            .margin({ right: 8 })
            .onClick(() => {
              this.showCreditCardDetails = true;
              this.selectedCreditCard = card;
            })
        }

        Button(card.isBound === BankCardStatus.BOUND ? '取消激活' : '激活')
          .fontSize(14)
          .fontColor(card.isBound === BankCardStatus.BOUND ? '#FF5722' : '#4CAF50')
          .backgroundColor(card.isBound === BankCardStatus.BOUND ? '#FFEBEE' : '#E8F5E8')
          .borderRadius(6)
          .padding({ left: 12, right: 12, top: 8, bottom: 8 })
          .margin({ right: 8 })
          .onClick(() => {
            if (card.isBound === BankCardStatus.BOUND) {
              this.confirmUnbindCard(card);
            } else {
              this.bindCard(card);
            }
          })

        Button('使用')
          .fontSize(14)
          .fontColor('#FFFFFF')
          .backgroundColor('#4CAF50')
          .borderRadius(6)
          .padding({ left: 12, right: 12, top: 8, bottom: 8 })
          .margin({ right: 8 })
          .onClick(() => {
            this.showCardOptions(card);
          })

        Button('解除绑定')
          .fontSize(14)
          .fontColor('#F44336')
          .backgroundColor('#FFEBEE')
          .borderRadius(6)
          .padding({ left: 12, right: 12, top: 8, bottom: 8 })
          .onClick(() => {
            this.confirmDeleteCard(card);
          })
      }
      .width('100%')
      .justifyContent(FlexAlign.Start)
    }
    .alignItems(HorizontalAlign.Start)
  }

  @Builder
  SwipeDeleteButton(card: BankCard) {
    Button() {
      Column() {
        Text('🗑️')
          .fontSize(24)
          .fontColor('#FFFFFF')

        Text('删除')
          .fontSize(12)
          .fontColor('#FFFFFF')
          .margin({ top: 4 })
      }
    }
    .width(80)
    .height(180)
    .backgroundColor('#F44336')
    .borderRadius(12)
    .onClick(() => {
      this.confirmDeleteCard(card);
    })
  }

  /**
   * 使用银行卡进行支付
   */
  useCardForPayment(card: BankCard) {
    console.log('使用银行卡支付:', card.bankName, card.cardNo);

    // 跳转到支付页面
    router.pushUrl({
      url: 'pages/PaymentPage',
      params: {
        selectedCard: {
          cardId: card.cardId,
          bankName: card.bankName,
          cardNo: card.cardNo,
          cardType: card.cardType,
          holderName: card.holderName,
          maskedCardNo: card.maskedCardNo
        }
      }
    });
  }

  /**
   * 使用银行卡进行转账
   */
  useCardForTransfer(card: BankCard) {
    console.log('使用银行卡转账:', card.bankName, card.cardNo);

    // 跳转到钱包操作页面（转账功能）
    router.pushUrl({
      url: 'pages/WalletOperationPage',
      params: {
        operationType: 'transfer',
        selectedCard: {
          cardId: card.cardId,
          bankName: card.bankName,
          cardNo: card.cardNo,
          cardType: card.cardType,
          holderName: card.holderName,
          maskedCardNo: card.maskedCardNo
        }
      }
    });
  }

  /**
   * 选择银行卡
   */
  selectCard(card: BankCard) {
    this.selectedCardId = card.cardId;
    console.log('选择银行卡:', card.bankName, 'ID:', card.cardId);

    if (this.isSelectMode) {
      // 选择模式下，返回选中的银行卡信息
      router.back({
        url: '',
        params: {
          selectedCard: {
            cardId: card.cardId,
            bankName: card.bankName,
            cardNo: card.cardNo,
            cardType: card.cardType,
            holderName: card.holderName,
            isDefault: card.isDefault,
            maskedCardNo: card.maskedCardNo
          }
        }
      });
    }
  }

  /**
   * 查看银行卡详情
   */
  viewCardDetail(card: BankCard) {
    console.log('查看银行卡详情:', card.bankName, card.cardNo);

    // 显示银行卡操作选项
    this.showCardOptions(card);
  }

  showCardOptions(card: BankCard) {
    this.goToCardDetail(card);
  }

  /**
   * 跳转到银行卡详情页面
   */
  goToCardDetail(card: BankCard) {
    router.pushUrl({
      url: 'pages/BankCardDetailPage',
      params: {
        cardId: card.cardId
      }
    }).catch((error: Error) => {
      console.error('跳转银行卡详情页面失败:', error);
      promptAction.showToast({ message: '打开详情页面失败' });
    });
  }

  /**
   * 重新绑定银行卡
   */
  async rebindCard(card: BankCard) {
    try {
      const result = await promptAction.showDialog({
        title: '重新绑定',
        message: `确定要重新绑定银行卡 ${card.bankName}(${card.cardNo.slice(-4)}) 吗？`,
        buttons: [
          { text: '确定', color: '#1976D2' },
          { text: '取消', color: '#666666' }
        ]
      });

      if (result.index === 0) {
        await this.bindCard(card);
      }
    } catch (error) {
      console.error('显示重新绑定确认对话框失败:', error);
    }
  }

  /**
   * 确认解绑银行卡
   */
  async confirmUnbindCard(card: BankCard) {
    try {
      const result = await promptAction.showDialog({
        title: '确认解绑',
        message: `确定要解绑银行卡 ${card.bankName}(${card.cardNo.slice(-4)}) 吗？`,
        buttons: [
          { text: '确定', color: '#F44336' },
          { text: '取消', color: '#666666' }
        ]
      });

      if (result.index === 0) {
        await this.unbindCard(card);
      }
    } catch (error) {
      console.error('显示确认对话框失败:', error);
    }
  }

  /**
   * 设置默认银行卡
   */
  async setDefaultCard(card: BankCard) {
    try {
      // 从本地存储获取当前用户ID
      const cachedUserInfo = await storageManager.getUserInfo();
      if (!cachedUserInfo || !cachedUserInfo.userId) {
        console.error('无法获取用户ID');
        promptAction.showToast({ message: '用户信息获取失败' });
        return;
      }

      // 调用SpringBoot3后端API设置默认银行卡
      const setDefaultRequest: SpringBootSetDefaultCardRequest = {};
      await httpClient.put<string>(`/bank-card/set-default/${card.cardId}?userId=${cachedUserInfo.userId}`, setDefaultRequest);
      promptAction.showToast({ message: '设置默认卡成功' });

      // 通知其他页面银行卡已更新
      console.log('BankCardPage - 设置银行卡更新事件标志');
      tempDataManager.setData('BANK_CARD_UPDATED', true);

      // 重新加载银行卡列表
      await this.loadBankCards();
    } catch (error) {
      console.error('设置默认卡失败:', error);
      promptAction.showToast({ message: '设置默认卡失败，请重试' });
    }
  }

  /**
   * 解绑银行卡
   */
  async unbindCard(card: BankCard) {
    try {
      // 从本地存储获取当前用户ID
      const cachedUserInfo = await storageManager.getUserInfo();
      if (!cachedUserInfo || !cachedUserInfo.userId) {
        console.error('无法获取用户ID');
        promptAction.showToast({ message: '用户信息获取失败' });
        return;
      }

      // 调用SpringBoot3后端API解绑银行卡
      const unbindRequest: SpringBootUnbindBankCardRequest = {};
      await httpClient.put<number>(`/bank-card/unbind/${card.cardId}?userId=${cachedUserInfo.userId}`, unbindRequest);
      promptAction.showToast({ message: '解绑成功' });

      // 通知其他页面银行卡已解绑
      console.log('BankCardPage - 设置银行卡解绑事件标志');
      tempDataManager.setData('BANK_CARD_UNBOUND', true);

      // 重新加载银行卡列表
      await this.loadBankCards();
    } catch (error) {
      console.error('解绑银行卡失败:', error);
      promptAction.showToast({ message: '解绑失败，请重试' });
    }
  }

  /**
   * 转换SpringBoot3银行卡到本地格式
   */
  private convertSpringBootBankCardsToLocal(bankCardList: SpringBootBankCardResponse[]): BankCard[] {
    return bankCardList.map((card: SpringBootBankCardResponse): BankCard => ({
      cardId: card.cardId || 0,
      userId: card.userId || 0,
      cardNo: card.cardNumber || '',
      cardType: this.mapBankCardType(card.cardType),
      bankName: card.bankName || '',
      holderName: card.cardHolder || '',
      isBound: card.status === 1 ? BankCardStatus.BOUND : BankCardStatus.UNBOUND,
      createTime: card.createdAt || card.createTime || '',
      updateTime: card.updatedAt || card.updateTime || '',
      maskedCardNo: card.cardNumber ? this.maskCardNo(card.cardNumber) : undefined,
      isDefault: card.isDefault === 1 || card.isDefault === true // 处理默认卡标识
    }));
  }

  /**
   * 映射银行卡类型
   */
  private mapBankCardType(cardType: string | number | undefined): BankCardType {
    if (typeof cardType === 'number') {
      // 数字类型：1=储蓄卡，2=信用卡
      return cardType === 2 ? BankCardType.CREDIT : BankCardType.DEBIT;
    } else if (typeof cardType === 'string') {
      // 字符串类型
      if (cardType === '信用卡' || cardType === 'CREDIT') {
        return BankCardType.CREDIT;
      }
    }
    // 默认返回储蓄卡
    return BankCardType.DEBIT;
  }

  /**
   * 脱敏银行卡号
   */
  private maskCardNo(cardNo: string): string {
    if (!cardNo || cardNo.length < 8) return cardNo;
    return cardNo.replace(/(\d{4})\d*(\d{4})/, '$1****$2');
  }

  /**
   * 格式化银行卡号显示（带空格分隔）
   */
  private formatCardNumber(cardNo: string): string {
    if (!cardNo) return '';

    // 脱敏处理
    const maskedCardNo = this.maskCardNo(cardNo);

    // 添加空格分隔，每4位一组
    return maskedCardNo.replace(/(.{4})/g, '$1 ').trim();
  }

  /**
   * 获取卡片类型文本
   */
  private getCardTypeText(cardType: BankCardType): string {
    switch (cardType) {
      case BankCardType.CREDIT:
        return '信用卡';
      case BankCardType.DEBIT:
        return '储蓄卡';
      default:
        return '储蓄卡';
    }
  }

  /**
   * 获取银行卡渐变色
   */
  private getBankCardGradient(bankName: string): Array<[string, number]> {
    const gradients: Record<string, Array<[string, number]>> = {
      '中国工商银行': [['#C41E3A', 0], ['#8B0000', 1]],
      '中国建设银行': [['#003DA5', 0], ['#001F5C', 1]],
      '中国农业银行': [['#00A651', 0], ['#006B35', 1]],
      '中国银行': [['#B8860B', 0], ['#8B6914', 1]],
      '招商银行': [['#DC143C', 0], ['#8B0000', 1]],
      '交通银行': [['#0066CC', 0], ['#003D7A', 1]],
      '中信银行': [['#FF6B35', 0], ['#CC4125', 1]],
      '光大银行': [['#9932CC', 0], ['#6A1B9A', 1]],
      '华夏银行': [['#FF1744', 0], ['#C62828', 1]],
      '民生银行': [['#00BCD4', 0], ['#0097A7', 1]],
      '广发银行': [['#FF5722', 0], ['#D84315', 1]],
      '平安银行': [['#FF9800', 0], ['#F57C00', 1]]
    };

    return gradients[bankName] || [['#1976D2', 0], ['#0D47A1', 1]];
  }

  /**
   * 确认删除银行卡
   */
  async confirmDeleteCard(card: BankCard) {
    try {
      const result = await promptAction.showDialog({
        title: '确认删除',
        message: `确定要删除银行卡 ${card.bankName}(${card.cardNo.slice(-4)}) 吗？\n删除后将无法恢复。`,
        buttons: [
          { text: '确定删除', color: '#F44336' },
          { text: '取消', color: '#666666' }
        ]
      });

      if (result.index === 0) {
        await this.deleteCard(card);
      }
    } catch (error) {
      console.error('显示删除确认对话框失败:', error);
    }
  }

  /**
   * 删除银行卡
   */
  async deleteCard(card: BankCard) {
    try {
      // 从本地存储获取当前用户ID
      const cachedUserInfo = await storageManager.getUserInfo();
      if (!cachedUserInfo || !cachedUserInfo.userId) {
        console.error('无法获取用户ID');
        promptAction.showToast({ message: '用户信息获取失败' });
        return;
      }

      // 调用SpringBoot3后端API删除银行卡
      await httpClient.delete<number>(`/bank-card/${card.cardId}?userId=${cachedUserInfo.userId}`);
      promptAction.showToast({ message: '删除成功' });

      // 通知其他页面银行卡已删除
      console.log('BankCardPage - 设置银行卡删除事件标志');
      tempDataManager.setData('BANK_CARD_DELETED', true);

      // 重新加载银行卡列表
      await this.loadBankCards();
    } catch (error) {
      console.error('删除银行卡失败:', error);
      promptAction.showToast({ message: '删除失败，请重试' });
    }
  }





  /**
   * 绑定银行卡
   */
  async bindCard(card: BankCard) {
    try {
      // 从本地存储获取当前用户ID
      const cachedUserInfo = await storageManager.getUserInfo();
      if (!cachedUserInfo || !cachedUserInfo.userId) {
        console.error('无法获取用户ID');
        promptAction.showToast({ message: '用户信息获取失败' });
        return;
      }

      // 调用SpringBoot3后端API绑定银行卡
      const bindRequest: Record<string, never> = {};
      await httpClient.put<number>(`/bank-card/bind/${card.cardId}?userId=${cachedUserInfo.userId}`, bindRequest);
      promptAction.showToast({ message: '绑定成功' });

      // 通知其他页面银行卡已绑定
      console.log('BankCardPage - 设置银行卡绑定事件标志');
      tempDataManager.setData('BANK_CARD_BOUND', true);

      // 重新加载银行卡列表
      await this.loadBankCards();
    } catch (error) {
      console.error('绑定银行卡失败:', error);
      promptAction.showToast({ message: '绑定失败，请重试' });
    }
  }

  /**
   * 获取银行卡余额（模拟数据）
   */
  private getCardBalance(card: BankCard): string {
    // 这里可以从后端API获取真实余额，现在使用模拟数据
    const balances: Record<number, string> = {
      1: '1,000',
      2: '5,280',
      3: '12,500',
      4: '850'
    };
    return balances[card.cardId] || '0';
  }

  @Builder
  CreditCardDetailsDialog() {
    if (this.selectedCreditCard) {
      Column() {
        Text('信用卡详细信息')
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })

        // 信用卡基本信息
        Column() {
          this.CreditCardInfoItem('银行名称', this.selectedCreditCard.bankName)
          this.CreditCardInfoItem('卡号', this.formatCardNumber(this.selectedCreditCard.cardNo))
          this.CreditCardInfoItem('卡片类型', '信用卡')
          this.CreditCardInfoItem('持卡人', this.selectedCreditCard.holderName || '未设置')
          this.CreditCardInfoItem('有效期', '12/28')
          this.CreditCardInfoItem('CVV', '***')
        }
        .width('100%')
        .padding(16)
        .margin({ bottom: 16 })
        .borderRadius(12)
        .backgroundColor('#F8F9FA')

        // 信用卡额度信息
        Column() {
          Text('额度信息')
            .fontSize(16)
            .fontWeight(FontWeight.Medium)
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 12 })

          this.CreditCardInfoItem('信用额度', '¥50,000.00')
          this.CreditCardInfoItem('可用额度', '¥35,280.00')
          this.CreditCardInfoItem('已用额度', '¥14,720.00')
          this.CreditCardInfoItem('最低还款', '¥1,472.00')
          this.CreditCardInfoItem('账单日', '每月5日')
          this.CreditCardInfoItem('还款日', '每月25日')
        }
        .width('100%')
        .padding(16)
        .margin({ bottom: 16 })
        .borderRadius(12)
        .backgroundColor('#F8F9FA')

        // 操作按钮
        Row() {
          Button('还款')
            .fontSize(14)
            .fontColor('#FFFFFF')
            .backgroundColor('#4CAF50')
            .borderRadius(8)
            .layoutWeight(1)
            .margin({ right: 8 })
            .onClick(() => {
              promptAction.showToast({ message: '信用卡还款功能开发中' });
            })

          Button('分期')
            .fontSize(14)
            .fontColor('#FFFFFF')
            .backgroundColor('#FF9800')
            .borderRadius(8)
            .layoutWeight(1)
            .margin({ left: 8 })
            .onClick(() => {
              promptAction.showToast({ message: '信用卡分期功能开发中' });
            })
        }
        .width('100%')
        .margin({ bottom: 16 })

        Button('关闭')
          .width('100%')
          .height(48)
          .fontSize(16)
          .fontColor('#FFFFFF')
          .backgroundColor('#1976D2')
          .borderRadius(8)
          .onClick(() => {
            this.showCreditCardDetails = false;
            this.selectedCreditCard = null;
          })
      }
      .width('100%')
      .padding(20)
    }
  }

  @Builder
  CreditCardInfoItem(label: string, value: string) {
    Row() {
      Text(label)
        .fontSize(14)
        .fontColor('#666666')
        .layoutWeight(1)

      Text(value)
        .fontSize(14)
        .fontColor('#333333')
        .fontWeight(FontWeight.Medium)
    }
    .width('100%')
    .padding({ top: 8, bottom: 8 })
  }
}
