import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import {
  UserLoginRequest,
  SendCodeRequest,
  SpringBootPasswordLoginRequest,
  SpringBootSmsLoginRequest,
  SpringBootUserResponse
} from '../common/types/index';
import { storageManager, LocalUserInfo } from '../common/storage/StorageManager';
import { httpClient } from '../common/http/HttpClient';
import { common } from '@kit.AbilityKit';

@Entry
@Component
struct LoginPage {
  @State phone: string = '';
  @State password: string = '';
  @State code: string = '';
  @State isLoading: boolean = false;
  @State currentTab: number = 0; // 0: 密码登录, 1: 验证码登录
  @State countdown: number = 0; // 验证码倒计时
  @State canSendCode: boolean = true; // 是否可以发送验证码
  @State showNetworkSettings: boolean = false; // 显示网络设置

  async aboutToAppear() {
    // 页面即将出现时的初始化逻辑
    console.log('LoginPage aboutToAppear');

    // 确保StorageManager已初始化
    await this.ensureStorageInitialized();
  }

  /**
   * 确保StorageManager已初始化
   */
  private async ensureStorageInitialized() {
    try {
      // 尝试获取context
      const context = getContext(this) as common.UIAbilityContext;

      // 初始化StorageManager（如果还没有初始化）
      await storageManager.init(context);
      console.log('StorageManager 初始化成功');
    } catch (error) {
      console.error('StorageManager 初始化失败:', error);
      // 如果初始化失败，可以显示提示但不阻止用户使用
      promptAction.showToast({ message: '存储初始化失败，部分功能可能受影响' });
    }
  }

  build() {
    Column() {
      // 顶部网络设置按钮
      Row() {
        Blank()
        Button('网络设置')
          .fontSize(14)
          .fontColor('#1976D2')
          .backgroundColor(Color.Transparent)
          .border({ width: 1, color: '#1976D2', radius: 16 })
          .height(32)
          .padding({ left: 12, right: 12 })
          .onClick(() => {
            this.showNetworkSettings = true;
          })
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 16 })

      // 顶部Logo区域
      Column() {
        Image($r('app.media.app_icon'))
          .width(80)
          .height(80)
          .margin({ bottom: 16 });

        Text('E-Wallet')
          .fontSize(28)
          .fontWeight(FontWeight.Bold)
          .fontColor('#1976D2')
          .margin({ bottom: 8 });

        Text('欢迎使用电子钱包')
          .fontSize(16)
          .fontColor('#666666');
      }
      .margin({ top: 40, bottom: 60 });

      // 登录表单
      Column() {
        // Tab切换
        Row() {
          Text('密码登录')
            .fontSize(16)
            .fontColor(this.currentTab === 0 ? '#1976D2' : '#666666')
            .fontWeight(this.currentTab === 0 ? FontWeight.Bold : FontWeight.Normal)
            .layoutWeight(1)
            .textAlign(TextAlign.Center)
            .padding({ top: 12, bottom: 12 })
            .onClick(() => {
              this.currentTab = 0;
            });

          Text('验证码登录')
            .fontSize(16)
            .fontColor(this.currentTab === 1 ? '#1976D2' : '#666666')
            .fontWeight(this.currentTab === 1 ? FontWeight.Bold : FontWeight.Normal)
            .layoutWeight(1)
            .textAlign(TextAlign.Center)
            .padding({ top: 12, bottom: 12 })
            .onClick(() => {
              this.currentTab = 1;
            });
        }
        .width('100%')
        .margin({ bottom: 20 });

        // Tab指示器
        Row() {
          Divider()
            .width('50%')
            .height(2)
            .color(this.currentTab === 0 ? '#1976D2' : '#E0E0E0');

          Divider()
            .width('50%')
            .height(2)
            .color(this.currentTab === 1 ? '#1976D2' : '#E0E0E0');
        }
        .width('100%')
        .margin({ bottom: 30 });

        // 手机号输入框（两种登录方式都需要）
        Column() {
          Text('手机号')
            .fontSize(14)
            .fontColor('#333333')
            .alignSelf(ItemAlign.Start)
            .margin({ bottom: 8 });

          TextInput({ placeholder: '请输入手机号', text: this.phone })
            .type(InputType.PhoneNumber)
            .maxLength(11)
            .fontSize(16)
            .height(48)
            .borderRadius(8)
            .backgroundColor('#F8F9FA')
            .border({ width: 1, color: '#E0E0E0' })
            .onChange((value: string) => {
              this.phone = value;
            });
        }
        .alignItems(HorizontalAlign.Start)
        .margin({ bottom: 20 });

        // 密码输入框（仅密码登录时显示）
        if (this.currentTab === 0) {
          Column() {
            Text('密码')
              .fontSize(14)
              .fontColor('#333333')
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 });

            TextInput({ placeholder: '请输入密码', text: this.password })
              .type(InputType.Password)
              .fontSize(16)
              .height(48)
              .borderRadius(8)
              .backgroundColor('#F8F9FA')
              .border({ width: 1, color: '#E0E0E0' })
              .showPasswordIcon(true)
              .onChange((value: string) => {
                this.password = value;
              });
          }
          .alignItems(HorizontalAlign.Start)
          .margin({ bottom: 30 });
        }

        // 验证码输入框（仅验证码登录时显示）
        if (this.currentTab === 1) {
          Column() {
            Text('验证码')
              .fontSize(14)
              .fontColor('#333333')
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 });

            Row() {
              TextInput({ placeholder: '请输入验证码', text: this.code })
                .type(InputType.Number)
                .maxLength(6)
                .fontSize(16)
                .height(48)
                .borderRadius(8)
                .backgroundColor('#F8F9FA')
                .border({ width: 1, color: '#E0E0E0' })
                .layoutWeight(1)
                .onChange((value: string) => {
                  this.code = value;
                });

              Button(this.countdown > 0 ? `${this.countdown}s` : '获取验证码')
                .fontSize(12)
                .fontColor(this.canSendCode ? '#1976D2' : '#999999')
                .backgroundColor(this.canSendCode ? '#E3F2FD' : '#F5F5F5')
                .width(100)
                .height(48)
                .enabled(this.canSendCode && this.phone.length === 11)
                .onClick(() => {
                  this.sendSmsCode();
                });
            }
          }
          .alignItems(HorizontalAlign.Start)
          .margin({ bottom: 30 });
        }

        // 登录按钮
        Button(this.currentTab === 0 ? '密码登录' : '验证码登录')
          .width('100%')
          .height(48)
          .fontSize(16)
          .fontColor(Color.White)
          .backgroundColor('#1976D2')
          .borderRadius(8)
          .enabled(this.isLoginEnabled())
          .opacity(this.isLoginEnabled() ? 1 : 0.5)
          .onClick(() => {
            this.handleLogin();
          });

        // 忘记密码链接（仅密码登录时显示）
        if (this.currentTab === 0) {
          Row() {
            Text('忘记密码？')
              .fontSize(14)
              .fontColor('#1976D2')
              .onClick(() => {
                router.pushUrl({
                  url: 'pages/ForgotPasswordPage'
                }).catch((error: Error) => {
                  console.error('跳转忘记密码页面失败:', error);
                  promptAction.showToast({ message: `页面跳转失败: ${error.message}` });
                });
              });
          }
          .width('100%')
          .justifyContent(FlexAlign.End)
          .margin({ top: 16 });
        }

        // 注册链接
        Row() {
          Text('还没有账号？')
            .fontSize(14)
            .fontColor('#666666');

          Text('立即注册')
            .fontSize(14)
            .fontColor('#1976D2')
            .onClick(() => {
              router.pushUrl({
                url: 'pages/RegisterPage'
              }).catch((error: Error) => {
                console.error('跳转注册页面失败:', error);
                promptAction.showToast({ message: `页面跳转失败: ${error.message}` });
              });
            });
        }
        .margin({ top: 20 });
      }
      .width('100%')
      .padding({ left: 24, right: 24 });

      Blank();

      // 底部信息
      Text('E-Wallet v1.0.0')
        .fontSize(12)
        .fontColor('#999999')
        .margin({ bottom: 20 });
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#FFFFFF')
    .bindSheet($$this.showNetworkSettings, this.NetworkSettingsSheet(), {
      height: 400,
      showClose: true,
      dragBar: true,
      onDisappear: () => {
        this.showNetworkSettings = false;
      }
    });
  }

  /**
   * 判断是否可以登录
   */
  isLoginEnabled(): boolean {
    if (!this.phone || this.phone.length !== 11 || this.isLoading) {
      return false;
    }

    if (this.currentTab === 0) {
      // 密码登录：需要密码
      return !!(this.password && this.password.length >= 6);
    } else {
      // 验证码登录：需要验证码
      return !!(this.code && this.code.length === 6);
    }
  }

  /**
   * 发送短信验证码
   */
  async sendSmsCode() {
    if (!this.canSendCode || this.phone.length !== 11) {
      return;
    }

    try {
      // 根据SpringBoot3后端接口格式调整参数
      const requestData: SendCodeRequest = {
        phone: this.phone,
        type: 1  // 1-登录验证码，2-操作验证码
      };

      const response = await httpClient.post<string>('/user/send-code', requestData);

      // 后端直接返回验证码，显示给用户（开发环境）
      if (response.data) {
        promptAction.showToast({ message: `验证码: ${response.data}` });
      } else {
        promptAction.showToast({ message: '验证码发送成功' });
      }

      // 开始倒计时
      this.startCountdown();

    } catch (error) {
      console.error('发送验证码失败:', error);
      let errorMessage = '发送验证码失败';
      if (error instanceof Error) {
        errorMessage = `发送失败: ${error.message}`;
        console.error('详细错误信息:', error);
      }
      promptAction.showToast({ message: errorMessage });
    }
  }

  /**
   * 开始倒计时
   */
  startCountdown() {
    this.canSendCode = false;
    this.countdown = 60;

    const timer = setInterval(() => {
      this.countdown--;
      if (this.countdown <= 0) {
        clearInterval(timer);
        this.canSendCode = true;
        this.countdown = 0;
      }
    }, 1000);
  }

  /**
   * 处理登录
   */
  async handleLogin() {
    if (this.isLoading) return;

    // 表单验证
    if (this.phone.length !== 11) {
      promptAction.showToast({ message: '请输入正确的手机号' });
      return;
    }

    if (this.currentTab === 0) {
      // 密码登录验证
      if (this.password.length < 6) {
        promptAction.showToast({ message: '密码长度不能少于6位' });
        return;
      }
    } else {
      // 验证码登录验证
      if (this.code.length !== 6) {
        promptAction.showToast({ message: '请输入6位验证码' });
        return;
      }
    }

    this.isLoading = true;

    try {
      let loginResponse: SpringBootUserResponse;

      if (this.currentTab === 0) {
        // 密码登录 - 调整为SpringBoot3后端格式
        const loginData: SpringBootPasswordLoginRequest = {
          phone: this.phone,
          password: this.password,
          loginType: 'password'
        };
        const apiResponse = await httpClient.post<SpringBootUserResponse>('/user/login', loginData);
        loginResponse = apiResponse.data;
      } else {
        // 验证码登录 - 调整为SpringBoot3后端格式
        const smsLoginData: SpringBootSmsLoginRequest = {
          phone: this.phone,
          verificationCode: this.code,
          loginType: 'sms'
        };
        const apiResponse = await httpClient.post<SpringBootUserResponse>('/user/login', smsLoginData);
        loginResponse = apiResponse.data;
      }

      // SpringBoot3后端返回的是User对象，需要适配
      // 生成一个简单的token（实际项目中应该由后端提供）
      const token = `token_${loginResponse.userId}_${Date.now()}`;

      // 保存token和用户信息到本地存储
      await storageManager.saveUserToken(token);
      const localUserInfo = this.convertToLocalUserInfo(loginResponse);
      await storageManager.saveUserInfo(localUserInfo);
      await storageManager.saveLoginPhone(this.phone);

      // 设置HTTP客户端的token
      httpClient.setAuthToken(token);

      promptAction.showToast({ message: '登录成功' });

      // 跳转到主页
      router.replaceUrl({
        url: 'pages/MainPage'
      }).catch((error: Error) => {
        console.error('跳转主页失败:', error);
        promptAction.showToast({ message: `页面跳转失败: ${error.message}` });
      });

    } catch (error) {
      console.error('登录失败:', error);
      let errorMessage = this.currentTab === 0 ? '登录失败，请检查账号密码' : '登录失败，请检查验证码';
      if (error instanceof Error) {
        errorMessage = `登录失败: ${error.message}`;
        console.error('详细错误信息:', error);
      }
      promptAction.showToast({ message: errorMessage });
    } finally {
      this.isLoading = false;
    }
  }

  private convertToLocalUserInfo(userInfo: SpringBootUserResponse): LocalUserInfo {
    return {
      userId: userInfo.userId,
      phone: userInfo.phone,
      realName: userInfo.realName || '',
      idCard: userInfo.idCard || '',
      walletNo: '',
      balance: 0,
      payLimit: userInfo.payLimit || 1000,
      status: userInfo.status,
      createTime: userInfo.createdAt || userInfo.createTime || '',
      updateTime: userInfo.updatedAt || userInfo.updateTime || ''
    };
  }

  @Builder
  NetworkSettingsSheet() {
    Column() {
      Text('网络连接设置')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 20 })

      Column() {
        Text('服务器地址')
          .fontSize(14)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 8 })

        TextInput({ placeholder: '请输入服务器地址', text: 'http://localhost:8096' })
          .fontSize(16)
          .height(48)
          .borderRadius(8)
          .backgroundColor('#F8F9FA')
          .border({ width: 1, color: '#E0E0E0' })
          .onChange((value: string) => {
            // 更新服务器地址
            httpClient.setBaseUrl(value);
          })

        Text('默认: http://localhost:8096')
          .fontSize(12)
          .fontColor('#999999')
          .alignSelf(ItemAlign.Start)
          .margin({ top: 4, bottom: 20 })

        Row() {
          Button('重置默认')
            .fontSize(14)
            .fontColor('#666666')
            .backgroundColor('#F5F5F5')
            .borderRadius(8)
            .layoutWeight(1)
            .margin({ right: 8 })
            .onClick(() => {
              httpClient.setBaseUrl('http://localhost:8096');
              promptAction.showToast({ message: '已重置为默认地址' });
            })

          Button('测试连接')
            .fontSize(14)
            .fontColor('#FFFFFF')
            .backgroundColor('#1976D2')
            .borderRadius(8)
            .layoutWeight(1)
            .margin({ left: 8 })
            .onClick(() => {
              this.testConnection();
            })
        }
        .width('100%')
        .margin({ bottom: 20 })

        Button('保存设置')
          .width('100%')
          .height(48)
          .fontSize(16)
          .fontColor('#FFFFFF')
          .backgroundColor('#1976D2')
          .borderRadius(8)
          .onClick(() => {
            this.showNetworkSettings = false;
            promptAction.showToast({ message: '网络设置已保存' });
          })
      }
      .padding(20)
    }
    .width('100%')
  }

  /**
   * 测试网络连接
   */
  async testConnection() {
    try {
      const response = await httpClient.get<string>('/test/ping');
      promptAction.showToast({ message: '连接成功' });
    } catch (error) {
      console.error('连接测试失败:', error);
      promptAction.showToast({ message: '连接失败，请检查网络设置' });
    }
  }
}