if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface AboutPage_Params {
    appInfo?: AppInfo;
    features?: FeatureItem[];
}
import router from "@ohos:router";
class AboutPage extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.appInfo = {
            name: 'E-Wallet',
            version: 'v1.2.0',
            buildNumber: '20250619',
            description: '安全便捷的数字钱包应用',
            email: '<EMAIL>',
            phone: '************'
        };
        this.features = [
            { icon: '🔒', title: '安全可靠', desc: '银行级安全加密，保护您的资金安全' },
            { icon: '⚡', title: '快速便捷', desc: '秒级转账，实时到账，操作简单' },
            { icon: '💳', title: '多卡管理', desc: '支持多张银行卡绑定，灵活管理' },
            { icon: '📊', title: '明细清晰', desc: '详细的交易记录，资金流向一目了然' }
        ];
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: AboutPage_Params) {
        if (params.appInfo !== undefined) {
            this.appInfo = params.appInfo;
        }
        if (params.features !== undefined) {
            this.features = params.features;
        }
    }
    updateStateVars(params: AboutPage_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
    }
    aboutToBeDeleted() {
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    // 应用信息
    private appInfo: AppInfo;
    // 功能特色
    private features: FeatureItem[];
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AboutPage.ets(28:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor('#F5F5F5');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 顶部导航栏
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AboutPage.ets(30:7)", "entry");
            // 顶部导航栏
            Row.width('100%');
            // 顶部导航栏
            Row.height(56);
            // 顶部导航栏
            Row.padding({ left: 16, right: 16 });
            // 顶部导航栏
            Row.backgroundColor('#FFFFFF');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithLabel('返回');
            Button.debugLine("entry/src/main/ets/pages/AboutPage.ets(31:9)", "entry");
            Button.fontSize(16);
            Button.fontColor('#1976D2');
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => {
                router.back();
            });
        }, Button);
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('关于我们');
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(39:9)", "entry");
            Text.fontSize(18);
            Text.fontWeight(FontWeight.Medium);
            Text.layoutWeight(1);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(45:9)", "entry");
            Text.width(60);
        }, Text);
        Text.pop();
        // 顶部导航栏
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Scroll.create();
            Scroll.debugLine("entry/src/main/ets/pages/AboutPage.ets(53:7)", "entry");
            Scroll.layoutWeight(1);
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AboutPage.ets(54:9)", "entry");
            Column.padding(16);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 应用Logo和基本信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AboutPage.ets(56:11)", "entry");
            // 应用Logo和基本信息
            Column.width('100%');
            // 应用Logo和基本信息
            Column.padding(40);
            // 应用Logo和基本信息
            Column.borderRadius(12);
            // 应用Logo和基本信息
            Column.backgroundColor('#FFFFFF');
            // 应用Logo和基本信息
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // Logo
            Image.create({ "id": 16777239, "type": 20000, params: [], "bundleName": "com.example.harmony", "moduleName": "entry" });
            Image.debugLine("entry/src/main/ets/pages/AboutPage.ets(58:13)", "entry");
            // Logo
            Image.width(80);
            // Logo
            Image.height(80);
            // Logo
            Image.borderRadius(16);
            // Logo
            Image.margin({ bottom: 16 });
        }, Image);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.appInfo.name);
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(64:13)", "entry");
            Text.fontSize(28);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Bold);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(this.appInfo.description);
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(70:13)", "entry");
            Text.fontSize(16);
            Text.fontColor('#666666');
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AboutPage.ets(75:13)", "entry");
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`版本 ${this.appInfo.version}`);
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(76:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#999999');
            Text.margin({ right: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(`构建号 ${this.appInfo.buildNumber}`);
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(81:15)", "entry");
            Text.fontSize(14);
            Text.fontColor('#999999');
        }, Text);
        Text.pop();
        Row.pop();
        // 应用Logo和基本信息
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 功能特色
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AboutPage.ets(93:11)", "entry");
            // 功能特色
            Column.width('100%');
            // 功能特色
            Column.padding(20);
            // 功能特色
            Column.borderRadius(12);
            // 功能特色
            Column.backgroundColor('#FFFFFF');
            // 功能特色
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('功能特色');
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(94:13)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const feature = _item;
                this.FeatureItem.bind(this)(feature);
            };
            this.forEachUpdateFunction(elmtId, this.features, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        // 功能特色
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 联系我们
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AboutPage.ets(112:11)", "entry");
            // 联系我们
            Column.width('100%');
            // 联系我们
            Column.padding(20);
            // 联系我们
            Column.borderRadius(12);
            // 联系我们
            Column.backgroundColor('#FFFFFF');
            // 联系我们
            Column.margin({ bottom: 16 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('联系我们');
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(113:13)", "entry");
            Text.fontSize(18);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ bottom: 16 });
        }, Text);
        Text.pop();
        this.InfoRow.bind(this)('客服邮箱', this.appInfo.email);
        this.InfoRow.bind(this)('客服热线', this.appInfo.phone);
        // 联系我们
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 版权信息
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AboutPage.ets(130:11)", "entry");
            // 版权信息
            Column.width('100%');
            // 版权信息
            Column.padding(20);
            // 版权信息
            Column.borderRadius(12);
            // 版权信息
            Column.backgroundColor('#FFFFFF');
            // 版权信息
            Column.margin({ bottom: 20 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('© 2024 E-Wallet Technology Co., Ltd.');
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(131:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.textAlign(TextAlign.Center);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('All rights reserved.');
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(137:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.textAlign(TextAlign.Center);
            Text.margin({ bottom: 8 });
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('本应用已通过国家信息安全等级保护认证');
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(143:13)", "entry");
            Text.fontSize(12);
            Text.fontColor('#999999');
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        // 版权信息
        Column.pop();
        Column.pop();
        Scroll.pop();
        Column.pop();
    }
    FeatureItem(feature: FeatureItem, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AboutPage.ets(165:5)", "entry");
            Row.width('100%');
            Row.padding({ top: 12, bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(feature.icon);
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(166:7)", "entry");
            Text.fontSize(24);
            Text.width(40);
            Text.textAlign(TextAlign.Center);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/AboutPage.ets(171:7)", "entry");
            Column.layoutWeight(1);
            Column.alignItems(HorizontalAlign.Start);
            Column.margin({ left: 12 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(feature.title);
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(172:9)", "entry");
            Text.fontSize(16);
            Text.fontColor('#333333');
            Text.fontWeight(FontWeight.Medium);
            Text.alignSelf(ItemAlign.Start);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(feature.desc);
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(178:9)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.alignSelf(ItemAlign.Start);
            Text.margin({ top: 4 });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    InfoRow(label: string, value: string, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/AboutPage.ets(194:5)", "entry");
            Row.width('100%');
            Row.margin({ bottom: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(label);
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(195:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#666666');
            Text.width(80);
        }, Text);
        Text.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create(value);
            Text.debugLine("entry/src/main/ets/pages/AboutPage.ets(200:7)", "entry");
            Text.fontSize(14);
            Text.fontColor('#333333');
            Text.layoutWeight(1);
        }, Text);
        Text.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "AboutPage";
    }
}
interface AppInfo {
    name: string;
    version: string;
    buildNumber: string;
    description: string;
    email: string;
    phone: string;
}
interface FeatureItem {
    icon: string;
    title: string;
    desc: string;
}
registerNamedRoute(() => new AboutPage(undefined, {}), "", { bundleName: "com.example.harmony", moduleName: "entry", pagePath: "pages/AboutPage", pageFullPath: "entry/src/main/ets/pages/AboutPage", integratedHsp: "false", moduleType: "followWithHap" });
