package com.icss.springbootbig.dto;

import lombok.Data;
import java.util.Date;

@Data
public class UserInfoResponse {
    private Integer userId;
    private String phone;
    private String username;
    private Integer status;
    private Date lastLogin;
    private Date createdAt;
    private Date updatedAt;
    // 注意：不包含不存在的字段如 payPassword, realName, idCard, email, avatar, loginAttempts, lastLoginTime, fingerprintEnabled

    /**
     * 从User实体转换为UserInfoResponse，只包含数据库中实际存在的字段
     */
    public static UserInfoResponse fromUser(com.icss.springbootbig.entity.User user) {
        UserInfoResponse response = new UserInfoResponse();
        response.setUserId(user.getUserId());
        response.setPhone(user.getPhone());
        response.setUsername(user.getUsername());
        response.setStatus(user.getStatus());
        response.setLastLogin(user.getLastLogin());
        response.setCreatedAt(user.getCreatedAt());
        response.setUpdatedAt(user.getUpdatedAt());
        return response;
    }
}
