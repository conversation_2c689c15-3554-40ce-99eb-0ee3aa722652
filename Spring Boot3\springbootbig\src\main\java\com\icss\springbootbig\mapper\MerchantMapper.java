package com.icss.springbootbig.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.icss.springbootbig.entity.Merchant;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface MerchantMapper extends BaseMapper<Merchant> {
    @Select("SELECT * FROM merchants WHERE merchant_no = #{merchantNo}")
    Merchant selectByMerchantNo(String merchantNo);
}