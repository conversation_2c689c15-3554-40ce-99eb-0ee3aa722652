package com.icss.springbootbig.controller;

import com.icss.springbootbig.dto.LoginResponse;
import com.icss.springbootbig.dto.UserInfoResponse;
import com.icss.springbootbig.entity.User;
import com.icss.springbootbig.result.R;
import com.icss.springbootbig.service.NewUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@CrossOrigin(origins = {"http://localhost:5173", "http://127.0.0.1:5173"})
@RestController
@RequestMapping("/api/user")
public class UserController {

    @Autowired
    private NewUserService userService;

    /**
     * 发送验证码
     */
    @PostMapping("/send-code")
    public R<String> sendVerificationCode(@RequestBody Map<String, Object> request) {
        try {
            String phone = (String) request.get("phone");
            Integer type = (Integer) request.get("type");

            String result = userService.sendVerificationCode(phone, type);
            // 直接返回验证码，不显示发送成功消息
            return R.success("", result);
        } catch (Exception e) {
            return R.failure("发送验证码失败: " + e.getMessage());
        }
    }

    /**
     * 验证验证码（独立接口）
     */
    @PostMapping("/verify-code")
    public R<Boolean> verifyCode(@RequestBody Map<String, Object> request) {
        try {
            String phone = (String) request.get("phone");
            String verificationCode = (String) request.get("verificationCode");
            Integer loginType = (Integer) request.get("loginType");

            // loginType: 1-登录验证码, 2-操作验证码
            Integer type = (loginType != null && loginType == 2) ? 2 : 1;

            boolean isValid = userService.verifyCode(phone, verificationCode, type);

            if (isValid) {
                return R.success("验证成功", true);
            } else {
                return R.failure("验证码无效或已过期");
            }
        } catch (Exception e) {
            return R.failure("验证失败: " + e.getMessage());
        }
    }



    /**
     * 用户登录（支持密码登录和验证码登录）
     */
    @PostMapping("/login")
    public R<LoginResponse> login(@RequestBody Map<String, Object> request) {
        try {
            String phone = (String) request.get("phone");
            String loginType = (String) request.get("loginType");

            User user;
            if ("password".equals(loginType)) {
                String password = (String) request.get("password");
                user = userService.loginWithPassword(phone, password);
            } else if ("sms".equals(loginType)) {
                String verificationCode = (String) request.get("verificationCode");
                user = userService.loginWithSms(phone, verificationCode);
            } else {
                return R.failure("不支持的登录方式");
            }

            // 转换为LoginResponse，只返回数据库中存在的字段
            LoginResponse loginResponse = LoginResponse.fromUser(user);
            return R.success("登录成功", loginResponse);
        } catch (Exception e) {
            return R.failure("登录失败: " + e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public R<User> register(@RequestBody User user) {
        try {
            User newUser = userService.register(user);
            return R.success("注册成功", newUser);
        } catch (Exception e) {
            return R.failure("注册失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/{userId}")
    public R<UserInfoResponse> getUserInfo(@PathVariable Integer userId) {
        try {
            User user = userService.getUserById(userId);
            // 转换为UserInfoResponse，只返回数据库中存在的字段
            UserInfoResponse response = UserInfoResponse.fromUser(user);
            return R.success("查询成功", response);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{userId}")
    public R<User> updateUserInfo(@PathVariable Integer userId, @RequestBody User user) {
        try {
            user.setUserId(userId);
            User updatedUser = userService.updateUser(user);
            return R.success("更新成功", updatedUser);
        } catch (Exception e) {
            return R.failure("更新失败: " + e.getMessage());
        }
    }

    /**
     * 修改登录密码 (支持路径参数)
     */
    @PutMapping("/change-password/{userId}")
    public R<String> changePassword(@PathVariable Integer userId, @RequestBody Map<String, Object> request) {
        try {
            String oldPassword = (String) request.get("oldPassword");
            String newPassword = (String) request.get("newPassword");

            userService.changePassword(userId, oldPassword, newPassword);
            return R.success("密码修改成功");
        } catch (Exception e) {
            return R.failure("密码修改失败: " + e.getMessage());
        }
    }

    /**
     * 修改登录密码 (兼容原有接口)
     */
    @PutMapping("/change-password")
    public R<String> changePasswordLegacy(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            String oldPassword = (String) request.get("oldPassword");
            String newPassword = (String) request.get("newPassword");

            userService.changePassword(userId, oldPassword, newPassword);
            return R.success("密码修改成功");
        } catch (Exception e) {
            return R.failure("密码修改失败: " + e.getMessage());
        }
    }

    /**
     * 设置支付密码
     */
    @PutMapping("/set-pay-password")
    public R<String> setPayPassword(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            String payPassword = (String) request.get("payPassword");

            userService.setPayPassword(userId, payPassword);
            return R.success("支付密码设置成功");
        } catch (Exception e) {
            return R.failure("支付密码设置失败: " + e.getMessage());
        }
    }

    /**
     * 修改支付密码 (支持路径参数)
     */
    @PutMapping("/pay-password/{userId}")
    public R<String> changePayPasswordWithPath(@PathVariable Integer userId, @RequestBody Map<String, Object> request) {
        try {
            String oldPayPassword = (String) request.get("payPassword");
            String newPayPassword = (String) request.get("newPayPassword");

            userService.changePayPassword(userId, oldPayPassword, newPayPassword);
            return R.success("支付密码修改成功");
        } catch (Exception e) {
            return R.failure("支付密码修改失败: " + e.getMessage());
        }
    }

    /**
     * 修改支付密码 (兼容原有接口)
     */
    @PutMapping("/change-pay-password")
    public R<String> changePayPassword(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            String oldPayPassword = (String) request.get("oldPayPassword");
            String newPayPassword = (String) request.get("newPayPassword");

            userService.changePayPassword(userId, oldPayPassword, newPayPassword);
            return R.success("支付密码修改成功");
        } catch (Exception e) {
            return R.failure("支付密码修改失败: " + e.getMessage());
        }
    }

    /**
     * 设置支付限额
     */
    @PutMapping("/set-limits")
    public R<String> setPaymentLimits(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            userService.setPaymentLimits(userId, request);
            return R.success("限额设置成功");
        } catch (Exception e) {
            return R.failure("限额设置失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用指纹登录
     */
    @PutMapping("/fingerprint/{userId}")
    public R<String> setFingerprintEnabled(@PathVariable Integer userId, @RequestParam Boolean enabled) {
        try {
            userService.setFingerprintEnabled(userId, enabled);
            return R.success(enabled ? "指纹登录已启用" : "指纹登录已禁用");
        } catch (Exception e) {
            return R.failure("设置失败: " + e.getMessage());
        }
    }

    /**
     * 上传用户头像
     */
    @PostMapping("/upload-avatar/{userId}")
    public R<String> uploadAvatar(@PathVariable Integer userId, @RequestParam("file") MultipartFile file) {
        try {
            String avatarUrl = userService.uploadAvatar(userId, file);
            return R.success("头像上传成功", avatarUrl);
        } catch (Exception e) {
            return R.failure("头像上传失败: " + e.getMessage());
        }
    }

    /**
     * 冻结用户账户
     */
    @PutMapping("/freeze/{userId}")
    public R<String> freezeAccount(@PathVariable Integer userId, @RequestParam String reason) {
        try {
            userService.freezeAccount(userId, reason);
            return R.success("账户已冻结");
        } catch (Exception e) {
            return R.failure("冻结失败: " + e.getMessage());
        }
    }

    /**
     * 解冻用户账户
     */
    @PutMapping("/unfreeze/{userId}")
    public R<String> unfreezeAccount(@PathVariable Integer userId) {
        try {
            userService.unfreezeAccount(userId);
            return R.success("账户已解冻");
        } catch (Exception e) {
            return R.failure("解冻失败: " + e.getMessage());
        }
    }

    /**
     * 注销用户账户
     */
    @DeleteMapping("/deactivate/{userId}")
    public R<String> deactivateAccount(@PathVariable Integer userId, @RequestParam String payPassword) {
        try {
            userService.deactivateAccount(userId, payPassword);
            return R.success("账户已注销");
        } catch (Exception e) {
            return R.failure("注销失败: " + e.getMessage());
        }
    }

    /**
     * 更新个人资料
     */
    @PutMapping("/profile/{userId}")
    public R<LoginResponse> updateProfile(@PathVariable Integer userId, @RequestBody Map<String, Object> profileData) {
        try {
            User updatedUser = userService.updateProfile(userId, profileData);
            // 转换为LoginResponse，只返回数据库中存在的字段
            LoginResponse response = LoginResponse.fromUser(updatedUser);
            return R.success("资料更新成功", response);
        } catch (Exception e) {
            return R.failure("更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户账户状态信息
     */
    @GetMapping("/status/{userId}")
    public R<Map<String, Object>> getAccountStatus(@PathVariable Integer userId) {
        try {
            Map<String, Object> status = userService.getAccountStatus(userId);
            return R.success("查询成功", status);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户完整账户信息（包括钱包和银行卡）
     */
    @GetMapping("/complete-info/{userId}")
    public R<Map<String, Object>> getCompleteUserInfo(@PathVariable Integer userId) {
        try {
            Map<String, Object> completeInfo = userService.getCompleteUserInfo(userId);
            return R.success("查询成功", completeInfo);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询用户列表
     */
    @GetMapping("/page")
    public R<com.baomidou.mybatisplus.core.metadata.IPage<User>> getUsersByPage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) Integer status) {
        try {
            com.baomidou.mybatisplus.core.metadata.IPage<User> page = userService.getUsersByPage(pageNum, pageSize, phone, username, status);
            return R.success("查询成功", page);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/status/{userId}")
    public R<String> updateUserStatus(@PathVariable Integer userId, @RequestBody Map<String, Object> request) {
        try {
            Integer status = (Integer) request.get("status");
            userService.updateUserStatus(userId, status);
            return R.success("状态更新成功");
        } catch (Exception e) {
            return R.failure("状态更新失败: " + e.getMessage());
        }
    }
}