import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { Transaction, TransactionQueryParams, PageResult, TransactionType, TransactionStatus, PaymentMethod, SpringBootTransactionResponse } from '../common/types/index';
import { httpClient } from '../common/http/HttpClient';
import { storageManager } from '../common/storage/StorageManager';

@Entry
@Component
struct TransactionListPage {
  @State transactions: Transaction[] = [];
  @State isLoading: boolean = false;
  @State isRefreshing: boolean = false;
  @State hasMore: boolean = true;
  @State currentPage: number = 1;
  @State pageSize: number = 20;
  @State selectedType: string = '全部交易'; // 当前选中的交易类型
  @State showFilterDialog: boolean = false;

  // 筛选条件
  @State filterType: string = '';
  @State filterStartDate: string = '';
  @State filterEndDate: string = '';

  // 交易类型选项 - 按照用户要求的5种记录类型
  private transactionTypes: string[] = ['全部交易', '支付记录', '充值记录', '提现记录', '转账记录', '收钱记录'];

  aboutToAppear() {
    this.loadTransactions(true);
  }

  async loadTransactions(refresh: boolean = false) {
    if (this.isLoading) return;

    if (refresh) {
      this.currentPage = 1;
      this.hasMore = true;
      this.isRefreshing = true;
    } else {
      this.isLoading = true;
    }

    try {
      // 从本地存储获取当前用户ID
      const cachedUserInfo = await storageManager.getUserInfo();
      if (!cachedUserInfo || !cachedUserInfo.userId) {
        console.error('无法获取用户ID');
        promptAction.showToast({ message: '用户信息获取失败' });
        return;
      }

      // 调用SpringBoot3后端API获取交易记录
      let url = `/transaction/user/${cachedUserInfo.userId}?page=${this.currentPage}&size=${this.pageSize}`;

      // 添加类型筛选 - 按照5种记录类型映射
      if (this.selectedType && this.selectedType !== '全部交易') {
        const typeMap: Record<string, number> = {
          '支付记录': 5, // 消费/支付
          '充值记录': 1, // 充值
          '提现记录': 2, // 提现
          '转账记录': 3, // 转账
          '收钱记录': 4  // 收款
        };
        const typeId = typeMap[this.selectedType];
        if (typeId) {
          url += `&type=${typeId}`;
        }
      }

      // 添加时间范围筛选
      if (this.filterStartDate) {
        url += `&startDate=${this.filterStartDate}`;
      }
      if (this.filterEndDate) {
        url += `&endDate=${this.filterEndDate}`;
      }

      const response = await httpClient.get<SpringBootTransactionResponse[]>(url);
      const transactionList: SpringBootTransactionResponse[] = response.data;

      // 转换为本地格式
      const localTransactions = this.convertSpringBootTransactionsToLocal(transactionList);

      if (refresh) {
        this.transactions = localTransactions;
      } else {
        this.transactions = [...this.transactions, ...localTransactions];
      }

      // 简单分页处理
      this.hasMore = localTransactions.length === this.pageSize;
      this.currentPage++;

    } catch (error) {
      console.error('获取交易记录失败:', error);
      promptAction.showToast({ message: '获取交易记录失败' });
    } finally {
      this.isLoading = false;
      this.isRefreshing = false;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button() {
          Image($r('app.media.ic_back'))
            .width(24)
            .height(24)
            .fillColor('#333333')
        }
        .width(40)
        .height(40)
        .borderRadius(20)
        .backgroundColor(Color.Transparent)
        .onClick(() => {
          router.back();
        })

        Text('交易记录')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)
          .fontColor('#333333')

        Row() {
          Text('筛选')
            .fontSize(14)
            .fontColor('#1976D2')
            .margin({ right: 8 })
            .onClick(() => {
              this.showFilterDialog = true;
            })

          Text('查看全部')
            .fontSize(14)
            .fontColor('#1976D2')
            .onClick(() => {
              this.viewAllTransactions();
            })
        }
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')

      // 交易类型筛选标签栏
      this.TransactionTypeTabsView()

      // 交易记录列表
      this.TransactionListView()
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
    .bindSheet($$this.showFilterDialog, this.FilterDialog(), {
      height: 400,
      showClose: true,
      dragBar: true,
      onDisappear: () => {
        this.showFilterDialog = false;
      }
    })
  }

  @Builder
  TransactionTypeTabsView() {
    Row() {
      ForEach(this.transactionTypes, (type: string) => {
        Button(type)
          .fontSize(14)
          .fontColor(this.selectedType === type ? '#FFFFFF' : '#666666')
          .backgroundColor(this.selectedType === type ? '#1976D2' : '#F8F9FA')
          .borderRadius(20)
          .height(36)
          .padding({ left: 16, right: 16 })
          .margin({ right: 8 })
          .onClick(() => {
            this.selectedType = type;
            this.loadTransactions(true);
          })
      })
    }
    .width('100%')
    .padding({ left: 16, right: 16, top: 12, bottom: 12 })
    .backgroundColor('#FFFFFF')
  }

  @Builder
  TransactionListView() {
    if (this.transactions.length === 0 && !this.isLoading) {
      Column() {
        Text('📄')
          .fontSize(60)
          .fontColor('#CCCCCC')
          .margin({ bottom: 16 })

        Text('暂无交易记录')
          .fontSize(16)
          .fontColor('#999999')
          .margin({ bottom: 20 })

        Button('去充值')
          .fontSize(14)
          .fontColor('#1976D2')
          .backgroundColor('#E3F2FD')
          .borderRadius(8)
          .padding({ left: 20, right: 20, top: 8, bottom: 8 })
          .onClick(() => {
            router.pushUrl({
              url: 'pages/WalletOperationPage',
              params: { operationType: 'recharge' }
            });
          })
      }
      .width('100%')
      .layoutWeight(1)
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
      .backgroundColor('#F5F5F5')
    } else {
      List() {
        ForEach(this.transactions, (transaction: Transaction, index: number) => {
          ListItem() {
            this.TransactionItem(transaction)
          }
          .onClick(() => {
            this.viewTransactionDetail(transaction);
          })
        })

        // 加载更多
        if (this.hasMore) {
          ListItem() {
            Row() {
              if (this.isLoading) {
                LoadingProgress()
                  .width(20)
                  .height(20)
                  .color('#1976D2')
                  .margin({ right: 8 })
              }
              Text(this.isLoading ? '加载中...' : '点击加载更多')
                .fontSize(14)
                .fontColor('#666666')
            }
            .width('100%')
            .height(50)
            .justifyContent(FlexAlign.Center)
            .onClick(() => {
              if (!this.isLoading) {
                this.loadTransactions(false);
              }
            })
          }
        }
      }
      .layoutWeight(1)
      .backgroundColor('#F5F5F5')
      .padding({ left: 16, right: 16, top: 8 })
      .onReachEnd(() => {
        if (this.hasMore && !this.isLoading) {
          this.loadTransactions(false);
        }
      })
    }
  }

  @Builder
  TransactionItem(transaction: Transaction) {
    Row() {
      // 交易类型图标
      Column() {
        Text(this.getTransactionIcon(transaction.transactionType))
          .fontSize(20)
          .fontColor('#FFFFFF')
      }
      .width(40)
      .height(40)
      .justifyContent(FlexAlign.Center)
      .backgroundColor(this.getTransactionIconBg(transaction.transactionType))
      .borderRadius(20)

      Column() {
        Row() {
          Text(this.getTransactionTypeText(transaction.transactionType))
            .fontSize(16)
            .fontColor('#333333')
            .fontWeight(FontWeight.Medium)
            .layoutWeight(1)

          Text(this.formatAmount(transaction))
            .fontSize(16)
            .fontColor(this.getAmountColor(transaction))
            .fontWeight(FontWeight.Medium)
        }
        .width('100%')

        Row() {
          Text(this.formatDateTime(transaction.createTime))
            .fontSize(12)
            .fontColor('#999999')
            .layoutWeight(1)

          Text(this.getTransactionStatusText(transaction.status))
            .fontSize(12)
            .fontColor(this.getStatusColor(transaction.status))
        }
        .width('100%')
        .margin({ top: 4 })

        // 支付方式和支付途径信息
        Row() {
          Text(this.getPaymentMethodText(transaction.paymentMethod))
            .fontSize(11)
            .fontColor('#1976D2')
            .backgroundColor('#E3F2FD')
            .borderRadius(4)
            .padding({ left: 6, right: 6, top: 2, bottom: 2 })
            .margin({ right: 6 })

          if (transaction.paymentChannel) {
            Text(this.getPaymentChannelText(transaction.paymentChannel))
              .fontSize(11)
              .fontColor('#4CAF50')
              .backgroundColor('#E8F5E8')
              .borderRadius(4)
              .padding({ left: 6, right: 6, top: 2, bottom: 2 })
              .margin({ right: 6 })
          }

          if (transaction.description) {
            Text(transaction.description)
              .fontSize(11)
              .fontColor('#666666')
              .maxLines(1)
              .textOverflow({ overflow: TextOverflow.Ellipsis })
              .layoutWeight(1)
          }
        }
        .width('100%')
        .margin({ top: 6 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)
      .margin({ left: 12 })
    }
    .width('100%')
    .padding(16)
    .margin({ bottom: 1 })
    .backgroundColor('#FFFFFF')
  }

  getTransactionIcon(type: TransactionType): string {
    switch (type) {
      case TransactionType.RECHARGE: return '💰'; // 充值记录
      case TransactionType.WITHDRAW: return '💸'; // 提现记录
      case TransactionType.TRANSFER: return '📤'; // 转账记录
      case TransactionType.RECEIVE: return '📥'; // 收钱记录
      case TransactionType.PAYMENT: return '💳'; // 支付记录
      case TransactionType.REFUND: return '↩️'; // 退款
      default: return '📄';
    }
  }

  getTransactionIconBg(type: TransactionType): string {
    switch (type) {
      case TransactionType.RECHARGE: return '#4CAF50';
      case TransactionType.WITHDRAW: return '#2196F3';
      case TransactionType.TRANSFER: return '#FF9800';
      case TransactionType.RECEIVE: return '#4CAF50';
      case TransactionType.PAYMENT: return '#9C27B0';
      case TransactionType.REFUND: return '#607D8B';
      default: return '#999999';
    }
  }

  formatAmount(transaction: Transaction): string {
    // 收入类型：充值、收款、退款
    const isIncome = transaction.transactionType === TransactionType.RECHARGE ||
                     transaction.transactionType === TransactionType.RECEIVE ||
                     transaction.transactionType === TransactionType.REFUND;
    const prefix = isIncome ? '+' : '-';
    return `${prefix}¥${transaction.amount.toFixed(2)}`;
  }

  getAmountColor(transaction: Transaction): string {
    // 收入类型显示绿色，支出类型显示黑色
    const isIncome = transaction.transactionType === TransactionType.RECHARGE ||
                     transaction.transactionType === TransactionType.RECEIVE ||
                     transaction.transactionType === TransactionType.REFUND;
    return isIncome ? '#4CAF50' : '#333333';
  }

  getStatusColor(status: TransactionStatus): string {
    switch (status) {
      case TransactionStatus.SUCCESS: return '#4CAF50';
      case TransactionStatus.FAILED: return '#F44336';
      case TransactionStatus.PENDING: return '#FF9800';
      default: return '#666666';
    }
  }

  /**
   * 获取交易类型显示文本
   */
  getTransactionTypeText(type: TransactionType): string {
    switch (type) {
      case TransactionType.RECHARGE: return '充值记录';
      case TransactionType.WITHDRAW: return '提现记录';
      case TransactionType.TRANSFER: return '转账记录';
      case TransactionType.RECEIVE: return '收钱记录';
      case TransactionType.PAYMENT: return '支付记录';
      case TransactionType.REFUND: return '退款记录';
      default: return '其他记录';
    }
  }

  /**
   * 获取交易状态显示文本
   */
  getTransactionStatusText(status: TransactionStatus): string {
    switch (status) {
      case TransactionStatus.SUCCESS: return '成功';
      case TransactionStatus.FAILED: return '失败';
      case TransactionStatus.PENDING: return '处理中';
      default: return '未知';
    }
  }

  formatDateTime(dateTime: string): string {
    const date = new Date(dateTime);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false });
    } else if (diffDays === 1) {
      return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false });
    } else {
      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false });
    }
  }

  viewTransactionDetail(transaction: Transaction) {
    router.pushUrl({
      url: 'pages/TransactionDetailPage',
      params: {
        transactionId: transaction.transactionId
      }
    });
  }

  viewAllTransactions() {
    // 查看全部交易记录
    this.selectedType = '全部交易';
    this.loadTransactions(true);
  }

  /**
   * 转换SpringBoot3交易记录到本地格式
   */
  private convertSpringBootTransactionsToLocal(transactionList: SpringBootTransactionResponse[]): Transaction[] {
    return transactionList.map((transaction: SpringBootTransactionResponse): Transaction => ({
      transactionId: transaction.transactionId || 0,
      transactionNo: transaction.transactionNo || '',
      fromUserId: transaction.fromUserId || 0,
      toUserId: transaction.toUserId || 0,
      amount: transaction.amount || 0,
      transactionType: this.mapTransactionType(transaction.type),
      paymentMethod: this.mapPaymentMethod(transaction.paymentMethod),
      description: transaction.description || '',
      status: this.mapTransactionStatus(transaction.status),
      createTime: transaction.createdAt || transaction.createTime || ''
    }));
  }

  /**
   * 映射交易类型
   */
  private mapTransactionType(type: number | undefined): TransactionType {
    switch (type) {
      case 1: return TransactionType.RECHARGE;
      case 2: return TransactionType.WITHDRAW;
      case 3: return TransactionType.TRANSFER;
      case 4: return TransactionType.RECEIVE;
      case 5: return TransactionType.PAYMENT;
      default: return TransactionType.PAYMENT;
    }
  }

  /**
   * 映射支付方式
   */
  private mapPaymentMethod(method: number | undefined): PaymentMethod {
    switch (method) {
      case 1: return PaymentMethod.WALLET;
      case 2: return PaymentMethod.BANK_CARD;
      case 3: return PaymentMethod.THIRD_PARTY;
      case 4: return PaymentMethod.THIRD_PARTY;
      default: return PaymentMethod.WALLET;
    }
  }

  /**
   * 映射交易状态
   */
  private mapTransactionStatus(status: number | undefined): TransactionStatus {
    switch (status) {
      case 1: return TransactionStatus.SUCCESS;
      case 0: return TransactionStatus.FAILED;
      case 2: return TransactionStatus.PENDING;
      default: return TransactionStatus.SUCCESS;
    }
  }

  @Builder
  FilterDialog() {
    Column() {
      Text('筛选条件')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .margin({ bottom: 20 })

      // 时间范围筛选
      Column() {
        Text('时间范围')
          .fontSize(16)
          .fontColor('#333333')
          .alignSelf(ItemAlign.Start)
          .margin({ bottom: 12 })

        Row() {
          Column() {
            Text('开始日期')
              .fontSize(14)
              .fontColor('#666666')
              .margin({ bottom: 8 })

            TextInput({ placeholder: '选择开始日期', text: this.filterStartDate })
              .fontSize(14)
              .borderRadius(8)
              .backgroundColor('#F8F9FA')
              .onChange((value: string) => {
                this.filterStartDate = value;
              })
          }
          .layoutWeight(1)
          .margin({ right: 8 })

          Column() {
            Text('结束日期')
              .fontSize(14)
              .fontColor('#666666')
              .margin({ bottom: 8 })

            TextInput({ placeholder: '选择结束日期', text: this.filterEndDate })
              .fontSize(14)
              .borderRadius(8)
              .backgroundColor('#F8F9FA')
              .onChange((value: string) => {
                this.filterEndDate = value;
              })
          }
          .layoutWeight(1)
          .margin({ left: 8 })
        }
        .width('100%')
      }
      .width('100%')
      .margin({ bottom: 20 })

      // 操作按钮
      Row() {
        Button('重置')
          .fontSize(14)
          .fontColor('#666666')
          .backgroundColor('#F8F9FA')
          .borderRadius(8)
          .layoutWeight(1)
          .margin({ right: 8 })
          .onClick(() => {
            this.resetFilter();
          })

        Button('确定')
          .fontSize(14)
          .fontColor('#FFFFFF')
          .backgroundColor('#1976D2')
          .borderRadius(8)
          .layoutWeight(1)
          .margin({ left: 8 })
          .onClick(() => {
            this.applyFilter();
          })
      }
      .width('100%')
    }
    .width('100%')
    .padding(20)
  }

  /**
   * 重置筛选条件
   */
  resetFilter() {
    this.filterStartDate = '';
    this.filterEndDate = '';
    this.showFilterDialog = false;
    this.loadTransactions(true);
  }

  /**
   * 应用筛选条件
   */
  applyFilter() {
    this.showFilterDialog = false;
    this.loadTransactions(true);
  }

  /**
   * 获取支付方式文本
   */
  getPaymentMethodText(paymentMethod: PaymentMethod): string {
    switch (paymentMethod) {
      case PaymentMethod.WALLET:
        return '钱包支付';
      case PaymentMethod.BANK_CARD:
        return '银行卡';
      case PaymentMethod.THIRD_PARTY:
        return '第三方支付';
      default:
        return '未知';
    }
  }

  /**
   * 获取支付途径文本
   */
  getPaymentChannelText(channel: string): string {
    switch (channel) {
      case 'QR_CODE':
        return '扫码支付';
      case 'NFC':
        return 'NFC支付';
      case 'MERCHANT':
        return '商户支付';
      case 'ONLINE':
        return '在线支付';
      case 'TRANSFER':
        return '转账';
      default:
        return channel || '其他';
    }
  }
}
