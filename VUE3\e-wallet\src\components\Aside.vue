<template>
  <div class="sidebar">
    <div class="logo">
      <div class="logo-icon">💳</div>
      <h1>E-Wallet</h1>
      <p class="logo-subtitle">数字钱包</p>
    </div>
    <el-menu
      :default-active="activeMenu"
      class="el-menu-vertical-demo"
      router
      background-color="transparent"
      text-color="#ffffff"
      active-text-color="#ffffff"
    >
      <el-menu-item index="/dashboard">
        <el-icon><DataBoard /></el-icon>
        <span>首页</span>
      </el-menu-item>
      <el-menu-item index="/wallet">
        <el-icon><Wallet /></el-icon>
        <span>我的钱包</span>
      </el-menu-item>
      <el-menu-item index="/payment">
        <el-icon><CreditCard /></el-icon>
        <span>支付中心</span>
      </el-menu-item>
      <el-menu-item index="/bankcard">
        <el-icon><CreditCard /></el-icon>
        <span>银行卡管理</span>
      </el-menu-item>
      <el-menu-item index="/transaction">
        <el-icon><Document /></el-icon>
        <span>交易记录</span>
      </el-menu-item>
      <el-menu-item index="/settings">
        <el-icon><Setting /></el-icon>
        <span>个人设置</span>
      </el-menu-item>
    </el-menu>
  </div>
</template>
  
<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import {
  DataBoard,
  Document,
  CreditCard,
  Setting,
  Wallet
} from '@element-plus/icons-vue'

const router = useRouter()
const activeMenu = ref('')

onMounted(() => {
  activeMenu.value = router.currentRoute.value.path
})

// 监听路由变化，更新活跃菜单
watch(() => router.currentRoute.value.path, (newPath) => {
  activeMenu.value = newPath
})
</script>
  
<style scoped>
.sidebar {
  width: 240px;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  height: 100vh;
  overflow-y: auto;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.logo {
  padding: 30px 20px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.logo-icon {
  font-size: 50px;
  margin-bottom: 12px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.logo h1 {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 5px 0;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.logo-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 300;
}

/* 菜单样式覆盖 */
.sidebar :deep(.el-menu) {
  border: none;
  background: transparent;
}

.sidebar :deep(.el-menu-item) {
  margin: 8px 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar :deep(.el-menu-item:hover) {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.sidebar :deep(.el-menu-item.is-active) {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.sidebar :deep(.el-menu-item .el-icon) {
  margin-right: 8px;
  font-size: 18px;
}

.sidebar :deep(.el-menu-item span) {
  font-weight: 500;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 200px;
  }

  .logo {
    padding: 20px 15px;
  }

  .logo h1 {
    font-size: 20px;
  }

  .logo-icon {
    font-size: 40px;
  }
}
</style>