package com.icss.springbootbig.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.icss.springbootbig.entity.Account;
import com.icss.springbootbig.entity.BankCard;
import com.icss.springbootbig.entity.User;
import com.icss.springbootbig.entity.VerificationCode;
import com.icss.springbootbig.exception.ApiException;
import com.icss.springbootbig.mapper.AccountMapper;
import com.icss.springbootbig.mapper.BankCardMapper;
import com.icss.springbootbig.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

@Service
public class NewUserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private BankCardMapper bankCardMapper;

    @Autowired
    private MemoryVerificationCodeService memoryVerificationCodeService;

    @Autowired
    private SmsService smsService;

    /**
     * 发送验证码
     */
    public String sendVerificationCode(String phone, Integer type) {
        // 验证手机号格式
        if (!isValidPhoneNumber(phone)) {
            throw new ApiException("手机号格式不正确");
        }

        // 验证验证码类型
        if (type == null || (type != 1 && type != 2)) {
            throw new ApiException("验证码类型错误，1-登录验证码，2-操作验证码");
        }

        // 检查发送频率限制（1分钟内只能发送一次）
        VerificationCode lastCode = memoryVerificationCodeService.findByPhoneAndType(phone, type);
        if (lastCode != null && lastCode.getCreatedAt().after(new Date(System.currentTimeMillis() - 60 * 1000))) {
            throw new ApiException("验证码发送过于频繁，请1分钟后再试");
        }

        // 生成6位随机验证码
        String code = String.format("%06d", new Random().nextInt(999999));

        // 设置过期时间（5分钟）
        Date expiresAt = new Date(System.currentTimeMillis() + 5 * 60 * 1000);

        // 保存验证码到内存
        VerificationCode verificationCode = new VerificationCode(phone, code, type, expiresAt);
        memoryVerificationCodeService.saveVerificationCode(verificationCode);

        // 调用短信服务发送验证码
        boolean sendSuccess = smsService.sendVerificationCode(phone, code, type);

        if (!sendSuccess) {
            throw new ApiException("验证码发送失败，请稍后重试");
        }

        // 强制返回验证码数字（用于前端显示）
        return code;
    }

    /**
     * 验证手机号格式
     */
    private boolean isValidPhoneNumber(String phone) {
        if (phone == null || phone.isEmpty()) {
            return false;
        }
        // 中国大陆手机号正则表达式
        return phone.matches("^1[3-9]\\d{9}$");
    }

    /**
     * 手机号脱敏
     */
    private String maskPhoneNumber(String phone) {
        if (phone == null || phone.length() != 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }



    /**
     * 密码登录
     */
    public User loginWithPassword(String phone, String password) {
        User user = userMapper.selectOne(
            new QueryWrapper<User>().eq("phone", phone)
        );
        
        if (user == null) {
            // 如果用户不存在，自动注册
            user = autoRegister(phone, password);
        } else {
            // 验证密码
            if (!password.equals(user.getPassword())) {
                throw new ApiException("密码错误");
            }
            
            if (user.getStatus() == 0) {
                throw new ApiException("账户已被冻结");
            }
        }
        
        // 更新最后登录时间
        user.setLastLogin(new Date());
        userMapper.updateById(user);
        
        return user;
    }

    /**
     * 验证码登录
     */
    public User loginWithSms(String phone, String code) {
        // 验证手机号格式
        if (!isValidPhoneNumber(phone)) {
            throw new ApiException("手机号格式不正确");
        }

        // 验证验证码格式
        if (code == null || !code.matches("\\d{6}")) {
            throw new ApiException("验证码格式不正确");
        }

        // 验证验证码
        VerificationCode verificationCode = memoryVerificationCodeService.findByPhoneAndType(phone, 1);

        if (verificationCode == null ||
            !code.equals(verificationCode.getCode()) ||
            verificationCode.getUsed() == 1 ||
            verificationCode.getExpiresAt().before(new Date())) {
            throw new ApiException("验证码无效或已过期");
        }

        // 标记验证码为已使用
        memoryVerificationCodeService.markAsUsed(phone, 1);

        // 查找或创建用户
        User user = userMapper.selectOne(
            new QueryWrapper<User>().eq("phone", phone)
        );

        if (user == null) {
            // 自动注册
            user = autoRegister(phone, null);
        } else {
            if (user.getStatus() == 0) {
                throw new ApiException("账户已被冻结");
            }
            if (user.getStatus() == -1) {
                throw new ApiException("账户已注销");
            }
        }

        // 更新最后登录时间
        user.setLastLogin(new Date());
        userMapper.updateById(user);

        return user;
    }

    /**
     * 验证验证码（通用方法）
     */
    public boolean verifyCode(String phone, String code, Integer type) {
        if (!isValidPhoneNumber(phone)) {
            return false;
        }

        if (code == null || !code.matches("\\d{6}")) {
            return false;
        }

        VerificationCode verificationCode = memoryVerificationCodeService.findByPhoneAndType(phone, type);

        if (verificationCode != null &&
            code.equals(verificationCode.getCode()) &&
            verificationCode.getUsed() == 0 &&
            verificationCode.getExpiresAt().after(new Date())) {

            // 标记为已使用
            memoryVerificationCodeService.markAsUsed(phone, type);
            return true;
        }

        return false;
    }

    /**
     * 自动注册用户
     */
    @Transactional
    private User autoRegister(String phone, String password) {
        User user = new User();
        user.setPhone(phone);
        user.setPassword(password);
        user.setStatus(1);
        user.setCreatedAt(new Date());
        user.setUpdatedAt(new Date());
        
        userMapper.insert(user);
        
        // 创建对应的账户
        Account account = new Account();
        account.setUserId(user.getUserId());
        account.setBalance(BigDecimal.ZERO);
        account.setDailyLimit(new BigDecimal("50000.00"));
        account.setSingleLimit(new BigDecimal("5000.00"));
        account.setMonthlyLimit(new BigDecimal("200000.00"));
        account.setStatus(1);
        account.setCreatedAt(new Date());
        account.setUpdatedAt(new Date());
        
        accountMapper.insert(account);

        // 创建默认银行卡
        createDefaultBankCard(user.getUserId(), user.getPhone());

        return user;
    }

    /**
     * 用户注册
     */
    @Transactional
    public User register(User user) {
        // 检查手机号是否已存在
        User existingUser = userMapper.selectOne(
            new QueryWrapper<User>().eq("phone", user.getPhone())
        );
        
        if (existingUser != null) {
            throw new ApiException("手机号已注册");
        }
        
        // 设置默认值
        user.setStatus(1);
        user.setCreatedAt(new Date());
        user.setUpdatedAt(new Date());
        
        userMapper.insert(user);
        
        // 创建对应的账户
        Account account = new Account();
        account.setUserId(user.getUserId());
        account.setBalance(BigDecimal.ZERO);
        account.setDailyLimit(new BigDecimal("50000.00"));
        account.setSingleLimit(new BigDecimal("5000.00"));
        account.setMonthlyLimit(new BigDecimal("200000.00"));
        account.setStatus(1);
        account.setCreatedAt(new Date());
        account.setUpdatedAt(new Date());
        
        accountMapper.insert(account);

        // 创建默认银行卡
        createDefaultBankCard(user.getUserId(), user.getPhone());

        return user;
    }

    /**
     * 创建默认银行卡
     */
    private void createDefaultBankCard(Integer userId, String phone) {
        BankCard defaultCard = new BankCard();
        defaultCard.setUserId(userId);
        defaultCard.setBankName("中国银行");
        // 生成模拟银行卡号（以6217开头的19位数字）
        defaultCard.setCardNumber(generateDefaultCardNumber());
        defaultCard.setCardType(1); // 1-借记卡
        defaultCard.setCardHolder("用户" + userId);
        defaultCard.setPhone(phone);
        defaultCard.setIsDefault(1); // 设为默认卡
        defaultCard.setStatus(1); // 1-正常状态
        defaultCard.setCreatedAt(new Date());
        defaultCard.setUpdatedAt(new Date());

        bankCardMapper.insert(defaultCard);
    }

    /**
     * 生成默认银行卡号
     */
    private String generateDefaultCardNumber() {
        // 生成以6217开头的19位银行卡号
        StringBuilder cardNumber = new StringBuilder("6217");
        Random random = new Random();
        for (int i = 0; i < 15; i++) {
            cardNumber.append(random.nextInt(10));
        }
        return cardNumber.toString();
    }

    /**
     * 根据ID获取用户
     */
    public User getUserById(Integer userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }
        return user;
    }

    /**
     * 更新用户信息
     */
    public User updateUser(User user) {
        user.setUpdatedAt(new Date());
        userMapper.updateById(user);
        return userMapper.selectById(user.getUserId());
    }

    /**
     * 修改登录密码
     */
    public void changePassword(Integer userId, String oldPassword, String newPassword) {
        User user = getUserById(userId);
        
        if (!oldPassword.equals(user.getPassword())) {
            throw new ApiException("原密码错误");
        }
        
        user.setPassword(newPassword);
        user.setUpdatedAt(new Date());
        userMapper.updateById(user);
    }

    /**
     * 设置支付密码 - 已废弃，请使用PayPasswordService
     */
    @Deprecated
    public void setPayPassword(Integer userId, String payPassword) {
        // 这个方法已经废弃，支付密码现在存储在银行卡表中
        // 请使用 PayPasswordService.setDefaultCardPayPassword 方法
        throw new ApiException("此方法已废弃，请使用PayPasswordService设置支付密码");
    }

    /**
     * 修改支付密码 - 已废弃，请使用PayPasswordService
     */
    @Deprecated
    public void changePayPassword(Integer userId, String oldPayPassword, String newPayPassword) {
        // 这个方法已经废弃，支付密码现在存储在银行卡表中
        // 请使用 PayPasswordService.changeDefaultCardPayPassword 方法
        throw new ApiException("此方法已废弃，请使用PayPasswordService修改支付密码");
    }

    /**
     * 设置支付限额
     */
    public void setPaymentLimits(Integer userId, Map<String, Object> limits) {
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );

        if (account == null) {
            throw new ApiException("账户不存在");
        }

        if (limits.containsKey("dailyLimit")) {
            account.setDailyLimit(new BigDecimal(limits.get("dailyLimit").toString()));
        }
        if (limits.containsKey("singleLimit")) {
            account.setSingleLimit(new BigDecimal(limits.get("singleLimit").toString()));
        }
        if (limits.containsKey("monthlyLimit")) {
            account.setMonthlyLimit(new BigDecimal(limits.get("monthlyLimit").toString()));
        }

        account.setUpdatedAt(new Date());
        accountMapper.updateById(account);
    }

    /**
     * 上传用户头像
     */
    public String uploadAvatar(Integer userId, MultipartFile file) {
        if (file.isEmpty()) {
            throw new ApiException("文件不能为空");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new ApiException("只能上传图片文件");
        }

        // 检查文件大小（限制为5MB）
        if (file.getSize() > 5 * 1024 * 1024) {
            throw new ApiException("文件大小不能超过5MB");
        }

        try {
            // 创建上传目录
            String uploadDir = "uploads/avatars/";
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String filename = userId + "_" + UUID.randomUUID().toString() + extension;

            // 保存文件
            Path filePath = Paths.get(uploadDir + filename);
            Files.copy(file.getInputStream(), filePath);

            // 更新用户头像URL
            User user = getUserById(userId);
            user.setAvatar("/uploads/avatars/" + filename);
            user.setUpdatedAt(new Date());
            userMapper.updateById(user);

            return user.getAvatar();
        } catch (IOException e) {
            throw new ApiException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 冻结用户账户
     */
    @Transactional
    public void freezeAccount(Integer userId, String reason) {
        User user = getUserById(userId);
        user.setStatus(0); // 0表示冻结
        user.setUpdatedAt(new Date());
        userMapper.updateById(user);

        // 同时冻结关联的钱包账户
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        if (account != null) {
            account.setStatus(0);
            account.setUpdatedAt(new Date());
            accountMapper.updateById(account);
        }
    }

    /**
     * 解冻用户账户
     */
    @Transactional
    public void unfreezeAccount(Integer userId) {
        User user = getUserById(userId);
        user.setStatus(1); // 1表示正常
        user.setUpdatedAt(new Date());
        userMapper.updateById(user);

        // 同时解冻关联的钱包账户
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        if (account != null) {
            account.setStatus(1);
            account.setUpdatedAt(new Date());
            accountMapper.updateById(account);
        }
    }

    /**
     * 注销用户账户（软删除）
     */
    @Transactional
    public void deactivateAccount(Integer userId, String payPassword) {
        User user = getUserById(userId);

        // 支付密码现在存储在银行卡表中，需要验证默认银行卡的支付密码
        // 暂时跳过支付密码验证，或者集成PayPasswordService
        // if (!payPassword.equals(user.getPayPassword())) {
        //     throw new ApiException("支付密码错误");
        // }

        // 检查账户余额
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        if (account != null && account.getBalance().compareTo(BigDecimal.ZERO) > 0) {
            throw new ApiException("账户余额不为零，无法注销");
        }

        // 设置用户状态为注销（-1）
        user.setStatus(-1);
        user.setUpdatedAt(new Date());
        userMapper.updateById(user);

        // 设置账户状态为注销
        if (account != null) {
            account.setStatus(-1);
            account.setUpdatedAt(new Date());
            accountMapper.updateById(account);
        }
    }

    /**
     * 更新个人资料（只更新数据库中实际存在的字段）
     */
    public User updateProfile(Integer userId, Map<String, Object> profileData) {
        User user = getUserById(userId);

        // 只更新数据库中实际存在的字段
        boolean hasUpdates = false;

        // 支付密码现在存储在银行卡表中，不再存储在用户表中
        if (profileData.containsKey("payPassword")) {
            throw new ApiException("支付密码设置功能已迁移到PayPasswordService，请使用相应的API");
        }

        // 如果有更新，则保存到数据库
        if (hasUpdates) {
            user.setUpdatedAt(new Date());
            userMapper.updateById(user);
        }

        return user;
    }

    /**
     * 获取用户账户状态信息
     */
    public Map<String, Object> getAccountStatus(Integer userId) {
        User user = getUserById(userId);
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );

        Map<String, Object> status = new HashMap<>();
        status.put("userStatus", user.getStatus());
        status.put("accountStatus", account != null ? account.getStatus() : null);
        // 这些字段在用户表中不存在，暂时设为默认值
        status.put("loginAttempts", 0);
        status.put("lastLoginTime", user.getLastLogin());
        status.put("fingerprintEnabled", false);

        return status;
    }

    /**
     * 设置指纹登录（用户表中没有此字段，暂时不可用）
     */
    public void setFingerprintEnabled(Integer userId, Boolean enabled) {
        throw new ApiException("指纹登录功能暂时不可用，用户表中没有相应字段");
    }

    /**
     * 获取用户完整账户信息（包括钱包和银行卡）
     */
    public Map<String, Object> getCompleteUserInfo(Integer userId) {
        Map<String, Object> completeInfo = new HashMap<>();

        // 获取用户基本信息
        User user = getUserById(userId);
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("userId", user.getUserId());
        userInfo.put("phone", user.getPhone());
        userInfo.put("username", user.getUsername());
        userInfo.put("status", user.getStatus());
        userInfo.put("lastLogin", user.getLastLogin());
        userInfo.put("createdAt", user.getCreatedAt());

        // 获取钱包账户信息
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        Map<String, Object> walletInfo = new HashMap<>();
        if (account != null) {
            walletInfo.put("accountId", account.getAccountId());
            walletInfo.put("balance", account.getBalance());
            walletInfo.put("dailyLimit", account.getDailyLimit());
            walletInfo.put("singleLimit", account.getSingleLimit());
            walletInfo.put("monthlyLimit", account.getMonthlyLimit());
            walletInfo.put("status", account.getStatus());
        } else {
            walletInfo.put("message", "钱包账户不存在");
        }

        // 获取银行卡信息
        List<BankCard> bankCards = bankCardMapper.selectList(
            new QueryWrapper<BankCard>().eq("user_id", userId)
        );
        List<Map<String, Object>> cardList = new ArrayList<>();
        for (BankCard card : bankCards) {
            Map<String, Object> cardInfo = new HashMap<>();
            cardInfo.put("cardId", card.getCardId());
            cardInfo.put("bankName", card.getBankName());
            cardInfo.put("cardNumber", maskCardNumber(card.getCardNumber()));
            cardInfo.put("cardType", card.getCardType() == 1 ? "借记卡" : "信用卡");
            cardInfo.put("cardHolder", card.getCardHolder());
            cardInfo.put("isDefault", card.getIsDefault() == 1);
            cardInfo.put("status", card.getStatus() == 1 ? "正常" : "冻结");
            cardInfo.put("createdAt", card.getCreatedAt());
            cardList.add(cardInfo);
        }

        completeInfo.put("userInfo", userInfo);
        completeInfo.put("walletInfo", walletInfo);
        completeInfo.put("bankCards", cardList);
        completeInfo.put("totalCards", cardList.size());

        return completeInfo;
    }

    /**
     * 银行卡号脱敏
     */
    private String maskCardNumber(String cardNumber) {
        if (cardNumber == null || cardNumber.length() < 8) {
            return cardNumber;
        }
        return cardNumber.substring(0, 4) + " **** **** " + cardNumber.substring(cardNumber.length() - 4);
    }

    /**
     * 分页查询用户列表
     */
    public IPage<User> getUsersByPage(int pageNum, int pageSize, String phone, String username, Integer status) {
        Page<User> page = new Page<>(pageNum, pageSize);
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();

        if (phone != null && !phone.trim().isEmpty()) {
            queryWrapper.like("phone", phone);
        }

        if (username != null && !username.trim().isEmpty()) {
            queryWrapper.like("username", username);
        }

        if (status != null) {
            queryWrapper.eq("status", status);
        }

        queryWrapper.orderByDesc("created_at");

        return userMapper.selectPage(page, queryWrapper);
    }

    /**
     * 更新用户状态
     */
    @Transactional
    public void updateUserStatus(Integer userId, Integer status) {
        User user = getUserById(userId);
        user.setStatus(status);
        user.setUpdatedAt(new Date());
        userMapper.updateById(user);

        // 同时更新关联的钱包账户状态
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        if (account != null) {
            account.setStatus(status);
            account.setUpdatedAt(new Date());
            accountMapper.updateById(account);
        }
    }

}
