package com.icss.springbootbig.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("accounts")
public class Account {
    @TableId(value = "account_id", type = IdType.AUTO)
    private Integer accountId;

    @TableField("user_id")
    private Integer userId;

    private BigDecimal balance;

    @TableField("daily_limit")
    private BigDecimal dailyLimit;

    @TableField("single_limit")
    private BigDecimal singleLimit;

    @TableField("monthly_limit")
    private BigDecimal monthlyLimit;

    private Integer status; // 账户状态：1-正常，0-冻结，-1-注销

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;
}