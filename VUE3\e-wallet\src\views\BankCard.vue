<template>
  <div class="bankcard-container">
    <!-- 网络状态提示 -->
    <NetworkStatus />



    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>银行卡管理</h2>
        <p>管理您的银行卡，包括绑定、查询、详情查看和解除绑定</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showAddCardDialog = true">
          <el-icon><Plus /></el-icon>
          添加银行卡
        </el-button>
        <el-button @click="loadBankCards">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-dropdown @command="handleViewCommand">
          <el-button>
            视图筛选<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="all">所有银行卡</el-dropdown-item>
              <el-dropdown-item command="bound">已绑定卡片</el-dropdown-item>
              <el-dropdown-item command="default">默认卡片</el-dropdown-item>
              <el-dropdown-item command="debit">储蓄卡</el-dropdown-item>
              <el-dropdown-item command="credit">信用卡</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 数据统计显示 -->
    <div class="data-type-indicator">
      <div class="left-info">
        <el-tag type="primary" size="large">
          {{ getViewTitle() }}
        </el-tag>
        <span class="data-count">共 {{ filteredCards.length }} 张银行卡</span>
        <span class="total-balance">总余额：¥{{ totalBalance.toLocaleString() }}</span>
      </div>
      <div class="right-actions">
        <el-tag
          :type="isUsingDatabaseData ? 'success' : 'warning'"
          size="small"
          style="margin-right: 10px;"
        >
          {{ isUsingDatabaseData ? '数据库数据' : '模拟数据' }}
        </el-tag>
        <el-button
          size="small"
          @click="loadBankCards(true)"
          :loading="loading"
          type="primary"
        >
          <el-icon><ArrowDown /></el-icon>
          从数据库刷新
        </el-button>
      </div>
    </div>

    <!-- 银行卡统计 -->
    <el-row :gutter="20" class="card-stats">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card total-card">
          <div class="stat-content">
            <div class="stat-icon">💳</div>
            <div class="stat-info">
              <div class="stat-number">{{ totalCards }}</div>
              <div class="stat-label">总银行卡数</div>
              <div class="stat-desc">已绑定 {{ boundCards }} 张</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="stat-card debit-card">
          <div class="stat-content">
            <div class="stat-icon">🏦</div>
            <div class="stat-info">
              <div class="stat-number">{{ debitCards }}</div>
              <div class="stat-label">储蓄卡数量</div>
              <div class="stat-desc">余额 ¥{{ debitBalance.toLocaleString() }}</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="stat-card credit-card">
          <div class="stat-content">
            <div class="stat-icon">💎</div>
            <div class="stat-info">
              <div class="stat-number">{{ creditCards }}</div>
              <div class="stat-label">信用卡数量</div>
              <div class="stat-desc">可用额度 ¥{{ creditLimit.toLocaleString() }}</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card shadow="hover" class="stat-card default-card">
          <div class="stat-content">
            <div class="stat-icon">⭐</div>
            <div class="stat-info">
              <div class="stat-number">{{ defaultCards }}</div>
              <div class="stat-label">默认卡片</div>
              <div class="stat-desc">{{ defaultCardName || '未设置' }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>



    <!-- 银行卡列表 -->
    <el-card class="card-list" shadow="hover">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <span class="card-title">我的银行卡</span>
            <el-tag type="info" size="small">{{ getViewTitle() }}</el-tag>
          </div>
          <div class="header-right">
            <el-button-group size="small">
              <el-button :type="cardFilter === 'all' ? 'primary' : ''" @click="cardFilter = 'all'">全部</el-button>
              <el-button :type="cardFilter === 'bound' ? 'primary' : ''" @click="cardFilter = 'bound'">已绑定</el-button>
              <el-button :type="cardFilter === 'debit' ? 'primary' : ''" @click="cardFilter = 'debit'">储蓄卡</el-button>
              <el-button :type="cardFilter === 'credit' ? 'primary' : ''" @click="cardFilter = 'credit'">信用卡</el-button>
            </el-button-group>
            <el-input
              v-model="searchKeyword"
              placeholder="搜索银行卡"
              size="small"
              style="width: 200px; margin-left: 10px;"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>
      </template>

      <div class="bank-cards">
        <div
          v-for="card in filteredCards"
          :key="card.id"
          class="bank-card-item"
          :class="card.type"
        >
          <div class="card-visual">
            <div class="card-bg" :style="{ background: card.gradient }">
              <div class="card-pattern"></div>
              <div class="card-content">
                <div class="card-bank">{{ card.bankName }}</div>
                <div class="card-number">{{ card.maskedNumber }}</div>
                <div class="card-balance">¥{{ (card.balance || 0).toLocaleString() }}</div>
                <div class="card-info">
                  <span class="card-holder">{{ card.holderName }}</span>
                  <span class="card-type-badge">{{ card.typeName }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="card-details">
            <div class="card-main-info">
              <h3>{{ card.bankName }} {{ card.typeName }}</h3>
              <p class="card-number-full">{{ card.maskedNumber }}</p>
              <div class="card-status">
                <el-tag :type="getConnectionStatusType(card)" size="small">
                  {{ getConnectionStatusText(card) }}
                </el-tag>
                <el-tag :type="card.status === 1 ? 'success' : 'warning'" size="small">
                  {{ card.status === 1 ? '已激活' : '未激活' }}
                </el-tag>
                <el-tag v-if="card.isDefault" type="primary" size="small">默认卡片</el-tag>
              </div>
            </div>

            <div class="card-actions">
              <el-button size="small" @click="viewCardDetails(card)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button
                size="small"
                :type="card.isDefault ? 'warning' : 'success'"
                @click="setDefaultCard(card)"
              >
                <el-icon><Star /></el-icon>
                {{ card.isDefault ? '取消默认' : '设为默认' }}
              </el-button>
              <el-button
                size="small"
                :type="card.status === 1 ? 'warning' : 'success'"
                @click="card.status === 1 ? deactivateCard(card) : activateCard(card)"
              >
                <el-icon v-if="card.status === 1"><Lock /></el-icon>
                <el-icon v-else><Unlock /></el-icon>
                {{ card.status === 1 ? '停用' : '激活' }}
              </el-button>
              <el-button
                size="small"
                :type="card.isBound ? 'warning' : 'primary'"
                @click="toggleCardBinding(card)"
              >
                <el-icon><Link /></el-icon>
                {{ card.isBound ? '解除绑定' : '绑定' }}
              </el-button>
              <el-dropdown @command="(command) => handleCardAction(command, card)">
                <el-button size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="activate" :disabled="card.status === 1">
                      <el-icon><Unlock /></el-icon>
                      激活卡片
                    </el-dropdown-item>
                    <el-dropdown-item command="deactivate" :disabled="card.status !== 1">
                      <el-icon><Lock /></el-icon>
                      停用卡片
                    </el-dropdown-item>
                    <el-dropdown-item command="freeze" :disabled="card.status !== 1" divided>
                      <el-icon><Lock /></el-icon>
                      冻结卡片
                    </el-dropdown-item>
                    <el-dropdown-item command="unfreeze" :disabled="card.status === 1">
                      <el-icon><Unlock /></el-icon>
                      解冻卡片
                    </el-dropdown-item>
                    <el-dropdown-item command="edit" divided>
                      <el-icon><Edit /></el-icon>
                      编辑信息
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>
                      <el-icon><Delete /></el-icon>
                      删除卡片
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredCards.length === 0" class="empty-state">
          <div class="empty-icon">💳</div>
          <p>暂无银行卡</p>
          <el-button type="primary" @click="showAddCardDialog = true">添加银行卡</el-button>
        </div>
      </div>
    </el-card>

    <!-- 添加银行卡对话框 -->
    <el-dialog v-model="showAddCardDialog" title="添加银行卡" width="500px">
      <el-form :model="addCardForm" :rules="addCardRules" ref="addCardFormRef" label-width="100px">
        <el-form-item label="银行名称" prop="bankName">
          <el-select v-model="addCardForm.bankName" placeholder="请选择银行" filterable>
            <el-option
              v-for="bank in bankList"
              :key="bank.bankId"
              :label="bank.bankName"
              :value="bank.bankName"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="卡片类型" prop="cardType">
          <el-radio-group v-model="addCardForm.cardType">
            <el-radio :value="1">储蓄卡</el-radio>
            <el-radio :value="2">信用卡</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="卡号" prop="cardNumber">
          <el-input
            v-model="addCardForm.cardNumber"
            placeholder="请输入银行卡号"
            maxlength="19"
            @input="formatCardNumber"
          ></el-input>
        </el-form-item>

        <el-form-item label="持卡人姓名" prop="cardHolder">
          <el-input v-model="addCardForm.cardHolder" placeholder="请输入持卡人姓名"></el-input>
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input v-model="addCardForm.phone" placeholder="请输入预留手机号"></el-input>
        </el-form-item>

        <el-form-item label="有效期" prop="expiryDate">
          <el-input
            v-model="addCardForm.expiryDate"
            placeholder="MM/YY"
            maxlength="5"
            @input="formatExpiryDate"
          ></el-input>
        </el-form-item>

        <el-form-item label="CVV" prop="cvv">
          <el-input
            v-model="addCardForm.cvv"
            placeholder="请输入CVV"
            maxlength="3"
            type="password"
          ></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddCardDialog = false">取消</el-button>
        <el-button type="primary" @click="handleAddCard">确认添加</el-button>
      </template>
    </el-dialog>

    <!-- 银行卡详情对话框 -->
    <el-dialog v-model="showCardDetailsDialog" title="银行卡详情" width="400px">
      <div v-if="selectedCard" class="card-details-content">
        <div class="detail-card">
          <div class="card-bg" :style="{ background: selectedCard.gradient }">
            <div class="card-content">
              <div class="card-bank">{{ selectedCard.bankName }}</div>
              <div class="card-number">{{ selectedCard.maskedNumber }}</div>
              <div class="card-balance">¥{{ (selectedCard.balance || 0).toLocaleString() }}</div>
              <div class="card-info">
                <span class="card-holder">{{ selectedCard.holderName }}</span>
                <span class="card-type-badge">{{ selectedCard.typeName }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="detail-info">
          <div class="info-item">
            <label>银行名称：</label>
            <span>{{ selectedCard.bankName }}</span>
          </div>
          <div class="info-item">
            <label>卡片类型：</label>
            <span>{{ selectedCard.typeName }}</span>
          </div>
          <div class="info-item">
            <label>卡号：</label>
            <span>{{ selectedCard.maskedNumber }}</span>
          </div>
          <div class="info-item">
            <label>持卡人：</label>
            <span>{{ selectedCard.holderName }}</span>
          </div>
          <div class="info-item">
            <label>账户余额：</label>
            <span class="balance-amount">¥{{ (selectedCard.balance || 0).toLocaleString() }}</span>
          </div>
          <div class="info-item">
            <label>状态：</label>
            <el-tag :type="selectedCard.status === 1 ? 'success' : 'warning'" size="small">
              {{ selectedCard.status === 1 ? '已激活' : '未激活' }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>绑定状态：</label>
            <el-tag :type="selectedCard.isBound ? 'success' : 'info'" size="small">
              {{ selectedCard.isBound ? '已绑定' : '未绑定' }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>查询密码：</label>
            <span class="password-display">
              {{ showPassword ? (selectedCard.password || '未设置') : '******' }}
              <el-button
                size="small"
                text
                @click="togglePasswordVisibility"
                style="margin-left: 8px;"
              >
                <el-icon>
                  <View v-if="!showPassword" />
                  <Hide v-else />
                </el-icon>
              </el-button>
            </span>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showCardDetailsDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 编辑银行卡对话框 -->
    <el-dialog v-model="showEditCardDialog" title="编辑银行卡信息" width="500px">
      <el-form :model="editCardForm" :rules="editCardRules" ref="editCardFormRef" label-width="100px">
        <el-form-item label="银行名称" prop="bankName">
          <el-select v-model="editCardForm.bankName" placeholder="请选择银行" filterable>
            <el-option
              v-for="bank in bankList"
              :key="bank.bankId"
              :label="bank.bankName"
              :value="bank.bankName"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="持卡人姓名" prop="cardHolder">
          <el-input v-model="editCardForm.cardHolder" placeholder="请输入持卡人姓名" />
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editCardForm.phone" placeholder="请输入手机号" maxlength="11" />
        </el-form-item>

        <el-form-item label="卡片类型" prop="cardType">
          <el-radio-group v-model="editCardForm.cardType">
            <el-radio :label="1">储蓄卡</el-radio>
            <el-radio :label="2">信用卡</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="editCardForm.cardType === 2" label="信用额度" prop="creditLimit">
          <el-input-number
            v-model="editCardForm.creditLimit"
            :min="0"
            :max="1000000"
            :step="1000"
            placeholder="请输入信用额度"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="设为默认卡">
          <el-switch v-model="editCardForm.isDefault" :active-value="1" :inactive-value="0" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showEditCardDialog = false">取消</el-button>
        <el-button type="primary" @click="handleEditCard" :loading="editLoading">确认修改</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, ArrowDown, Lock, Unlock, Edit, Delete, Star, Link, View, Hide } from '@element-plus/icons-vue';
import bankCardApi from '@/api/bankCard';
import { bankApi } from '@/api/bank';
import {
  getUserInfo,
  getCurrentUserId,
  getUserBankCards,
  getBankList as getStoredBankList,
  refreshUserData
} from '@/stores/user';
import NetworkStatus from '@/components/NetworkStatus.vue';

// 对话框显示状态
const showAddCardDialog = ref(false);
const showCardDetailsDialog = ref(false);
const showEditCardDialog = ref(false);
const editLoading = ref(false);

// 筛选状态
const cardFilter = ref('all');
const searchKeyword = ref('');

// 选中的卡片
const selectedCard = ref(null);

// 密码显示控制
const showPassword = ref(false);

// 加载状态
const loading = ref(false);
const isUsingDatabaseData = ref(false); // 标识是否使用数据库数据

// 当前用户信息 - 从登录状态获取
const currentUserId = ref(null);

// 初始化用户ID
const initializeUserId = () => {
  const userInfo = getUserInfo();
  if (userInfo && userInfo.userId) {
    currentUserId.value = userInfo.userId;
    return true;
  } else {
    ElMessage.error('请先登录');
    return false;
  }
};

// 银行卡数据
const bankCards = ref([]);

// 银行列表数据
const bankList = ref([]);

// 添加银行卡表单
const addCardForm = ref({
  userId: currentUserId.value || 1,
  bankName: '',
  cardType: 1, // 1-借记卡 2-信用卡
  cardNumber: '',
  cardHolder: '',
  expiryDate: '',
  cvv: '',
  phone: '',
  isDefault: 0,
  status: 1 // 1-正常状态
});

const addCardFormRef = ref(null);
const editCardFormRef = ref(null);

// 编辑银行卡表单
const editCardForm = ref({
  cardId: null,
  bankName: '',
  cardHolder: '',
  phone: '',
  cardType: 1,
  creditLimit: 0,
  isDefault: 0
});

// 表单验证规则
const addCardRules = {
  bankName: [
    { required: true, message: '请选择银行', trigger: 'change' }
  ],
  cardType: [
    { required: true, message: '请选择卡片类型', trigger: 'change' }
  ],
  cardNumber: [
    { required: true, message: '请输入银行卡号', trigger: 'blur' },
    { min: 16, max: 19, message: '银行卡号长度应为16-19位', trigger: 'blur' }
  ],
  cardHolder: [
    { required: true, message: '请输入持卡人姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  expiryDate: [
    { required: false, message: '请输入有效期', trigger: 'blur' },
    { pattern: /^(0[1-9]|1[0-2])\/\d{2}$/, message: '有效期格式应为MM/YY', trigger: 'blur' }
  ],
  cvv: [
    { required: false, message: '请输入CVV', trigger: 'blur' },
    { len: 3, message: 'CVV应为3位数字', trigger: 'blur' }
  ]
};

// 编辑表单验证规则
const editCardRules = {
  bankName: [
    { required: true, message: '请选择银行', trigger: 'change' }
  ],
  cardHolder: [
    { required: true, message: '请输入持卡人姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  cardType: [
    { required: true, message: '请选择卡片类型', trigger: 'change' }
  ]
};

// 页面加载时获取数据
onMounted(() => {
  // 初始化用户ID并检查权限
  if (initializeUserId()) {
    loadBankCards();
    loadBankList();
  }
});

// 当前显示的数据类型
const currentDataType = ref('all');

// 获取模拟银行列表
const getMockBankList = () => {
  return [
    { bankId: 1, bankName: '招商银行', bankCode: 'CMB' },
    { bankId: 2, bankName: '建设银行', bankCode: 'CCB' },
    { bankId: 3, bankName: '工商银行', bankCode: 'ICBC' },
    { bankId: 4, bankName: '农业银行', bankCode: 'ABC' },
    { bankId: 5, bankName: '中国银行', bankCode: 'BOC' },
    { bankId: 6, bankName: '交通银行', bankCode: 'BOCOM' },
    { bankId: 7, bankName: '邮储银行', bankCode: 'PSBC' },
    { bankId: 8, bankName: '民生银行', bankCode: 'CMBC' },
    { bankId: 9, bankName: '光大银行', bankCode: 'CEB' },
    { bankId: 10, bankName: '平安银行', bankCode: 'PAB' }
  ];
};

// 加载银行列表 - 从用户数据获取
const loadBankList = async () => {
  try {
    // 首先从用户数据获取银行列表
    const storedBankList = getStoredBankList();
    if (storedBankList && storedBankList.length > 0) {
      bankList.value = storedBankList;
      console.log('使用存储的银行列表数据');
    } else {
      // 如果没有存储的数据，使用模拟数据
      bankList.value = getMockBankList();
      console.log('使用模拟银行列表数据');
    }
  } catch (error) {
    console.error('加载银行列表失败:', error);
    // 使用模拟数据
    bankList.value = getMockBankList();
    console.log('网络错误，使用模拟银行列表数据');
  }
};

// 加载银行卡数据 - 直接从数据库获取
const loadBankCards = async (forceRefresh = false) => {
  currentDataType.value = 'all';
  loading.value = true;

  const userId = currentUserId.value;
  if (!userId) {
    ElMessage.error('用户信息无效');
    loading.value = false;
    return;
  }

  try {
    console.log(`开始加载用户 ${userId} 的银行卡数据...`);

    // 如果强制刷新或没有缓存数据，直接调用API
    if (forceRefresh || !getUserBankCards().length) {
      console.log('直接从数据库获取银行卡数据...');
      const response = await bankCardApi.getUserCards(userId);

      if (response && response.code === 0) {
        const cards = response.data || [];
        console.log(`从数据库获取到 ${cards.length} 张银行卡`);
        bankCards.value = cards.map(card => transformCardData(card));
        isUsingDatabaseData.value = true; // 标记为数据库数据

        // 更新用户状态管理中的数据
        await refreshUserData();

        ElMessage.success(`银行卡数据加载成功 (${cards.length}张)`);
      } else {
        console.warn('数据库返回错误:', response?.msg);
        // 如果数据库没有数据，使用模拟数据
        bankCards.value = getMockBankCards();
        isUsingDatabaseData.value = false; // 标记为模拟数据
        ElMessage.warning('数据库暂无数据，使用模拟数据');
      }
    } else {
      // 使用缓存数据
      console.log('使用缓存的银行卡数据');
      const storedCards = getUserBankCards();
      bankCards.value = storedCards.map(card => transformCardData(card));
      isUsingDatabaseData.value = storedCards.length > 0; // 如果有缓存数据，可能来自数据库
      ElMessage.success('银行卡数据加载成功 (缓存)');
    }
  } catch (error) {
    console.error('加载银行卡失败:', error);
    // 网络错误时使用模拟数据
    bankCards.value = getMockBankCards();
    isUsingDatabaseData.value = false; // 标记为模拟数据
    ElMessage.error('网络连接异常，使用模拟数据');
  } finally {
    loading.value = false;
  }
};





// 设置默认银行卡
const setDefaultCard = async (card) => {
  try {
    if (card.isDefault) {
      ElMessage.warning('该卡片已经是默认卡片');
      return;
    }

    await ElMessageBox.confirm('确定要设置这张卡片为默认卡片吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    try {
      const response = await bankCardApi.setDefaultCard(currentUserId.value, card.cardId);
      if (response && response.code === 0) {
        ElMessage.success('设置默认卡片成功');
        await loadBankCards(); // 重新加载数据
      } else {
        // 模拟设置成功
        bankCards.value.forEach(c => {
          c.isDefault = c.cardId === card.cardId;
        });
        ElMessage.success('设置默认卡片成功（模拟）');
      }
    } catch (apiError) {
      console.error('API调用失败，使用模拟操作:', apiError);
      // 模拟设置成功
      bankCards.value.forEach(c => {
        c.isDefault = c.cardId === card.cardId;
      });
      ElMessage.success('设置默认卡片成功（模拟）');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作已取消');
    }
  }
};

// 加载信用卡
const loadCreditCards = async () => {
  currentDataType.value = 'credit';
  loading.value = true;
  try {
    const response = await bankCardApi.getCreditCards(currentUserId.value);
    if (response.code === 0) {
      bankCards.value = response.data.map(card => transformCardData(card));
      ElMessage.success('已加载信用卡数据');
    }
  } catch (error) {
    ElMessage.error('加载信用卡失败');
    console.error('加载失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取模拟银行卡数据 - 生成当前用户专属数据
const getMockBankCards = () => {
  const userId = currentUserId.value;
  if (!userId) {
    return [];
  }

  const userInfo = getUserInfo();
  const userName = userInfo?.username || `用户${userId}`;

  // 基础模拟数据
  const baseCards = [
    {
      cardId: parseInt(`${userId}001`),
      userId: userId,
      bankName: '招商银行',
      cardNumber: `*************${userId.toString().padStart(3, '0')}`,
      cardType: 1,
      cardHolder: userName,
      balance: 15680.50,
      status: 1, // 1=已激活，0=未激活
      isDefault: 1,
      isBound: true, // 绑定状态
      creditLimit: 0,
      phone: userInfo?.phone || '***********'
    },
    {
      cardId: parseInt(`${userId}002`),
      userId: userId,
      bankName: '建设银行',
      cardNumber: `*************${userId.toString().padStart(3, '0')}`,
      cardType: 2,
      cardHolder: userName,
      balance: 0,
      status: 1, // 已激活
      isDefault: 0,
      isBound: true, // 已绑定
      creditLimit: 50000,
      phone: userInfo?.phone || '***********'
    },
    {
      cardId: parseInt(`${userId}003`),
      userId: userId,
      bankName: '工商银行',
      cardNumber: `*************${userId.toString().padStart(3, '0')}`,
      cardType: 1,
      cardHolder: userName,
      balance: 8520.30,
      status: 1, // 已激活
      isDefault: 0,
      isBound: true, // 已绑定
      creditLimit: 0,
      phone: userInfo?.phone || '***********'
    }
  ];

  return baseCards.map(card => transformCardData(card));
};

// 转换银行卡数据格式
const transformCardData = (card) => ({
  ...card,
  id: card.cardId,
  maskedNumber: `**** **** **** ${card.cardNumber.slice(-4)}`,
  typeName: card.cardType === 1 ? '储蓄卡' : '信用卡',
  type: card.cardType === 1 ? 'debit' : 'credit',
  holderName: card.cardHolder,
  // 修复绑定状态逻辑：如果有绑定状态字段则使用，否则默认为已绑定
  isBound: card.isBound !== undefined ? card.isBound : (card.status === 1),
  isDefault: card.isDefault === 1,
  // 连接状态基于卡片状态：1=正常(已激活)，0=冻结(未激活)
  connectionStatus: card.status === 1 ? 'connected' : 'disconnected',
  connecting: false,
  gradient: getRandomGradient()
});



// 计算属性
const totalCards = computed(() => bankCards.value.length);
const boundCards = computed(() => bankCards.value.filter(card => card.isBound).length);
const debitCards = computed(() => bankCards.value.filter(card => card.type === 'debit').length);
const creditCards = computed(() => bankCards.value.filter(card => card.type === 'credit').length);
const defaultCards = computed(() => bankCards.value.filter(card => card.isDefault).length);

// 余额统计
const totalBalance = computed(() => {
  return bankCards.value.reduce((total, card) => total + (card.balance || 0), 0);
});

const debitBalance = computed(() => {
  return bankCards.value
    .filter(card => card.type === 'debit')
    .reduce((total, card) => total + (card.balance || 0), 0);
});

const creditLimit = computed(() => {
  return bankCards.value
    .filter(card => card.type === 'credit')
    .reduce((total, card) => total + (card.creditLimit || 0), 0);
});

const defaultCardName = computed(() => {
  const defaultCard = bankCards.value.find(card => card.isDefault);
  return defaultCard ? `${defaultCard.bankName} ${defaultCard.typeName}` : '';
});

const filteredCards = computed(() => {
  let cards = bankCards.value;

  // 按类型筛选
  if (cardFilter.value === 'bound') {
    cards = cards.filter(card => card.isBound);
  } else if (cardFilter.value === 'default') {
    cards = cards.filter(card => card.isDefault);
  } else if (cardFilter.value !== 'all') {
    cards = cards.filter(card => card.type === cardFilter.value);
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    cards = cards.filter(card =>
      card.bankName.toLowerCase().includes(keyword) ||
      card.cardNumber.includes(keyword) ||
      card.holderName.toLowerCase().includes(keyword)
    );
  }

  return cards;
});

// 获取视图标题
const getViewTitle = () => {
  const titles = {
    all: '所有银行卡',
    bound: '已绑定卡片',
    default: '默认卡片',
    debit: '储蓄卡',
    credit: '信用卡'
  };
  return titles[cardFilter.value] || '所有银行卡';
};

// 格式化银行卡号
const formatCardNumber = () => {
  let value = addCardForm.value.cardNumber.replace(/\s/g, '');
  value = value.replace(/(.{4})/g, '$1 ').trim();
  addCardForm.value.cardNumber = value;
};

// 格式化有效期
const formatExpiryDate = () => {
  let value = addCardForm.value.expiryDate.replace(/\D/g, '');
  if (value.length >= 2) {
    value = value.substring(0, 2) + '/' + value.substring(2, 4);
  }
  addCardForm.value.expiryDate = value;
};

// 添加银行卡 - 保存到数据库
const handleAddCard = async () => {
  if (!addCardFormRef.value) return;

  try {
    await addCardFormRef.value.validate();

    // 确保userId正确设置
    const userId = currentUserId.value;
    if (!userId) {
      ElMessage.error('用户信息无效');
      return;
    }

    // 准备提交的数据
    const cardData = {
      ...addCardForm.value,
      userId: userId,
      cardNumber: addCardForm.value.cardNumber.replace(/\s/g, ''), // 移除空格
      cardType: addCardForm.value.cardType || 1,
      isDefault: addCardForm.value.isDefault || 0,
      status: 1 // 设置为正常状态
    };

    console.log('提交银行卡数据到数据库:', cardData);

    try {
      // 调用API添加银行卡到数据库
      const response = await bankCardApi.addBankCard(cardData);

      if (response && response.code === 0) {
        ElMessage.success('银行卡已成功保存到数据库！');
        showAddCardDialog.value = false;

        // 重新从数据库加载数据
        await loadBankCards(true); // 强制刷新
        resetAddCardForm();

        console.log('银行卡添加成功，数据已保存到数据库');
      } else {
        ElMessage.error(response?.msg || '保存到数据库失败');
        console.error('数据库保存失败:', response);
      }
    } catch (apiError) {
      console.error('数据库API调用失败:', apiError);
      ElMessage.error('网络连接失败，无法保存到数据库');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('请检查输入信息');
      console.error('表单验证失败:', error);
    }
  }
};

// 重置添加银行卡表单
const resetAddCardForm = () => {
  addCardForm.value = {
    userId: currentUserId.value || 1,
    bankName: '',
    cardType: 1,
    cardNumber: '',
    cardHolder: '',
    expiryDate: '',
    cvv: '',
    phone: '',
    isDefault: 0,
    status: 1 // 设置为正常状态
  };
};

// 获取随机渐变色
const getRandomGradient = () => {
  const gradients = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
  ];
  return gradients[Math.floor(Math.random() * gradients.length)];
};

// 处理视图命令
const handleViewCommand = (command) => {
  cardFilter.value = command;
};

// 处理银行卡操作
const handleCardAction = async (command, card) => {
  switch (command) {
    case 'activate':
      await activateCard(card);
      break;
    case 'deactivate':
      await deactivateCard(card);
      break;
    case 'freeze':
      await freezeCard(card);
      break;
    case 'unfreeze':
      await unfreezeCard(card);
      break;
    case 'edit':
      editCard(card);
      break;
    case 'delete':
      await removeCard(card);
      break;
    default:
      console.log('未知操作:', command);
  }
};

// 激活银行卡
const activateCard = async (card) => {
  try {
    await ElMessageBox.confirm('确定要激活这张银行卡吗？激活后可以正常使用。', '确认激活', {
      confirmButtonText: '确定激活',
      cancelButtonText: '取消',
      type: 'info'
    });

    try {
      const response = await bankCardApi.updateCardStatus(card.cardId, 1); // 1表示激活状态
      if (response && response.code === 0) {
        ElMessage.success('银行卡已激活并更新到数据库');
        // 重新从数据库加载数据
        await loadBankCards(true); // 强制刷新
        console.log('银行卡激活成功，数据库已更新');
      } else {
        ElMessage.error(response?.msg || '激活失败，数据库更新失败');
        console.error('数据库更新失败:', response);
      }
    } catch (apiError) {
      console.error('数据库API调用失败:', apiError);
      ElMessage.error('网络连接失败，无法更新数据库');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作已取消');
    }
  }
};

// 停用银行卡
const deactivateCard = async (card) => {
  try {
    await ElMessageBox.confirm('确定要停用这张银行卡吗？停用后将无法使用。', '确认停用', {
      confirmButtonText: '确定停用',
      cancelButtonText: '取消',
      type: 'warning'
    });

    try {
      const response = await bankCardApi.updateCardStatus(card.cardId, 0); // 0表示停用状态
      if (response && response.code === 0) {
        ElMessage.success('银行卡已停用并更新到数据库');
        // 重新从数据库加载数据
        await loadBankCards(true); // 强制刷新
        console.log('银行卡停用成功，数据库已更新');
      } else {
        ElMessage.error(response?.msg || '停用失败，数据库更新失败');
        console.error('数据库更新失败:', response);
      }
    } catch (apiError) {
      console.error('数据库API调用失败:', apiError);
      ElMessage.error('网络连接失败，无法更新数据库');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作已取消');
    }
  }
};

// 冻结银行卡
const freezeCard = async (card) => {
  try {
    await ElMessageBox.confirm('确定要冻结这张银行卡吗？冻结后将无法使用。', '确认冻结', {
      confirmButtonText: '确定冻结',
      cancelButtonText: '取消',
      type: 'warning'
    });

    try {
      const response = await bankCardApi.updateCardStatus(card.cardId, 0); // 0表示冻结状态
      if (response && response.code === 0) {
        // 更新本地状态
        card.status = 0;
        card.connectionStatus = 'disconnected';
        ElMessage.success('银行卡已冻结');
        await loadBankCards();
      } else {
        // 模拟冻结成功
        card.status = 0;
        card.connectionStatus = 'disconnected';
        ElMessage.success('银行卡已冻结（模拟）');
      }
    } catch (apiError) {
      console.error('API调用失败，使用模拟冻结:', apiError);
      // 模拟冻结成功
      card.status = 0;
      card.connectionStatus = 'disconnected';
      ElMessage.success('银行卡已冻结（模拟）');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作已取消');
    }
  }
};

// 解冻银行卡
const unfreezeCard = async (card) => {
  try {
    await ElMessageBox.confirm('确定要解冻这张银行卡吗？', '确认解冻', {
      confirmButtonText: '确定解冻',
      cancelButtonText: '取消',
      type: 'info'
    });

    try {
      const response = await bankCardApi.updateCardStatus(card.cardId, 1); // 1表示正常状态
      if (response && response.code === 0) {
        // 更新本地状态
        card.status = 1;
        card.connectionStatus = 'connected';
        ElMessage.success('银行卡已解冻');
        await loadBankCards();
      } else {
        // 模拟解冻成功
        card.status = 1;
        card.connectionStatus = 'connected';
        ElMessage.success('银行卡已解冻（模拟）');
      }
    } catch (apiError) {
      console.error('API调用失败，使用模拟解冻:', apiError);
      // 模拟解冻成功
      card.status = 1;
      card.connectionStatus = 'connected';
      ElMessage.success('银行卡已解冻（模拟）');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作已取消');
    }
  }
};

// 编辑银行卡
const editCard = (card) => {
  selectedCard.value = card;
  // 填充编辑表单
  editCardForm.value = {
    cardId: card.cardId,
    bankName: card.bankName,
    cardHolder: card.holderName,
    phone: card.phone || '',
    cardType: card.cardType,
    creditLimit: card.creditLimit || 0,
    isDefault: card.isDefault ? 1 : 0
  };
  showEditCardDialog.value = true;
};

// 处理编辑银行卡 - 更新数据库
const handleEditCard = async () => {
  if (!editCardFormRef.value) return;

  try {
    await editCardFormRef.value.validate();
    editLoading.value = true;

    const updateData = {
      bankName: editCardForm.value.bankName,
      cardHolder: editCardForm.value.cardHolder,
      phone: editCardForm.value.phone,
      cardType: editCardForm.value.cardType,
      creditLimit: editCardForm.value.creditLimit,
      isDefault: editCardForm.value.isDefault
    };

    console.log('更新银行卡数据到数据库:', updateData);

    try {
      const response = await bankCardApi.updateBankCard(editCardForm.value.cardId, updateData);
      if (response && response.code === 0) {
        ElMessage.success('银行卡信息已成功更新到数据库！');
        showEditCardDialog.value = false;

        // 重新从数据库加载数据
        await loadBankCards(true); // 强制刷新

        console.log('银行卡更新成功，数据库已更新');
      } else {
        ElMessage.error(response?.msg || '更新数据库失败');
        console.error('数据库更新失败:', response);
      }
    } catch (apiError) {
      console.error('数据库API调用失败:', apiError);
      ElMessage.error('网络连接失败，无法更新数据库');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('请检查输入信息');
      console.error('表单验证失败:', error);
    }
  } finally {
    editLoading.value = false;
  }
};

// 查看银行卡详情
const viewCardDetails = async (card) => {
  try {
    const response = await bankCardApi.getCardDetails(card.cardId);
    if (response.code === 0) {
      selectedCard.value = {
        ...response.data,
        maskedNumber: `**** **** **** ${response.data.cardNumber.slice(-4)}`,
        typeName: response.data.cardType === 1 ? '储蓄卡' : '信用卡',
        holderName: response.data.cardHolder,
        gradient: card.gradient
      };
      showCardDetailsDialog.value = true;
    }
  } catch (error) {
    ElMessage.error('获取银行卡详情失败');
    console.error('获取详情失败:', error);
  }
};

// 删除银行卡（从数据库中彻底删除）
const removeCard = async (card) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这张银行卡吗？删除后将从数据库中彻底移除，无法恢复！',
      '危险操作',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    );

    // 使用管理员删除（不验证用户权限），确保可以删除任何银行卡
    const response = await bankCardApi.adminDeleteCard(card.cardId);

    if (response.code === 0) {
      // 从列表中移除
      const index = bankCards.value.findIndex(c => c.cardId === card.cardId);
      if (index > -1) {
        bankCards.value.splice(index, 1);
      }
      ElMessage.success('银行卡已从数据库中删除！');

      // 重新加载数据以确保同步
      await loadBankCards();
    } else {
      ElMessage.error(response.msg || '删除银行卡失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除银行卡失败');
      console.error('删除失败:', error);
    }
  }
};

// 切换银行卡绑定状态（只更新状态，不删除数据）
const toggleCardBinding = async (card) => {
  try {
    if (card.isBound) {
      // 解绑银行卡（只更新状态为未绑定）
      try {
        const response = await bankCardApi.unbindCard(card.cardId, currentUserId.value);
        if (response && response.code === 0) {
          card.isBound = false;
          card.connectionStatus = 'disconnected';
          ElMessage.success('银行卡解绑成功！');
        } else {
          // 模拟解绑成功
          card.isBound = false;
          card.connectionStatus = 'disconnected';
          ElMessage.success('银行卡解绑成功（模拟）！');
        }
      } catch (apiError) {
        console.error('API调用失败，使用模拟解绑:', apiError);
        // 模拟解绑成功
        card.isBound = false;
        card.connectionStatus = 'disconnected';
        ElMessage.success('银行卡解绑成功（模拟）！');
      }
    } else {
      // 绑定银行卡
      try {
        const response = await bankCardApi.bindCard(card.cardId, currentUserId.value);
        if (response && response.code === 0) {
          card.isBound = true;
          card.connectionStatus = 'connected';
          ElMessage.success('银行卡绑定成功！');
        } else {
          // 模拟绑定成功
          card.isBound = true;
          card.connectionStatus = 'connected';
          ElMessage.success('银行卡绑定成功（模拟）！');
        }
      } catch (apiError) {
        console.error('API调用失败，使用模拟绑定:', apiError);
        // 模拟绑定成功
        card.isBound = true;
        card.connectionStatus = 'connected';
        ElMessage.success('银行卡绑定成功（模拟）！');
      }
    }
  } catch (error) {
    ElMessage.error('操作失败，请重试');
    console.error('绑定/解绑失败:', error);
  }
};

// 获取连接状态文本
const getConnectionStatusText = (card) => {
  switch (card.connectionStatus) {
    case 'connected': return '已连接';
    case 'disconnected': return '未连接';
    case 'connecting': return '连接中';
    case 'failed': return '连接失败';
    default: return '未知状态';
  }
};

// 获取连接状态类型
const getConnectionStatusType = (card) => {
  switch (card.connectionStatus) {
    case 'connected': return 'success';
    case 'disconnected': return 'info';
    case 'connecting': return 'warning';
    case 'failed': return 'danger';
    default: return 'info';
  }
};

// 获取连接按钮文本
const getConnectionButtonText = (card) => {
  if (card.connecting) return '处理中...';
  return card.connectionStatus === 'connected' ? '断开连接' : '连接银行卡';
};

// 获取连接按钮类型
const getConnectionButtonType = (card) => {
  if (card.connecting) return 'warning';
  return card.connectionStatus === 'connected' ? 'danger' : 'success';
};

// 切换银行卡连接状态
const toggleCardConnection = async (card) => {
  if (card.connecting) return;

  card.connecting = true;

  try {
    if (card.connectionStatus === 'connected') {
      // 断开连接
      const response = await bankCardApi.disconnectCard(card.cardId, currentUserId.value);
      if (response.code === 0) {
        card.connectionStatus = 'disconnected';
        card.isBound = false;
        ElMessage.success('银行卡连接已断开！');
      }
    } else {
      // 连接银行卡 - 这里可以添加验证码验证流程
      card.connectionStatus = 'connecting';

      // 模拟验证码验证（实际应用中应该弹出验证码输入框）
      const response = await bankCardApi.verifyCardConnection(card.cardId, '123456');
      if (response.code === 0 && response.data) {
        card.connectionStatus = 'connected';
        card.isBound = true;
        ElMessage.success('银行卡连接成功！');
      } else {
        card.connectionStatus = 'failed';
        ElMessage.error('银行卡连接失败，请重试！');
      }
    }
  } catch (error) {
    card.connectionStatus = 'failed';
    ElMessage.error('操作失败，请重试！');
    console.error('连接操作失败:', error);
  } finally {
    card.connecting = false;
  }
};

// 切换密码显示状态
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};


</script>

<style scoped>
.bankcard-container {
  padding: 20px;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
}

.header-left h2 {
  font-size: 28px;
  color: #2c3e50;
  margin: 0 0 5px 0;
}

.header-left p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.header-actions .el-button {
  margin-left: 0;
}

.active-btn {
  background-color: #409eff !important;
  border-color: #409eff !important;
  color: white !important;
}

/* 数据类型指示器 */
.data-type-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 15px 25px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.data-type-indicator .el-tag {
  margin-right: 15px;
  font-size: 16px;
  padding: 8px 16px;
  font-weight: bold;
}

.data-count {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  margin-right: 20px;
}

.total-balance {
  font-size: 16px;
  color: #27ae60;
  font-weight: 600;
}

/* 统计卡片 */
.card-stats {
  margin-bottom: 30px;
}

.stat-card {
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  font-size: 48px;
  margin-right: 20px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-bottom: 3px;
}

.stat-desc {
  font-size: 12px;
  color: #999;
}

/* 不同类型的统计卡片 */
.total-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.total-card .stat-number,
.total-card .stat-label,
.total-card .stat-desc {
  color: white;
}

.debit-card {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.debit-card .stat-number,
.debit-card .stat-label,
.debit-card .stat-desc {
  color: white;
}

.credit-card {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.credit-card .stat-number,
.credit-card .stat-label,
.credit-card .stat-desc {
  color: white;
}

.default-card {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.default-card .stat-number,
.default-card .stat-label,
.default-card .stat-desc {
  color: white;
}

/* 银行卡列表 */
.card-list {
  border-radius: 12px;
  border: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.bank-cards {
  padding: 0;
}

.bank-card-item {
  display: flex;
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 12px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.bank-card-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.bank-card-item:last-child {
  margin-bottom: 0;
}

/* 银行卡视觉效果 */
.card-visual {
  margin-right: 20px;
}

.card-bg {
  width: 280px;
  height: 180px;
  border-radius: 16px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.card-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
  opacity: 0.3;
}

.card-content {
  position: relative;
  z-index: 2;
  padding: 25px;
  color: white;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-bank {
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.card-number {
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 2px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.card-balance {
  font-size: 24px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin: 8px 0;
  color: rgba(255, 255, 255, 0.95);
}

.card-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-holder {
  font-size: 14px;
  opacity: 0.9;
}

.card-type-badge {
  font-size: 12px;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 6px;
  backdrop-filter: blur(10px);
}

/* 银行卡详情 */
.card-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-main-info h3 {
  font-size: 20px;
  color: #2c3e50;
  margin: 0 0 10px 0;
}

.card-number-full {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0 0 15px 0;
  font-family: 'Courier New', monospace;
}

.card-status {
  margin-bottom: 20px;
}

.card-status .el-tag {
  margin-right: 10px;
}

.card-actions {
  display: flex;
  gap: 10px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.empty-state p {
  font-size: 16px;
  margin-bottom: 20px;
}

/* 银行卡详情对话框 */
.card-details-content {
  text-align: center;
}

.detail-card {
  margin-bottom: 20px;
}

.detail-card .card-bg {
  width: 100%;
  max-width: 300px;
  height: 190px;
  margin: 0 auto;
}

.detail-info {
  text-align: left;
  margin-top: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f1f3f4;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 600;
  color: #2c3e50;
}

.info-item span {
  color: #7f8c8d;
}

.balance-amount {
  font-size: 18px;
  font-weight: 700;
  color: #27ae60 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bank-card-item {
    flex-direction: column;
  }

  .card-visual {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .card-bg {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }

  .card-actions {
    justify-content: center;
  }

  .card-stats .el-col {
    margin-bottom: 15px;
  }
}
</style>