[2025-06-25T12:10:38.770] [DEBUG] debug-file - session manager: set active socket. socketId=IRr0CQQuKtTUZMbQAAAz
[2025-06-25T12:10:38.846] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-06-25T12:10:38.861] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-06-25T12:10:38.863] [DEBUG] debug-file - Since current hvigor version 5.15.3 differs from last hvigor version 
      undefined, delete file-cache.json and task-cache.json.
[2025-06-25T12:10:38.863] [DEBUG] debug-file - Cache service initialization finished in 2 ms 
[2025-06-25T12:10:38.874] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\harmony\hvigorfile.ts
[2025-06-25T12:10:38.878] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T12:10:38.878] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T12:10:38.885] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-06-25T12:10:38.885] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-06-25T12:10:38.885] [DEBUG] debug-file - Product 'default' build option: {}
[2025-06-25T12:10:38.885] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-06-25T12:10:38.887] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-06-25T12:10:38.891] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-06-25T12:10:38.900] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-06-25T12:10:38.930] [DEBUG] debug-file - Sdk init in 38 ms 
[2025-06-25T12:10:38.957] [DEBUG] debug-file - Project task initialization takes 26 ms 
[2025-06-25T12:10:38.957] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T12:10:38.957] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\harmony\hvigorfile.ts
[2025-06-25T12:10:38.957] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\harmony\hvigorfile.ts
[2025-06-25T12:10:38.966] [DEBUG] debug-file - hvigorfile, resolving D:\vue\daxiangmuwallet\harmony\entry\hvigorfile.ts
[2025-06-25T12:10:38.970] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-06-25T12:10:38.970] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-06-25T12:10:38.976] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2025-06-25T12:10:38.977] [DEBUG] debug-file - Target 'default' config: {}
[2025-06-25T12:10:38.977] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-06-25T12:10:38.977] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
}
[2025-06-25T12:10:38.977] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "default"
}
[2025-06-25T12:10:38.978] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2025-06-25T12:10:38.978] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  },
  "name": "debug"
} in this build.
[2025-06-25T12:10:38.980] [DEBUG] debug-file - Module entry task initialization takes 1 ms 
[2025-06-25T12:10:38.980] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-06-25T12:10:38.980] [DEBUG] debug-file - hvigorfile, no custom plugins were found in D:\vue\daxiangmuwallet\harmony\entry\hvigorfile.ts
[2025-06-25T12:10:38.980] [DEBUG] debug-file - hvigorfile, resolve finished D:\vue\daxiangmuwallet\harmony\entry\hvigorfile.ts
[2025-06-25T12:10:38.998] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 17 ms 
[2025-06-25T12:10:39.000] [DEBUG] debug-file - project has submodules:entry
[2025-06-25T12:10:39.001] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-06-25T12:10:39.005] [DEBUG] debug-file - load to the disk finished
[2025-06-25T12:10:39.006] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T12:10:39.011] [DEBUG] debug-file - Module harmony Collected Dependency: 
[2025-06-25T12:10:39.011] [DEBUG] debug-file - Module harmony's total dependency: 0
[2025-06-25T12:10:39.011] [DEBUG] debug-file - Start to initialize dependency information.
[2025-06-25T12:10:39.013] [DEBUG] debug-file - Module entry Collected Dependency: 
[2025-06-25T12:10:39.013] [DEBUG] debug-file - Module entry's total dependency: 0
[2025-06-25T12:10:39.015] [DEBUG] debug-file - Configuration phase cost:148 ms 
[2025-06-25T12:10:39.017] [DEBUG] debug-file - Configuration task cost before running: 167 ms 
[2025-06-25T12:10:39.020] [DEBUG] debug-file - Executing task :entry:clean
[2025-06-25T12:10:39.020] [DEBUG] debug-file - clean: Worker pool is inactive.
[2025-06-25T12:10:39.158] [DEBUG] debug-file - entry : clean cost memory -0.0767669677734375
[2025-06-25T12:10:39.158] [DEBUG] debug-file - runTaskFromQueue task cost before running: 308 ms 
[2025-06-25T12:10:39.158] [INFO] debug-file - Finished :entry:clean... after 139 ms 
[2025-06-25T12:10:39.161] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.161] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.164] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2025-06-25T12:10:39.171] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.171] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.199] [DEBUG] debug-file - current product is not Atomic service.
[2025-06-25T12:10:39.200] [DEBUG] debug-file - Use tool [win32: JAVA_HOME, CLASSPATH]
 [
  { JAVA_HOME: 'D:\\app\\devecostudio\\DevEco Studio\\jbr' },
  { CLASSPATH: undefined }
]
[2025-06-25T12:10:39.200] [DEBUG] debug-file - Use tool [win32: NODE_HOME]
 [ { NODE_HOME: 'D:\\app\\devecostudio\\DevEco Studio\\tools\\node' } ]
[2025-06-25T12:10:39.201] [DEBUG] debug-file - entry : default@PreBuild cost memory 1.0117568969726562
[2025-06-25T12:10:39.201] [DEBUG] debug-file - runTaskFromQueue task cost before running: 351 ms 
[2025-06-25T12:10:39.203] [INFO] debug-file - Finished :entry:default@PreBuild... after 37 ms 
[2025-06-25T12:10:39.205] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.205] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.206] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2025-06-25T12:10:39.207] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.207] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.207] [DEBUG] debug-file - Change app api release type with 'Release'
[2025-06-25T12:10:39.207] [DEBUG] debug-file - Change app compile API version with '*********'
[2025-06-25T12:10:39.207] [DEBUG] debug-file - Change app target API version with '50003015'
[2025-06-25T12:10:39.207] [DEBUG] debug-file - Change app minimum API version with '50003015'
[2025-06-25T12:10:39.207] [DEBUG] debug-file - Use cli appEnvironment
[2025-06-25T12:10:39.210] [DEBUG] debug-file - entry : default@MergeProfile cost memory -0.6472015380859375
[2025-06-25T12:10:39.210] [DEBUG] debug-file - runTaskFromQueue task cost before running: 360 ms 
[2025-06-25T12:10:39.210] [INFO] debug-file - Finished :entry:default@MergeProfile... after 4 ms 
[2025-06-25T12:10:39.212] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.212] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.213] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2025-06-25T12:10:39.214] [DEBUG] debug-file - Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-06-25T12:10:39.214] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.214] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.215] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.101654052734375
[2025-06-25T12:10:39.215] [DEBUG] debug-file - runTaskFromQueue task cost before running: 365 ms 
[2025-06-25T12:10:39.215] [INFO] debug-file - Finished :entry:default@CreateBuildProfile... after 3 ms 
[2025-06-25T12:10:39.218] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.218] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.219] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2025-06-25T12:10:39.219] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.219] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.219] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.03693389892578125
[2025-06-25T12:10:39.219] [DEBUG] debug-file - runTaskFromQueue task cost before running: 369 ms 
[2025-06-25T12:10:39.219] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2025-06-25T12:10:39.221] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.221] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.227] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2025-06-25T12:10:39.227] [DEBUG] debug-file - Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-06-25T12:10:39.228] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.04660797119140625
[2025-06-25T12:10:39.228] [DEBUG] debug-file - runTaskFromQueue task cost before running: 379 ms 
[2025-06-25T12:10:39.228] [INFO] debug-file - Finished :entry:default@GeneratePkgContextInfo... after 2 ms 
[2025-06-25T12:10:39.230] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.231] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.232] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2025-06-25T12:10:39.232] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.232] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.233] [DEBUG] debug-file - [
  'D:\\app\\devecostudio\\DevEco Studio\\tools\\node\\node.exe',
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\ets\\build-tools\\ets-loader\\bin\\ark\\ts2abc.js',
  '--target-api-version',
  '15'
]
[2025-06-25T12:10:39.326] [DEBUG] debug-file - ********
[2025-06-25T12:10:39.330] [DEBUG] debug-file - entry : default@ProcessProfile cost memory -0.5177154541015625
[2025-06-25T12:10:39.330] [DEBUG] debug-file - runTaskFromQueue task cost before running: 480 ms 
[2025-06-25T12:10:39.330] [INFO] debug-file - Finished :entry:default@ProcessProfile... after 99 ms 
[2025-06-25T12:10:39.333] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.333] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.335] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2025-06-25T12:10:39.337] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.337] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.338] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.12575531005859375
[2025-06-25T12:10:39.338] [DEBUG] debug-file - runTaskFromQueue task cost before running: 488 ms 
[2025-06-25T12:10:39.339] [INFO] debug-file - Finished :entry:default@ProcessRouterMap... after 4 ms 
[2025-06-25T12:10:39.341] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.341] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.342] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewProcessResource
[2025-06-25T12:10:39.345] [DEBUG] debug-file - Executing task :entry:default@PreviewProcessResource
[2025-06-25T12:10:39.345] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.345] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.345] [DEBUG] debug-file - entry : default@PreviewProcessResource cost memory 0.07025146484375
[2025-06-25T12:10:39.346] [DEBUG] debug-file - runTaskFromQueue task cost before running: 497 ms 
[2025-06-25T12:10:39.348] [INFO] debug-file - Finished :entry:default@PreviewProcessResource... after 2 ms 
[2025-06-25T12:10:39.350] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.351] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.354] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2025-06-25T12:10:39.370] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory -0.110076904296875
[2025-06-25T12:10:39.370] [DEBUG] debug-file - runTaskFromQueue task cost before running: 520 ms 
[2025-06-25T12:10:39.375] [INFO] debug-file - Finished :entry:default@GenerateLoaderJson... after 17 ms 
[2025-06-25T12:10:39.378] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.378] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.379] [DEBUG] debug-file - restool module names: entry; moduleName=entry, taskName=default@PreviewCompileResource
[2025-06-25T12:10:39.381] [DEBUG] debug-file - Executing task :entry:default@PreviewCompileResource
[2025-06-25T12:10:39.402] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\vue\\daxiangmuwallet\\harmony\\AppScope\\resources',
  '-o',
  'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled'
]
[2025-06-25T12:10:39.403] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 120492032,
  heapTotal: 125394944,
  heapUsed: 111454840,
  external: 3146840,
  arrayBuffers: 140743
} os memoryUsage :13.020099639892578
[2025-06-25T12:10:39.435] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T12:10:39.438] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-x',
  'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\resources',
  '-o',
  'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled'
]
[2025-06-25T12:10:39.441] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 120745984,
  heapTotal: 125394944,
  heapUsed: 111720120,
  external: 3155158,
  arrayBuffers: 149076
} os memoryUsage :13.02145767211914
[2025-06-25T12:10:39.507] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T12:10:39.510] [DEBUG] debug-file - Use tool [D:\app\devecostudio\DevEco Studio\sdk\default\openharmony\toolchains\restool.exe]
 [
  'D:\\app\\devecostudio\\DevEco Studio\\sdk\\default\\openharmony\\toolchains\\restool.exe',
  '-m',
  'entry',
  '-f',
  '-j',
  'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\process_profile\\default\\module.json',
  '-p',
  'c***y',
  '-r',
  'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\generated\\r\\default\\ResourceTable.h',
  '-z',
  '--ids',
  'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map',
  '--defined-ids',
  'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ids_map\\id_defined.json',
  '-i',
  'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\app_compiled',
  '-i',
  'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\module_compiled',
  '-o',
  'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default'
]
[2025-06-25T12:10:39.512] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 121073664,
  heapTotal: 125394944,
  heapUsed: 112009552,
  external: 3155284,
  arrayBuffers: 150086
} os memoryUsage :13.019607543945312
[2025-06-25T12:10:39.578] [DEBUG] debug-file - Info: restool resources compile success.

[2025-06-25T12:10:39.583] [DEBUG] debug-file - entry : default@PreviewCompileResource cost memory -0.6025009155273438
[2025-06-25T12:10:39.583] [DEBUG] debug-file - runTaskFromQueue task cost before running: 733 ms 
[2025-06-25T12:10:39.584] [INFO] debug-file - Finished :entry:default@PreviewCompileResource... after 203 ms 
[2025-06-25T12:10:39.586] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.586] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.586] [DEBUG] debug-file - Executing task :entry:default@PreviewHookCompileResource
[2025-06-25T12:10:39.586] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.586] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.586] [DEBUG] debug-file - entry : default@PreviewHookCompileResource cost memory 0.03804779052734375
[2025-06-25T12:10:39.586] [DEBUG] debug-file - runTaskFromQueue task cost before running: 737 ms 
[2025-06-25T12:10:39.586] [INFO] debug-file - Finished :entry:default@PreviewHookCompileResource... after 1 ms 
[2025-06-25T12:10:39.588] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.588] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.589] [DEBUG] debug-file - Executing task :entry:default@CopyPreviewProfile
[2025-06-25T12:10:39.589] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.589] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.594] [DEBUG] debug-file - entry : default@CopyPreviewProfile cost memory 0.16473388671875
[2025-06-25T12:10:39.594] [DEBUG] debug-file - runTaskFromQueue task cost before running: 745 ms 
[2025-06-25T12:10:39.595] [INFO] debug-file - Finished :entry:default@CopyPreviewProfile... after 6 ms 
[2025-06-25T12:10:39.596] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.596] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.597] [DEBUG] debug-file - Executing task :entry:default@ReplacePreviewerPage
[2025-06-25T12:10:39.597] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.597] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.597] [DEBUG] debug-file - entry : default@ReplacePreviewerPage cost memory 0.037994384765625
[2025-06-25T12:10:39.597] [DEBUG] debug-file - runTaskFromQueue task cost before running: 748 ms 
[2025-06-25T12:10:39.597] [INFO] debug-file - Finished :entry:default@ReplacePreviewerPage... after 1 ms 
[2025-06-25T12:10:39.599] [DEBUG] debug-file - Executing task :entry:buildPreviewerResource
[2025-06-25T12:10:39.599] [DEBUG] debug-file - entry : buildPreviewerResource cost memory 0.011688232421875
[2025-06-25T12:10:39.599] [DEBUG] debug-file - runTaskFromQueue task cost before running: 749 ms 
[2025-06-25T12:10:39.599] [INFO] debug-file - Finished :entry:buildPreviewerResource... after 1 ms 
[2025-06-25T12:10:39.601] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.601] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.602] [DEBUG] debug-file - Executing task :entry:default@PreviewUpdateAssets
[2025-06-25T12:10:39.603] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.603] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.605] [DEBUG] debug-file - entry : default@PreviewUpdateAssets cost memory 0.1083831787109375
[2025-06-25T12:10:39.605] [DEBUG] debug-file - runTaskFromQueue task cost before running: 755 ms 
[2025-06-25T12:10:39.606] [INFO] debug-file - Finished :entry:default@PreviewUpdateAssets... after 4 ms 
[2025-06-25T12:10:39.608] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-06-25T12:10:39.608] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2025-06-25T12:10:39.612] [DEBUG] debug-file - Executing task :entry:default@PreviewArkTS
[2025-06-25T12:10:39.629] [DEBUG] debug-file - session manager: binding session. socketId=IRr0CQQuKtTUZMbQAAAz, threadId=1@25.
[2025-06-25T12:10:39.631] [DEBUG] debug-file - entry : default@PreviewArkTS cost memory 1.011810302734375
[2025-06-25T12:10:43.758] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchStart
[2025-06-25T12:10:43.759] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchStart
[2025-06-25T12:10:47.907] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:47.909] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:47.908] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:47.910] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:47.909] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:47.911] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:47.911] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:47.913] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:47.911] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:47.914] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:47.912] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:47.916] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:47.914] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:47.994] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:47.994] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:47.997] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:47.997] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.000] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.000] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.002] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.002] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.016] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.016] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.017] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.017] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.019] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.019] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.020] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.020] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.022] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.022] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.025] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.025] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.028] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.028] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.029] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.029] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.031] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.031] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.032] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.032] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.034] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.034] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.036] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.035] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.043] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.043] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.046] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.046] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.047] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.047] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.048] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.048] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.050] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.049] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.052] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.052] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.054] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.053] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.056] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.054] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.057] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.055] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.058] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.057] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.059] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.058] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.060] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.059] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.061] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.060] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.063] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.061] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.063] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.063] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.065] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.065] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.067] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.067] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.068] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.068] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.070] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.070] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.072] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.071] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.073] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.072] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.074] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.074] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.075] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.075] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.077] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.076] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.078] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.077] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.079] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.078] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.080] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.079] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.082] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.082] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.084] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.083] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.086] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.085] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.088] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.086] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.089] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.087] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.090] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.089] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.092] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.090] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.093] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.092] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.095] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.094] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.097] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.096] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.099] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.099] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.102] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.101] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.104] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.103] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.105] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.104] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.106] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.106] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.108] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.107] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.110] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.108] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.111] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.110] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.113] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.111] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.114] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.112] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.115] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.114] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.116] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.115] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.118] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.117] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.119] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.118] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.120] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.119] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.121] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.120] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.122] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.122] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.124] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.123] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.125] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.125] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.127] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.127] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.129] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.128] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.130] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.130] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.132] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.131] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.133] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.132] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.134] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.133] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.134] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.134] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.135] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.134] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.136] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.135] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.137] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.136] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.138] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.137] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.139] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.137] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.140] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.138] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.140] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.139] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.141] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.140] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.143] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.141] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.144] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.141] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.145] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.142] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.146] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.143] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.147] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.144] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.148] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.146] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.149] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.146] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.150] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.147] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.151] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.148] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.152] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.148] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.153] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.149] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.153] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.150] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.154] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.151] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.156] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.152] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.158] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.152] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.160] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.153] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.161] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.154] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.162] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:49.004] [WARN] debug-file - Error [RollupError]: 'return' outside of function (Note that you need plugins to import files that are not JavaScript)
    at error (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:210:30)
    at Module.error (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:13962:16)
    at Module.tryParse (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:14728:25)
    at Module.setSource (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:14257:37)
    at ModuleLoader.addModuleSource (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:24000:20) {
  cause: SyntaxError: 'return' outside of function (1244:12)
      at pp$4.raise (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:21395:13)
      at pp$8.parseReturnStatement (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19008:12)
      at pp$8.parseStatement (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18844:37)
      at pp$8.parseBlock (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19172:21)
      at pp$8.parseStatement (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18854:36)
      at pp$8.parseIfStatement (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19001:26)
      at pp$8.parseStatement (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18843:33)
      at pp$8.parseBlock (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19172:21)
      at pp$8.parseTryStatement (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:19075:21)
      at pp$8.parseStatement (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor-ohos-plugin\node_modules\@ohos\hvigor-arkts-base\node_modules\rollup\dist\shared\rollup.js:18847:34) {
    pos: 45895,
    loc: Position { line: 1244, column: 12 },
    raisedAt: 45901
  },
  code: 'PARSE_ERROR',
  id: 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\BankCardPage.ets',
  pos: 45895,
  loc: {
    column: 8,
    file: 'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\BankCardPage.ets',
    line: 996
  },
  frame: "994:         console.error('无法获取用户ID');\n" +
    "995:         promptAction.showToast({ message: '用户信息获取失败' });\n" +
    '996:         return;\n' +
    '             ^\n' +
    '997:       }',
  watchFiles: [
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\.preview\\default\\intermediates\\res\\default\\ResourceTable.txt',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\oh-package.json5',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\entryability\\EntryAbility.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\entrybackupability\\EntryBackupAbility.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\RegisterPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\ForgotPasswordPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\Index.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\LoginPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\MainPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\BankCardPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\BankCardDetailPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\AddBankCardPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\TransactionDetailPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\TransactionListPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\WalletOperationPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\PaymentPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\ChangePayPasswordPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\PayLimitSettingPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\SettingsPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\BankSelectorPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\CardTypeSelectorPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\HelpCenterPage.ets',
    'D:\\vue\\daxiangmuwallet\\harmony\\entry\\src\\main\\ets\\pages\\AboutPage.ets'
  ]
}
[2025-06-25T12:10:48.154] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.163] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.155] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.164] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.156] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.165] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.157] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.166] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.158] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.168] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.160] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.169] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.162] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.170] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.163] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.177] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.164] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.184] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.165] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.203] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.167] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.211] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.168] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.214] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.169] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.215] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.170] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.217] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.179] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.218] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.181] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.219] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.183] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.220] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.188] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.221] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.189] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.222] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.190] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.224] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.190] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.227] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.191] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.229] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.192] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.231] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.192] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.233] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.193] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.235] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.194] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.237] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.194] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.239] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.195] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.241] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.196] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.243] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.196] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.245] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.197] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.246] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.198] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.247] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.199] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.248] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.200] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.249] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.200] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.251] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.201] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.252] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.202] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.253] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.203] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.254] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.203] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.255] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.205] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.256] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.206] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.257] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.206] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.259] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.207] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.260] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.209] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.261] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.209] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.262] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.210] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.263] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.211] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.264] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.212] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.265] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.213] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.266] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.213] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.267] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.214] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.268] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.215] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.269] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.215] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.270] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.216] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.271] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.217] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.272] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.218] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.273] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.219] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.274] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.219] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.275] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.220] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.276] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.221] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.276] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.222] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.277] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.223] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.278] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.224] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.279] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.226] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.280] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.227] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.281] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.229] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.282] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.230] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.283] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.231] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.284] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.232] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.285] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.233] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.286] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.234] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.287] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.236] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.288] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.237] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.290] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.238] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.292] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.239] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.293] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.240] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.294] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.241] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.295] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.242] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.296] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.242] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.297] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.243] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.298] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.244] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.299] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.244] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.301] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.245] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.303] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.246] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.304] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.247] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.305] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.248] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.307] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.249] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.308] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.250] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.309] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.251] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.310] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.252] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.312] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.253] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.313] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.253] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.314] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.254] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.316] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.256] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.318] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.257] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.319] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.258] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.321] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.259] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.322] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.260] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.323] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.260] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.324] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.261] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.325] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.262] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.327] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.263] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.329] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.263] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.331] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.265] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.333] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.266] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.335] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.268] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.337] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.269] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.338] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.271] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.340] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.272] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.342] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.274] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.344] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.275] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.346] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.276] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.347] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.278] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.349] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.279] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.349] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.280] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.351] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.281] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.352] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.282] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.353] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.282] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.355] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.283] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.356] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.284] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.359] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.285] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.360] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.286] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.362] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.287] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.365] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.288] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.368] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.289] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.370] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.289] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.371] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.290] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.372] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.291] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.374] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.291] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.375] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.293] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.377] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.294] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.379] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.296] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.381] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.297] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.382] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.299] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.384] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.300] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.386] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.302] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.387] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.304] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.388] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.305] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.389] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.307] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.390] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.307] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.391] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.309] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.392] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.310] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.393] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.312] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.394] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.313] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.395] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.314] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.396] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.316] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.397] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.317] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.398] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.318] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.398] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.320] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.399] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.321] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.400] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.323] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.401] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.325] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.403] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.326] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.405] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.327] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.406] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.329] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.408] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.329] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.410] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.330] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.411] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.331] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.412] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.332] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.413] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.333] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.414] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.334] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.415] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.336] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.417] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.337] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.419] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.338] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.420] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.339] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.420] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.340] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.421] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.340] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.422] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.341] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.423] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.342] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.424] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.344] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.425] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.346] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.426] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.346] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.427] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.347] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.427] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.348] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.428] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.350] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.429] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.351] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.430] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.351] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.431] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.352] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.432] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.353] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.434] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.354] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.435] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.354] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.437] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.355] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.438] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.357] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.440] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.359] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.451] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.361] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.455] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.362] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.457] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.364] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.459] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.365] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.461] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.368] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.463] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.369] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.472] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.370] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.474] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.372] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.475] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.374] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.478] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.375] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.480] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.376] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.482] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.378] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.484] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.379] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.486] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.381] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.488] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.382] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.489] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.383] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.490] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.385] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.492] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.386] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.493] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.387] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.494] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.388] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.495] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.389] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.497] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.390] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.499] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.390] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.500] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.391] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.501] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.392] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.501] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.392] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.502] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.393] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.503] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.394] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.504] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.394] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.506] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.395] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.507] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.396] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.509] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.397] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.510] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.397] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.511] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.398] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.512] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.399] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.513] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.399] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.514] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.401] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.515] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.402] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.516] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.403] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.517] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.403] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.518] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.404] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.519] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.405] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.520] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.405] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.520] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.406] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.521] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.407] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.522] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.408] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.523] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.409] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.524] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.409] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.525] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.410] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.526] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.411] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.528] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.412] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.530] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.412] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.532] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.413] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.534] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.414] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.537] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.414] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.539] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.415] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.541] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.416] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.542] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.417] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.544] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.417] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.545] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.418] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.547] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.419] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.548] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.420] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.549] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.420] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.551] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.421] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.552] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.422] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.554] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.422] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.555] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.423] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.557] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.424] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.559] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.425] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.561] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.427] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.563] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.428] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.564] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.429] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.566] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.430] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.567] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.431] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.569] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.432] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.570] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.434] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.572] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.436] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.573] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.438] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.575] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.439] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.577] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.440] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.579] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.453] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.580] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.455] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.581] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.456] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.582] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.458] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.584] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.459] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.585] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.460] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.586] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.461] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.588] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.463] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.590] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.464] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.592] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.473] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.593] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.475] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.595] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.478] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.596] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.479] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.596] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.481] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.597] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.483] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.599] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.484] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.600] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.486] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.601] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.487] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.602] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.489] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.603] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.490] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.604] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.491] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.605] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.491] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.606] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.492] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.607] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.493] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.608] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.494] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.609] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.496] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.610] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.497] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.611] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.498] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.612] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.499] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.612] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.499] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.613] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.500] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.615] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.501] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.616] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.502] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.617] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.503] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.619] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.504] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.620] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.504] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.622] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.505] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.623] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.506] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.624] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.506] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.625] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.507] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.626] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.507] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.628] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.508] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.629] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.509] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.632] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.509] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.644] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.511] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.646] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.512] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.649] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.514] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.651] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.514] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.656] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.515] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.659] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.516] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.662] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.517] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.664] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.518] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.666] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.519] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.668] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.520] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.670] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.521] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.671] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.522] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.674] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.524] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.675] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.525] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.683] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.527] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.686] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.528] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.688] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.529] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.689] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.530] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.690] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.531] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.691] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.532] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.692] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.533] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.693] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.534] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.694] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.535] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.695] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.536] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.696] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.538] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.697] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.539] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.698] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.541] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.699] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.542] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.700] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.543] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.701] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.545] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.702] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.547] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.703] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.548] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.704] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.551] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.705] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.555] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.707] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.557] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.708] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.558] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.709] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.562] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.710] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.564] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.712] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.566] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.714] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.568] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.716] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.569] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.718] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.571] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.719] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.573] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.720] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.574] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.721] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.576] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.722] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.579] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.724] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.580] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.726] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.580] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.728] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.581] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.730] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.582] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.731] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.582] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.732] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.583] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.733] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.584] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.734] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.585] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.735] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.586] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.736] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.588] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.737] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.589] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.738] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.591] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.739] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.592] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.740] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.594] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.741] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.595] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.742] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.596] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.743] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.598] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.744] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.600] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.745] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.601] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.746] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.603] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.746] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.603] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.748] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.604] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.748] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.605] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.749] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.606] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.751] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.606] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.752] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.607] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.753] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.608] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.753] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.608] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.755] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.609] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.756] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.610] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.757] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.610] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.758] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.611] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.760] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.612] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.762] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.612] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.763] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.613] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.765] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.614] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.767] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.615] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.768] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.616] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.768] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.617] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.770] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.618] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.771] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.619] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.772] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.620] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.773] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.621] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.774] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.622] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.775] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.624] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.776] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.625] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.777] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.626] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.778] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.626] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.780] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.627] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.781] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.628] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.782] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.630] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.783] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.632] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.785] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.639] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.786] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.641] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.787] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.644] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.788] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.648] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.790] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.650] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.791] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.652] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.792] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.654] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.794] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.656] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.795] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.656] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.796] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.666] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.797] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.667] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.798] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.669] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.799] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.670] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.800] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.672] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.801] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.674] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.802] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.676] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.803] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.678] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.803] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.680] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.804] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.682] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.805] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.684] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.806] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.686] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.808] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.687] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.809] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.689] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.810] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.691] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.811] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.692] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.813] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.693] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.814] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.694] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.816] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.695] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.818] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.696] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.821] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.697] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.822] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.698] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.824] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.699] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.826] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.700] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.828] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.700] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.830] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.701] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.833] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.702] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.835] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.703] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.838] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.704] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.841] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.705] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.843] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.705] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.845] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.706] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.846] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.707] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.849] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.707] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.850] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.708] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.851] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.709] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.852] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.709] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.853] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.710] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.855] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.711] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.856] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.712] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.858] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.712] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.860] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.713] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.861] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.714] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.862] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.715] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.863] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.715] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.864] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.716] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.865] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.717] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.866] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.718] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.867] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.719] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.868] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.719] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.869] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.720] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.870] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.721] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.871] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.722] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.873] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.722] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.874] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.723] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.876] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.724] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.878] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.725] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.880] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.725] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.881] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.726] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.883] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.727] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.884] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.728] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.885] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.729] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.886] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.729] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.887] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.730] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.889] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.731] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.890] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.732] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.891] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.733] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.891] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.733] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.892] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.734] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.893] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.735] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.895] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.736] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.896] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.737] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.897] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.738] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.899] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.739] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.900] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.739] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.901] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.740] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.902] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.741] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.903] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.743] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.904] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.744] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.905] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.745] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.906] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.746] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.906] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.747] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.907] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.748] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.908] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.749] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.909] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.749] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.911] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.750] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.911] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.751] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.912] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.752] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.913] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.752] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.914] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.753] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.915] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.754] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.916] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.755] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.917] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.756] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.918] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.757] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.920] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.757] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.921] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.758] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.924] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.759] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.927] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.759] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.928] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.760] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.929] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.761] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.930] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.761] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.932] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.762] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.934] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.763] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.935] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.764] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.936] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.765] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.937] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.765] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.938] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.766] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.939] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.767] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.940] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.768] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.941] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.769] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.942] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.770] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.944] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.771] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.944] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.772] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.946] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.772] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.947] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.773] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.948] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.774] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.949] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.775] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.950] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.776] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.951] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.777] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.954] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.778] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.956] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.779] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.957] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.780] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.959] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.781] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.960] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.782] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.961] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.782] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.962] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.783] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.964] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.784] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.965] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.784] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.966] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.785] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.968] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.786] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.969] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.787] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.970] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.788] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.971] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.788] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.972] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.789] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.973] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.790] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.974] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.790] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.975] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.791] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.977] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.792] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.978] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.792] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.978] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.793] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.979] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.794] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.980] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.794] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.981] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.795] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.982] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.796] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.983] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.796] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.984] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.797] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.985] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.798] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.988] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.798] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.989] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.799] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.990] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.800] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.992] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.800] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.993] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.801] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.994] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.802] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.995] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.802] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.996] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.803] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.998] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.804] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:48.999] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.971] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:49.000] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.972] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:49.001] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:48.998] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:49.002] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:49.005] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchResult
[2025-06-25T12:10:49.006] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchResult
[2025-06-25T12:10:49.010] [DEBUG] debug-file - default@PreviewArkTS watch work[25] failed.
[2025-06-25T12:10:49.010] [ERROR] debug-file - Failed :entry:default@PreviewArkTS... 
[2025-06-25T12:10:49.010] [DEBUG] debug-file - hvigor build process will be closed with an error.
[2025-06-25T12:10:49.011] [DEBUG] debug-file - ERROR: stacktrace = Error: Compilation failed
    at handleResponse (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-25T12:10:49.011] [ERROR] debug-file - Error: Compilation failed
    at handleResponse (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:1683)
    at async Worker.<anonymous> (D:\app\devecostudio\DevEco Studio\tools\hvigor\hvigor\src\base\internal\pool\worker-pool\watch-worker.js:1:2871)
[2025-06-25T12:10:49.021] [WARN] debug-file - BUILD FAILED in 10 s 171 ms 
[2025-06-25T12:10:49.022] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\harmony\AppScope\app.json5 cache by regenerate.
[2025-06-25T12:10:49.023] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\harmony\entry\src\main\module.json5 cache by regenerate.
[2025-06-25T12:10:49.023] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\harmony\build-profile.json5 cache by regenerate.
[2025-06-25T12:10:49.023] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\harmony\entry\build-profile.json5 cache by regenerate.
[2025-06-25T12:10:49.023] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\harmony\entry\src\main\resources\base\profile\main_pages.json cache by regenerate.
[2025-06-25T12:10:49.024] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\harmony\hvigor\hvigor-config.json5 cache by regenerate.
[2025-06-25T12:10:49.024] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\harmony\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-25T12:10:49.024] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\harmony\entry\oh-package.json5 cache by regenerate.
[2025-06-25T12:10:49.024] [DEBUG] debug-file - Update task entry:default@PreBuild input file:D:\vue\daxiangmuwallet\harmony\oh-package.json5 cache by regenerate.
[2025-06-25T12:10:49.024] [DEBUG] debug-file - Incremental task entry:default@PreBuild post-execution cost:4 ms .
[2025-06-25T12:10:49.024] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:D:\vue\daxiangmuwallet\harmony\AppScope\app.json5 cache by regenerate.
[2025-06-25T12:10:49.025] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:D:\vue\daxiangmuwallet\harmony\build-profile.json5 cache by regenerate.
[2025-06-25T12:10:49.025] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:D:\vue\daxiangmuwallet\harmony\entry\src\main\module.json5 cache by regenerate.
[2025-06-25T12:10:49.025] [DEBUG] debug-file - Update task entry:default@MergeProfile output file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\merge_profile\default\module.json cache.
[2025-06-25T12:10:49.025] [DEBUG] debug-file - Incremental task entry:default@MergeProfile post-execution cost:1 ms .
[2025-06-25T12:10:49.025] [DEBUG] debug-file - Update task entry:default@CreateBuildProfile input file:D:\vue\daxiangmuwallet\harmony\AppScope\app.json5 cache by regenerate.
[2025-06-25T12:10:49.026] [DEBUG] debug-file - Update task entry:default@CreateBuildProfile input file:D:\vue\daxiangmuwallet\harmony\build-profile.json5 cache by regenerate.
[2025-06-25T12:10:49.026] [DEBUG] debug-file - Update task entry:default@CreateBuildProfile output file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\generated\profile\default\BuildProfile.ets cache.
[2025-06-25T12:10:49.026] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile post-execution cost:1 ms .
[2025-06-25T12:10:49.026] [DEBUG] debug-file - Update task entry:default@GeneratePkgContextInfo output file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache.
[2025-06-25T12:10:49.026] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .
[2025-06-25T12:10:49.026] [DEBUG] debug-file - Update task entry:default@ProcessProfile input file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-25T12:10:49.027] [DEBUG] debug-file - Update task entry:default@ProcessProfile output file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\process_profile\default\module.json cache.
[2025-06-25T12:10:49.027] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile post-execution cost:1 ms .
[2025-06-25T12:10:49.028] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:D:\vue\daxiangmuwallet\harmony\entry\oh-package.json5 cache by regenerate.
[2025-06-25T12:10:49.028] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:D:\vue\daxiangmuwallet\harmony\oh-package.json5 cache by regenerate.
[2025-06-25T12:10:49.028] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:D:\vue\daxiangmuwallet\harmony\entry\src\main\module.json5 cache by regenerate.
[2025-06-25T12:10:49.029] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-25T12:10:49.029] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap output file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\router_map\default\temp-router-map.json cache.
[2025-06-25T12:10:49.029] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap output file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\router_map\default\loader-router-map.json cache.
[2025-06-25T12:10:49.029] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap post-execution cost:3 ms .
[2025-06-25T12:10:49.032] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson input file:D:\vue\daxiangmuwallet\harmony\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-25T12:10:49.032] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson input file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-25T12:10:49.033] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson input file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\router_map\default\temp-router-map.json cache by regenerate.
[2025-06-25T12:10:49.033] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson output file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\loader\default\loader.json cache.
[2025-06-25T12:10:49.033] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson post-execution cost:5 ms .
[2025-06-25T12:10:49.034] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\vue\daxiangmuwallet\harmony\entry\src\main\resources cache by regenerate.
[2025-06-25T12:10:49.041] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource input file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\merge_profile\default\module.json cache by regenerate.
[2025-06-25T12:10:49.041] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\res\default cache.
[2025-06-25T12:10:49.058] [DEBUG] debug-file - Update task entry:default@PreviewCompileResource output file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\generated\r\default cache.
[2025-06-25T12:10:49.058] [DEBUG] debug-file - Incremental task entry:default@PreviewCompileResource post-execution cost:25 ms .
[2025-06-25T12:10:49.058] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile input file:D:\vue\daxiangmuwallet\harmony\entry\src\main\resources\base\profile cache by regenerate.
[2025-06-25T12:10:49.059] [DEBUG] debug-file - Update task entry:default@CopyPreviewProfile output file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile cache.
[2025-06-25T12:10:49.060] [DEBUG] debug-file - Incremental task entry:default@CopyPreviewProfile post-execution cost:2 ms .
[2025-06-25T12:10:49.060] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\res\default\module.json cache.
[2025-06-25T12:10:49.061] [DEBUG] debug-file - Update task entry:default@PreviewUpdateAssets output file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile\main_pages.json cache.
[2025-06-25T12:10:49.061] [DEBUG] debug-file - Incremental task entry:default@PreviewUpdateAssets post-execution cost:2 ms .
[2025-06-25T12:10:49.065] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\loader\default cache by regenerate.
[2025-06-25T12:10:49.066] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\res\default\ResourceTable.txt cache by regenerate.
[2025-06-25T12:10:49.066] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\process_profile\default\module.json cache by regenerate.
[2025-06-25T12:10:49.066] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\res\default\resources\base\profile cache by regenerate.
[2025-06-25T12:10:49.067] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\harmony\entry\src\main\ets cache by regenerate.
[2025-06-25T12:10:49.079] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\loader\default\pkgContextInfo.json cache by regenerate.
[2025-06-25T12:10:49.079] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\generated\profile\default\BuildProfile.ets cache by regenerate.
[2025-06-25T12:10:49.080] [DEBUG] debug-file - Update task entry:default@PreviewArkTS input file:D:\vue\daxiangmuwallet\harmony\entry\src\mock\mock-config.json5 cache by regenerate.
[2025-06-25T12:10:49.080] [DEBUG] debug-file - Update task entry:default@PreviewArkTS output file:D:\vue\daxiangmuwallet\harmony\entry\.preview\default\intermediates\loader_out\default\ets cache.
[2025-06-25T12:10:49.080] [DEBUG] debug-file - Incremental task entry:default@PreviewArkTS post-execution cost:19 ms .
[2025-06-25T12:10:49.180] [DEBUG] debug-file - There's no busy workers and idle workers need cleanup.
[2025-06-25T12:10:49.180] [DEBUG] debug-file - hvigor build process will be closed.
[2025-06-25T12:10:49.187] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T12:10:49.188] [DEBUG] debug-file - session manager: send message to worker process.
[2025-06-25T12:10:49.766] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:49.770] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:49.770] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-25T12:10:49.949] [DEBUG] debug-file - watch worker: send response to session manager. Response type: WatchLog
[2025-06-25T12:10:49.951] [DEBUG] debug-file - session manager: watch message received from worker 1. Type: WatchLog
[2025-06-25T12:10:49.952] [DEBUG] debug-file - session manager: undefined socket. Type: WatchLog
[2025-06-25T12:10:49.006] [DEBUG] debug-file - Ark compile finished.
[2025-06-25T12:10:50.225] [DEBUG] debug-file - watch worker: worker is ready to be terminated.
[2025-06-25T12:10:50.225] [DEBUG] debug-file - Server currently has 0 watch-worker
