import request from '@/utils/request'

/**
 * 用户登录
 * @param {string} phone - 手机号
 * @param {string} password - 密码
 * @returns {Promise}
 */
export function login(phone, password) {
  // 使用JSON格式发送数据，匹配后端@RequestBody
  return request({
    url: '/api/user/login',
    method: 'post',
    data: {
      phone: phone,
      password: password,
      loginType: 'password'
    }
  })
}

/**
 * 发送验证码
 * @param {string} phone - 手机号
 * @param {number} type - 验证码类型 1-登录 2-操作
 * @returns {Promise}
 */
export function sendVerificationCode(phone, type) {
  return request({
    url: '/api/user/send-code',
    method: 'post',
    data: {
      phone: phone,
      type: type
    }
  })
}

/**
 * 验证码登录
 * @param {string} phone - 手机号
 * @param {string} verificationCode - 验证码
 * @returns {Promise}
 */
export function loginWithSms(phone, verificationCode) {
  return request({
    url: '/api/user/login',
    method: 'post',
    data: {
      phone: phone,
      verificationCode: verificationCode,
      loginType: 'sms'
    }
  })
}

/**
 * 用户注册
 * @param {string|Object} phone - 手机号或用户对象
 * @param {string} password - 密码
 * @param {string} confirmPassword - 确认密码
 * @returns {Promise}
 */
export function register(phone, password, confirmPassword) {
  // 如果第一个参数是对象，则直接使用
  if (typeof phone === 'object') {
    return request({
      url: '/api/user/register',
      method: 'post',
      data: phone
    })
  }

  // 使用JSON格式发送数据，匹配后端@RequestBody
  return request({
    url: '/api/user/register',
    method: 'post',
    data: {
      phone: phone,
      password: password,
      confirmPassword: confirmPassword
    }
  })
}

/**
 * 更新用户信息
 * @param {Object} userInfo - 用户信息
 * @returns {Promise}
 */
export function updateUser(userInfo) {
  return request({
    url: '/api/user',
    method: 'put',
    data: userInfo
  })
}

/**
 * 获取用户信息
 * @param {number} userId - 用户ID
 * @returns {Promise}
 */
export function getUserInfo(userId) {
  return request({
    url: `/api/user/${userId}`,
    method: 'get'
  })
}

/**
 * 分页获取用户列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getUsersByPage(params) {
  return request({
    url: '/api/user/page',
    method: 'get',
    params
  })
}

/**
 * 更新用户状态
 * @param {number} userId - 用户ID
 * @param {number} status - 状态
 * @returns {Promise}
 */
export function updateUserStatus(userId, status) {
  return request({
    url: `/api/user/status/${userId}`,
    method: 'put',
    data: { status }
  })
}

/**
 * 修改密码
 * @param {Object} data - 密码数据
 * @returns {Promise}
 */
export function changePassword(data) {
  return request({
    url: '/api/user/change-password',
    method: 'put',
    data
  })
}

/**
 * 设置支付密码
 * @param {Object} data - 支付密码数据
 * @returns {Promise}
 */
export function setPayPassword(data) {
  return request({
    url: '/api/user/set-pay-password',
    method: 'put',
    data
  })
}

/**
 * 设置支付限额
 * @param {number} userId - 用户ID
 * @param {Object} data - 限额数据
 * @returns {Promise}
 */
export function setPaymentLimits(userId, data) {
  return request({
    url: '/api/user/set-limits',
    method: 'put',
    data: { userId, ...data }
  })
}

/**
 * 启用/禁用指纹登录
 * @param {number} userId - 用户ID
 * @param {boolean} enabled - 是否启用
 * @returns {Promise}
 */
export function setFingerprintEnabled(userId, enabled) {
  return request({
    url: `/api/user/fingerprint/${userId}`,
    method: 'put',
    params: { enabled }
  })
}

// 用户API对象
export const userApi = {
  login,
  register,
  sendVerificationCode,
  loginWithSms,
  getUserInfo,
  updateUser,
  getUsersByPage,
  updateUserStatus,
  changePassword,
  setPayPassword,
  setPaymentLimits,
  setFingerprintEnabled
}
