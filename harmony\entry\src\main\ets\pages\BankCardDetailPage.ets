import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { BankCardApi } from '../api/BankCardApi';
import { BankCard } from '../common/types/index';
import { tempDataManager } from '../common/storage/TempDataManager';

@Entry
@Component
struct BankCardDetailPage {
  @State cardDetail: BankCard | null = null;
  @State isLoading: boolean = true;
  @State cardId: number = 0;

  aboutToAppear() {
    // 获取传入的银行卡ID
    const params = router.getParams() as Record<string, number>;
    this.cardId = params?.cardId || 0;
    
    if (this.cardId > 0) {
      this.loadCardDetail();
    } else {
      promptAction.showToast({ message: '银行卡信息错误' });
      router.back();
    }
  }

  async loadCardDetail() {
    try {
      this.isLoading = true;
      this.cardDetail = await BankCardApi.getCardDetail(this.cardId);
    } catch (error) {
      console.error('获取银行卡详情失败:', error);
      promptAction.showToast({ message: '获取银行卡详情失败' });
      router.back();
    } finally {
      this.isLoading = false;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Button('返回')
          .fontSize(16)
          .fontColor('#1976D2')
          .backgroundColor(Color.Transparent)
          .onClick(() => {
            router.back();
          })

        Text('银行卡详情')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('')
          .width(60) // 占位，保持标题居中
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')

      if (this.isLoading) {
        // 加载状态
        Column() {
          LoadingProgress()
            .width(40)
            .height(40)
            .color('#1976D2')

          Text('加载中...')
            .fontSize(14)
            .fontColor('#999999')
            .margin({ top: 16 })
        }
        .width('100%')
        .height('100%')
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .backgroundColor('#F5F5F5')
      } else if (this.cardDetail) {
        // 银行卡详情内容
        Scroll() {
          Column() {
            // 银行卡卡片
            this.BankCardDisplay()

            // 详细信息
            this.CardDetailInfo()

            // 操作按钮
            this.ActionButtons()
          }
          .padding({ left: 16, right: 16, bottom: 20 })
        }
        .layoutWeight(1)
        .backgroundColor('#F5F5F5')
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F5F5F5')
  }

  @Builder
  BankCardDisplay() {
    // 真实银行卡设计
    Stack({ alignContent: Alignment.TopStart }) {
      Column() {
        // 银行卡顶部信息
        Row() {
          Column() {
            Text(this.cardDetail?.bankName || '')
              .fontSize(18)
              .fontColor('#FFFFFF')
              .fontWeight(FontWeight.Bold)
              .maxLines(1)
              .textOverflow({ overflow: TextOverflow.Ellipsis })

            Text(this.cardDetail?.cardType || '')
              .fontSize(14)
              .fontColor('#E0E0E0')
              .margin({ top: 4 })
          }
          .alignItems(HorizontalAlign.Start)
          .layoutWeight(1)

          Column() {
            // 绑定状态标识
            Text(this.getCardStatus())
              .fontSize(12)
              .fontColor('#FFFFFF')
              .backgroundColor(this.cardDetail?.isBound === 1 ? '#4CAF50' : '#FF5722')
              .borderRadius(10)
              .padding({ left: 8, right: 8, top: 4, bottom: 4 })
              .margin({ bottom: 6 })

            // 默认卡标识
            if (this.cardDetail?.isDefault) {
              Text('默认')
                .fontSize(12)
                .fontColor('#FFFFFF')
                .backgroundColor('#FF9800')
                .borderRadius(10)
                .padding({ left: 8, right: 8, top: 4, bottom: 4 })
            }
          }
          .alignItems(HorizontalAlign.End)
        }
        .width('100%')
        .margin({ bottom: 24 })

        // 银行卡号 - 格式化显示
        Text(this.formatCardNumberWithSpaces())
          .fontSize(20)
          .fontColor('#FFFFFF')
          .fontWeight(FontWeight.Medium)
          .letterSpacing(3)
          .margin({ bottom: 20 })
          .alignSelf(ItemAlign.Start)

        // 持卡人信息
        Row() {
          Column() {
            Text('持卡人')
              .fontSize(12)
              .fontColor('#E0E0E0')
              .margin({ bottom: 4 })

            Text(this.cardDetail?.holderName || '')
              .fontSize(16)
              .fontColor('#FFFFFF')
              .fontWeight(FontWeight.Medium)
          }
          .alignItems(HorizontalAlign.Start)
          .layoutWeight(1)

          Column() {
            Text('有效期')
              .fontSize(12)
              .fontColor('#E0E0E0')
              .margin({ bottom: 4 })

            Text('**/**')
              .fontSize(16)
              .fontColor('#FFFFFF')
              .fontWeight(FontWeight.Medium)
          }
          .alignItems(HorizontalAlign.End)
        }
        .width('100%')
      }
      .width('100%')
      .height(200)
      .padding(24)
      .borderRadius(16)
      .linearGradient({
        direction: GradientDirection.Right,
        colors: this.getBankCardGradient(this.cardDetail?.bankName || '')
      })
      .shadow({
        radius: 12,
        color: 'rgba(0,0,0,0.2)',
        offsetX: 0,
        offsetY: 6
      })

      // 银行卡装饰图案
      Image($r('app.media.ic_bank_card'))
        .width(100)
        .height(100)
        .fillColor('rgba(255,255,255,0.1)')
        .position({ x: '65%', y: '10%' })
    }
    .width('100%')
    .margin({ top: 16, bottom: 24 })
  }

  @Builder
  CardDetailInfo() {
    Column() {
      Text('银行卡信息')
        .fontSize(16)
        .fontColor('#333333')
        .fontWeight(FontWeight.Medium)
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })

      this.DetailItem('银行名称', this.cardDetail?.bankName || '')
      this.DetailItem('卡片类型', this.cardDetail?.cardType || '')
      this.DetailItem('持卡人姓名', this.cardDetail?.holderName || '')
      this.DetailItem('卡号', this.maskCardNumber(this.cardDetail?.cardNo || ''))
      this.DetailItem('绑定状态', this.getCardStatus())
      this.DetailItem('绑定时间', this.formatDateTime(this.cardDetail?.createTime || ''))
    }
    .width('100%')
    .padding(20)
    .borderRadius(12)
    .backgroundColor('#FFFFFF')
    .margin({ bottom: 16 })
  }

  @Builder
  DetailItem(label: string, value: string) {
    Row() {
      Text(label)
        .fontSize(14)
        .fontColor('#666666')
        .width(80)

      Text(value)
        .fontSize(14)
        .fontColor('#333333')
        .layoutWeight(1)
        .textAlign(TextAlign.End)
    }
    .width('100%')
    .height(44)
    .justifyContent(FlexAlign.SpaceBetween)
    .alignItems(VerticalAlign.Center)
  }

  @Builder
  ActionButtons() {
    Column() {
      // 快捷操作按钮
      Text('快捷操作')
        .fontSize(16)
        .fontColor('#333333')
        .fontWeight(FontWeight.Medium)
        .alignSelf(ItemAlign.Start)
        .margin({ bottom: 16 })

      Row() {
        Button('支付')
          .fontSize(16)
          .fontColor('#FFFFFF')
          .backgroundColor('#4CAF50')
          .borderRadius(8)
          .layoutWeight(1)
          .height(48)
          .margin({ right: 8 })
          .onClick(() => {
            this.useCardForPayment();
          })

        Button('转账')
          .fontSize(16)
          .fontColor('#FFFFFF')
          .backgroundColor('#2196F3')
          .borderRadius(8)
          .layoutWeight(1)
          .height(48)
          .margin({ left: 8 })
          .onClick(() => {
            this.useCardForTransfer();
          })
      }
      .width('100%')
      .margin({ bottom: 16 })

      Button('解绑银行卡')
        .width('100%')
        .height(48)
        .fontSize(16)
        .fontColor('#F44336')
        .backgroundColor('#FFFFFF')
        .borderRadius(8)
        .border({ width: 1, color: '#F44336' })
        .onClick(() => {
          this.confirmUnbindCard();
        })

      Text('解绑后将无法使用此银行卡进行充值和提现')
        .fontSize(12)
        .fontColor('#999999')
        .textAlign(TextAlign.Center)
        .width('100%')
        .margin({ top: 8 })
    }
    .width('100%')
    .padding(20)
    .borderRadius(12)
    .backgroundColor('#FFFFFF')
  }

  // 工具方法
  getCardStatus(): string {
    return this.cardDetail?.isBound === 1 ? '已绑定' : '未绑定';
  }

  getCardStatusColor(): string {
    return this.cardDetail?.isBound === 1 ? '#4CAF50' : '#F44336';
  }

  getCardStatusBgColor(): string {
    return this.cardDetail?.isBound === 1 ? '#E8F5E8' : '#FFEBEE';
  }

  maskCardNumber(cardNo: string): string {
    if (!cardNo || cardNo.length < 8) {
      return cardNo;
    }
    
    // 显示前4位和后4位
    const start = cardNo.substring(0, 4);
    const end = cardNo.substring(cardNo.length - 4);
    const middle = '*'.repeat(cardNo.length - 8);
    
    return `${start} ${middle} ${end}`;
  }

  formatDateTime(dateTime: string): string {
    if (!dateTime) return '';

    const date = new Date(dateTime);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  }

  /**
   * 格式化银行卡号显示（带空格分隔）
   */
  formatCardNumberWithSpaces(): string {
    if (!this.cardDetail?.cardNo) return '';

    // 脱敏处理
    const maskedCardNo = this.maskCardNumber(this.cardDetail.cardNo);

    // 添加空格分隔，每4位一组
    return maskedCardNo.replace(/(.{4})/g, '$1 ').trim();
  }

  /**
   * 获取银行卡渐变色
   */
  getBankCardGradient(bankName: string): Array<[string, number]> {
    const gradients: Record<string, Array<[string, number]>> = {
      '中国工商银行': [['#C41E3A', 0], ['#8B0000', 1]],
      '中国建设银行': [['#003DA5', 0], ['#001F5C', 1]],
      '中国农业银行': [['#00A651', 0], ['#006B35', 1]],
      '中国银行': [['#B8860B', 0], ['#8B6914', 1]],
      '招商银行': [['#DC143C', 0], ['#8B0000', 1]],
      '交通银行': [['#0066CC', 0], ['#003D7A', 1]],
      '中信银行': [['#FF6B35', 0], ['#CC4125', 1]],
      '光大银行': [['#9932CC', 0], ['#6A1B9A', 1]],
      '华夏银行': [['#FF1744', 0], ['#C62828', 1]],
      '民生银行': [['#00BCD4', 0], ['#0097A7', 1]],
      '广发银行': [['#FF5722', 0], ['#D84315', 1]],
      '平安银行': [['#FF9800', 0], ['#F57C00', 1]]
    };

    return gradients[bankName] || [['#1976D2', 0], ['#0D47A1', 1]];
  }

  async confirmUnbindCard() {
    try {
      const result = await promptAction.showDialog({
        title: '确认解绑',
        message: `确定要解绑银行卡 ${this.cardDetail?.bankName}(${this.cardDetail?.cardNo.slice(-4)}) 吗？`,
        buttons: [
          { text: '确定', color: '#F44336' },
          { text: '取消', color: '#666666' }
        ]
      });

      if (result.index === 0) {
        await this.unbindCard();
      }
    } catch (error) {
      console.error('显示确认对话框失败:', error);
    }
  }

  async unbindCard() {
    try {
      await BankCardApi.unbindCard(this.cardId);
      promptAction.showToast({ message: '解绑成功' });

      // 通知银行卡列表页面刷新
      console.log('BankCardDetailPage - 设置银行卡解绑事件标志');
      tempDataManager.setData('BANK_CARD_UNBOUND', true);

      // 返回银行卡列表页
      router.back();
    } catch (error) {
      console.error('解绑银行卡失败:', error);
      promptAction.showToast({ message: '解绑失败，请重试' });
    }
  }

  /**
   * 使用银行卡进行支付
   */
  useCardForPayment() {
    if (!this.cardDetail) return;

    console.log('使用银行卡支付:', this.cardDetail.bankName, this.cardDetail.cardNo);

    // 跳转到支付页面，传递银行卡信息
    router.pushUrl({
      url: 'pages/PaymentPage',
      params: {
        selectedCard: {
          cardId: this.cardDetail.cardId,
          bankName: this.cardDetail.bankName,
          cardNo: this.cardDetail.cardNo,
          cardType: this.cardDetail.cardType,
          holderName: this.cardDetail.holderName,
          maskedCardNo: this.maskCardNumber(this.cardDetail.cardNo)
        },
        paymentMethod: 'bankCard'
      }
    }).catch((error: Error) => {
      console.error('跳转支付页面失败:', error);
      promptAction.showToast({ message: '打开支付页面失败' });
    });
  }

  /**
   * 使用银行卡进行转账
   */
  useCardForTransfer() {
    if (!this.cardDetail) return;

    console.log('使用银行卡转账:', this.cardDetail.bankName, this.cardDetail.cardNo);

    // 跳转到钱包操作页面（转账功能），传递银行卡信息
    router.pushUrl({
      url: 'pages/WalletOperationPage',
      params: {
        operationType: 'transfer',
        selectedCard: {
          cardId: this.cardDetail.cardId,
          bankName: this.cardDetail.bankName,
          cardNo: this.cardDetail.cardNo,
          cardType: this.cardDetail.cardType,
          holderName: this.cardDetail.holderName,
          maskedCardNo: this.maskCardNumber(this.cardDetail.cardNo)
        },
        transferMethod: 'bankCard'
      }
    }).catch((error: Error) => {
      console.error('跳转钱包操作页面失败:', error);
      promptAction.showToast({ message: '打开钱包操作页面失败' });
    });
  }
}
