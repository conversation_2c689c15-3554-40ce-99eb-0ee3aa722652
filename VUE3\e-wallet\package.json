{"name": "e-wallet", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "echarts": "^5.4.3", "element-plus": "^2.10.2", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^9.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.19.2", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-eslint-parser": "^9.4.0"}}