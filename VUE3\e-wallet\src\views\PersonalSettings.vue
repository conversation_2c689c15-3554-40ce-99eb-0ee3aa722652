<template>
  <div class="settings-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>个人设置</h2>
      <p>管理您的账户安全和偏好设置</p>
    </div>

    <!-- 用户信息卡片 -->
    <el-card class="user-info-card" shadow="hover">
      <div class="user-info">
        <div class="user-avatar">
          <img :src="userInfo.avatar || defaultAvatar" alt="用户头像" />
          <div class="avatar-upload" @click="showAvatarUpload = true">
            <el-icon><Camera /></el-icon>
          </div>
        </div>
        <div class="user-details">
          <h3>{{ userInfo.username || '未设置用户名' }}</h3>
          <p>手机号：{{ formatPhone(userInfo.phone) }}</p>
          <p>用户ID：{{ userInfo.userId }}</p>
          <p>注册时间：{{ formatDate(userInfo.createdAt) }}</p>
          <el-tag :type="userInfo.status === 1 ? 'success' : 'danger'" size="small">
            {{ userInfo.status === 1 ? '正常' : '冻结' }}
          </el-tag>
        </div>
        <div class="user-actions">
          <el-button size="small" @click="showEditProfileDialog = true">编辑资料</el-button>
          <el-button size="small" type="warning" @click="showAccountStatusDialog = true">账户管理</el-button>
        </div>
      </div>
    </el-card>

    <!-- 设置选项 -->
    <el-row :gutter="20" class="settings-grid">
      <!-- 支付设置 -->
      <el-col :span="12">
        <el-card class="setting-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span class="card-title">💳 支付设置</span>
            </div>
          </template>

          <div class="setting-items">
            <div class="setting-item" @click="showPaymentPasswordDialog = true">
              <div class="setting-info">
                <h4>支付密码</h4>
                <p>{{ userInfo.payPassword ? '已设置支付密码' : '未设置支付密码' }}</p>
              </div>
              <el-icon class="setting-arrow"><ArrowRight /></el-icon>
            </div>

            <div class="setting-item" @click="showPaymentLimitDialog = true">
              <div class="setting-info">
                <h4>支付限额</h4>
                <p>日限额：¥{{ accountInfo.dailyLimit?.toLocaleString() || '0' }}</p>
                <p>单笔限额：¥{{ accountInfo.singleLimit?.toLocaleString() || '0' }}</p>
              </div>
              <el-icon class="setting-arrow"><ArrowRight /></el-icon>
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <h4>指纹支付</h4>
                <p>使用指纹进行支付验证</p>
              </div>
              <el-switch v-model="settings.fingerprintPayment" @change="updateSetting('fingerprintPayment')" />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <h4>免密支付</h4>
                <p>小额支付免输密码（≤¥200）</p>
              </div>
              <el-switch v-model="settings.passwordFreePayment" @change="updateSetting('passwordFreePayment')" />
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 安全设置 -->
      <el-col :span="12">
        <el-card class="setting-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span class="card-title">🔒 安全设置</span>
            </div>
          </template>

          <div class="setting-items">
            <div class="setting-item" @click="showChangePasswordDialog = true">
              <div class="setting-info">
                <h4>登录密码</h4>
                <p>定期更新安全密码</p>
              </div>
              <el-icon class="setting-arrow"><ArrowRight /></el-icon>
            </div>

            <div class="setting-item" @click="showSecurityDevicesDialog = true">
              <div class="setting-info">
                <h4>安全设备管理</h4>
                <p>管理登录设备记录与授权</p>
              </div>
              <el-icon class="setting-arrow"><ArrowRight /></el-icon>
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <h4>短信验证</h4>
                <p>重要操作短信验证</p>
              </div>
              <el-switch v-model="settings.smsVerification" @change="updateSetting('smsVerification')" />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <h4>风险提醒</h4>
                <p>异常交易实时通知</p>
              </div>
              <el-switch v-model="settings.riskAlert" @change="updateSetting('riskAlert')" />
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 通知设置 -->
      <el-col :span="12">
        <el-card class="setting-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span class="card-title">🔔 通知设置</span>
            </div>
          </template>

          <div class="setting-items">
            <div class="setting-item">
              <div class="setting-info">
                <h4>交易通知</h4>
                <p>收支变动实时通知</p>
              </div>
              <el-switch v-model="settings.transactionNotification" @change="updateSetting('transactionNotification')" />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <h4>营销推送</h4>
                <p>接收优惠活动推送</p>
              </div>
              <el-switch v-model="settings.marketingNotification" @change="updateSetting('marketingNotification')" />
            </div>

            <div class="setting-item">
              <div class="setting-info">
                <h4>系统通知</h4>
                <p>系统维护和更新通知</p>
              </div>
              <el-switch v-model="settings.systemNotification" @change="updateSetting('systemNotification')" />
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 账户管理 -->
      <el-col :span="12">
        <el-card class="setting-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span class="card-title">👤 账户管理</span>
            </div>
          </template>

          <div class="setting-items">
            <div class="setting-item" @click="showAccountStatusDialog = true">
              <div class="setting-info">
                <h4>账户状态</h4>
                <p>{{ userInfo.status === 1 ? '正常使用' : '账户已冻结' }}</p>
              </div>
              <el-icon class="setting-arrow"><ArrowRight /></el-icon>
            </div>

            <div class="setting-item" @click="showUserSwitchDialog = true">
              <div class="setting-info">
                <h4>用户切换</h4>
                <p>切换到其他账户</p>
              </div>
              <el-icon class="setting-arrow"><ArrowRight /></el-icon>
            </div>

            <div class="setting-item" @click="showLogoutDialog = true">
              <div class="setting-info">
                <h4>退出登录</h4>
                <p>安全退出当前账户</p>
              </div>
              <el-icon class="setting-arrow"><ArrowRight /></el-icon>
            </div>

            <div class="setting-item" @click="showDeleteAccountDialog = true">
              <div class="setting-info">
                <h4>注销账户</h4>
                <p style="color: #f56c6c;">永久删除账户数据</p>
              </div>
              <el-icon class="setting-arrow"><ArrowRight /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 其他设置 -->
      <el-col :span="12">
        <el-card class="setting-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span class="card-title">⚙️ 其他设置</span>
            </div>
          </template>

          <div class="setting-items">
            <div class="setting-item">
              <div class="setting-info">
                <h4>深色模式</h4>
                <p>切换应用主题</p>
              </div>
              <el-switch v-model="settings.darkMode" @change="updateSetting('darkMode')" />
            </div>

            <div class="setting-item" @click="clearCache">
              <div class="setting-info">
                <h4>清除缓存</h4>
                <p>清理应用缓存数据</p>
              </div>
              <el-icon class="setting-arrow"><ArrowRight /></el-icon>
            </div>

            <div class="setting-item" @click="showNetworkSettingsDialog = true">
              <div class="setting-info">
                <h4>网络设置</h4>
                <p>配置网络连接参数</p>
              </div>
              <el-icon class="setting-arrow"><ArrowRight /></el-icon>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 编辑资料对话框 -->
    <el-dialog v-model="showEditProfileDialog" title="编辑个人资料" width="500px">
      <el-form :model="editForm" :rules="editFormRules" ref="editFormRef" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone" placeholder="请输入手机号" disabled>
            <template #suffix>
              <el-button link type="primary" @click="showChangePhoneDialog = true">修改</el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="头像">
          <div class="avatar-upload-area">
            <img :src="editForm.avatar || defaultAvatar" alt="头像预览" class="avatar-preview" />
            <el-button size="small" @click="showAvatarUpload = true">更换头像</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditProfileDialog = false">取消</el-button>
        <el-button type="primary" @click="updateProfile" :loading="updating">保存</el-button>
      </template>
    </el-dialog>

    <!-- 头像上传对话框 -->
    <el-dialog v-model="showAvatarUpload" title="上传头像" width="400px">
      <div class="avatar-upload-container">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          :before-upload="beforeAvatarUpload"
          :http-request="handleAvatarUpload"
        >
          <img v-if="avatarUrl" :src="avatarUrl" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
        <div class="upload-tips">
          <p>支持 JPG、PNG 格式，文件大小不超过 2MB</p>
        </div>
      </div>
      <template #footer>
        <el-button @click="showAvatarUpload = false">取消</el-button>
        <el-button type="primary" @click="confirmAvatarUpload" :disabled="!avatarUrl">确认</el-button>
      </template>
    </el-dialog>

    <!-- 支付密码对话框 -->
    <el-dialog v-model="showPaymentPasswordDialog" title="设置支付密码" width="500px">
      <el-form :model="passwordForm" :rules="passwordFormRules" ref="passwordFormRef" label-width="120px">
        <el-form-item label="登录密码" prop="currentPassword">
          <el-input v-model="passwordForm.currentPassword" type="password" placeholder="请输入登录密码进行验证" show-password></el-input>
        </el-form-item>
        <el-form-item label="支付密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" placeholder="请输入6位数字支付密码" show-password maxlength="6"></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" placeholder="请确认支付密码" show-password maxlength="6"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showPaymentPasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="updatePaymentPassword" :loading="updating">确认</el-button>
      </template>
    </el-dialog>

    <!-- 支付限额对话框 -->
    <el-dialog v-model="showPaymentLimitDialog" title="设置支付限额" width="500px">
      <el-form :model="limitForm" :rules="limitFormRules" ref="limitFormRef" label-width="120px">
        <el-form-item label="日限额" prop="dailyLimit">
          <el-input v-model="limitForm.dailyLimit" placeholder="请输入日限额" type="number">
            <template #prepend>¥</template>
          </el-input>
          <div class="form-tip">当前日限额：¥{{ accountInfo.dailyLimit?.toLocaleString() || '0' }}</div>
        </el-form-item>
        <el-form-item label="月限额" prop="monthlyLimit">
          <el-input v-model="limitForm.monthlyLimit" placeholder="请输入月限额" type="number">
            <template #prepend>¥</template>
          </el-input>
          <div class="form-tip">当前月限额：¥{{ accountInfo.monthlyLimit?.toLocaleString() || '0' }}</div>
        </el-form-item>
        <el-form-item label="单笔限额" prop="singleLimit">
          <el-input v-model="limitForm.singleLimit" placeholder="请输入单笔限额" type="number">
            <template #prepend>¥</template>
          </el-input>
          <div class="form-tip">当前单笔限额：¥{{ accountInfo.singleLimit?.toLocaleString() || '0' }}</div>
        </el-form-item>
        <el-form-item label="支付密码" prop="payPassword">
          <el-input v-model="limitForm.payPassword" type="password" placeholder="请输入支付密码确认" show-password></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showPaymentLimitDialog = false">取消</el-button>
        <el-button type="primary" @click="updatePaymentLimit" :loading="updating">确认</el-button>
      </template>
    </el-dialog>

    <!-- 修改登录密码对话框 -->
    <el-dialog v-model="showChangePasswordDialog" title="修改登录密码" width="400px">
      <el-form :model="loginPasswordForm" label-width="100px">
        <el-form-item label="当前密码">
          <el-input v-model="loginPasswordForm.currentPassword" type="password" placeholder="请输入当前登录密码"></el-input>
        </el-form-item>
        <el-form-item label="新密码">
          <el-input v-model="loginPasswordForm.newPassword" type="password" placeholder="请输入新登录密码"></el-input>
        </el-form-item>
        <el-form-item label="确认密码">
          <el-input v-model="loginPasswordForm.confirmPassword" type="password" placeholder="请确认新登录密码"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showChangePasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="updateLoginPassword">确认</el-button>
      </template>
    </el-dialog>

    <!-- 账户状态管理对话框 -->
    <el-dialog v-model="showAccountStatusDialog" title="账户状态管理" width="600px">
      <div class="account-status-info">
        <el-descriptions title="账户信息" :column="2" border>
          <el-descriptions-item label="用户ID">{{ userInfo.userId }}</el-descriptions-item>
          <el-descriptions-item label="账户状态">
            <el-tag :type="userInfo.status === 1 ? 'success' : 'danger'">
              {{ userInfo.status === 1 ? '正常' : '冻结' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">{{ formatDate(userInfo.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="最后登录">{{ formatDate(userInfo.lastLogin) }}</el-descriptions-item>
        </el-descriptions>

        <div class="account-actions" style="margin-top: 20px;">
          <el-alert
            v-if="userInfo.status !== 1"
            title="账户已冻结"
            type="warning"
            description="您的账户已被冻结，如有疑问请联系客服"
            show-icon
            :closable="false"
          />
          <div style="margin-top: 15px;">
            <el-button v-if="userInfo.status === 1" type="warning" @click="freezeAccount">冻结账户</el-button>
            <el-button v-else type="success" @click="unfreezeAccount">解冻账户</el-button>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="showAccountStatusDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 安全设备管理对话框 -->
    <el-dialog v-model="showSecurityDevicesDialog" title="安全设备管理" width="700px">
      <div class="security-devices">
        <el-table :data="loginDevices" style="width: 100%">
          <el-table-column prop="deviceName" label="设备名称" width="200" />
          <el-table-column prop="deviceType" label="设备类型" width="120" />
          <el-table-column prop="location" label="登录地点" width="150" />
          <el-table-column prop="loginTime" label="登录时间" width="180" />
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.isCurrent ? 'success' : 'info'">
                {{ scope.row.isCurrent ? '当前设备' : '历史登录' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button v-if="!scope.row.isCurrent" size="small" type="danger" @click="removeDevice(scope.row)">
                移除授权
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <el-button @click="showSecurityDevicesDialog = false">关闭</el-button>
        <el-button type="primary" @click="clearAllDevices">清除所有设备</el-button>
      </template>
    </el-dialog>

    <!-- 用户切换对话框 -->
    <el-dialog v-model="showUserSwitchDialog" title="用户切换" width="400px">
      <div class="user-switch">
        <p>请输入要切换的用户手机号：</p>
        <el-input v-model="switchUserPhone" placeholder="请输入手机号" />
        <el-alert
          title="注意"
          type="info"
          description="切换用户需要重新登录验证"
          show-icon
          :closable="false"
          style="margin-top: 15px;"
        />
      </div>
      <template #footer>
        <el-button @click="showUserSwitchDialog = false">取消</el-button>
        <el-button type="primary" @click="switchUser">切换用户</el-button>
      </template>
    </el-dialog>

    <!-- 网络设置对话框 -->
    <el-dialog v-model="showNetworkSettingsDialog" title="网络设置" width="500px">
      <el-form :model="networkForm" label-width="120px">
        <el-form-item label="服务器地址">
          <el-input v-model="networkForm.serverUrl" placeholder="请输入服务器地址" />
        </el-form-item>
        <el-form-item label="连接超时">
          <el-input v-model="networkForm.timeout" placeholder="请输入超时时间(秒)" type="number" />
        </el-form-item>
        <el-form-item label="自动重连">
          <el-switch v-model="networkForm.autoReconnect" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showNetworkSettingsDialog = false">取消</el-button>
        <el-button type="primary" @click="updateNetworkSettings">保存</el-button>
      </template>
    </el-dialog>

    <!-- 注销账户确认对话框 -->
    <el-dialog v-model="showDeleteAccountDialog" title="注销账户" width="500px">
      <div class="delete-account-warning">
        <el-alert
          title="危险操作"
          type="error"
          description="注销账户将永久删除您的所有数据，包括钱包余额、交易记录等，此操作不可恢复！"
          show-icon
          :closable="false"
        />
        <div style="margin-top: 20px;">
          <p>请输入您的支付密码确认注销：</p>
          <el-input v-model="deleteAccountPassword" type="password" placeholder="请输入支付密码" show-password />
        </div>
      </div>
      <template #footer>
        <el-button @click="showDeleteAccountDialog = false">取消</el-button>
        <el-button type="danger" @click="deleteAccount" :loading="deleting">确认注销</el-button>
      </template>
    </el-dialog>

    <!-- 退出登录确认对话框 -->
    <el-dialog v-model="showLogoutDialog" title="退出登录" width="300px">
      <p>确定要退出当前账户吗？</p>
      <template #footer>
        <el-button @click="showLogoutDialog = false">取消</el-button>
        <el-button type="primary" @click="handleLogout">确认退出</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ArrowRight, Camera, Plus } from '@element-plus/icons-vue';
import { getUserInfo, updateUser, changePassword, setPaymentLimits, sendVerificationCode as sendSmsCode } from '@/api/user';
import { payPasswordApi } from '@/api/payPassword';
import { getUserInfo as getCurrentUser } from '@/stores/user';

const router = useRouter();

// 用户信息
const userInfo = ref({
  userId: null,
  username: '',
  phone: '',
  password: '',
  payPassword: '',
  status: 1,
  lastLogin: null,
  createdAt: null,
  avatar: ''
});

// 账户信息
const accountInfo = ref({
  accountId: null,
  balance: 0,
  dailyLimit: 50000,
  singleLimit: 5000,
  monthlyLimit: 200000,
  status: 1
});

// 默认头像
const defaultAvatar = 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png';

// 对话框显示状态
const showEditProfileDialog = ref(false);
const showPaymentPasswordDialog = ref(false);
const showPaymentLimitDialog = ref(false);
const showChangePasswordDialog = ref(false);
const showLogoutDialog = ref(false);
const showAccountStatusDialog = ref(false);
const showSecurityDevicesDialog = ref(false);
const showUserSwitchDialog = ref(false);
const showNetworkSettingsDialog = ref(false);
const showDeleteAccountDialog = ref(false);
const showAvatarUpload = ref(false);
const showChangePhoneDialog = ref(false);

// 加载状态
const updating = ref(false);
const deleting = ref(false);
const sendingCode = ref(false);
const countdown = ref(0);

// 表单引用
const editFormRef = ref(null);
const passwordFormRef = ref(null);
const limitFormRef = ref(null);

// 设置项
const settings = ref({
  passwordFreePayment: true,
  fingerprintPayment: false,
  smsVerification: true,
  transactionNotification: true,
  marketingNotification: false,
  systemNotification: true,
  darkMode: false,
  riskAlert: true
});

// 表单数据
const editForm = ref({
  username: '',
  phone: '',
  avatar: ''
});

const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

const limitForm = ref({
  dailyLimit: '',
  monthlyLimit: '',
  singleLimit: '',
  payPassword: ''
});

const loginPasswordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 新增表单
const switchUserPhone = ref('');
const deleteAccountPassword = ref('');
const avatarUrl = ref('');

const networkForm = ref({
  serverUrl: 'http://localhost:8096',
  timeout: 30,
  autoReconnect: true
});

// 登录设备列表
const loginDevices = ref([
  {
    id: 1,
    deviceName: 'iPhone 13',
    deviceType: 'iOS',
    location: '北京市',
    loginTime: '2024-01-15 10:30:00',
    isCurrent: true
  },
  {
    id: 2,
    deviceName: 'Chrome浏览器',
    deviceType: 'Web',
    location: '上海市',
    loginTime: '2024-01-14 15:20:00',
    isCurrent: false
  }
]);

// 表单验证规则
const editFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ]
};

const passwordFormRules = {
  currentPassword: [
    { required: true, message: '请输入登录密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新支付密码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '支付密码必须是6位数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认支付密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
};

const limitFormRules = {
  dailyLimit: [
    { required: true, message: '请输入日限额', trigger: 'blur' },
    { type: 'number', min: 1, message: '日限额必须大于0', trigger: 'blur', transform: value => Number(value) }
  ],
  monthlyLimit: [
    { required: true, message: '请输入月限额', trigger: 'blur' },
    { type: 'number', min: 1, message: '月限额必须大于0', trigger: 'blur', transform: value => Number(value) }
  ],
  singleLimit: [
    { required: true, message: '请输入单笔限额', trigger: 'blur' },
    { type: 'number', min: 1, message: '单笔限额必须大于0', trigger: 'blur', transform: value => Number(value) }
  ],
  payPassword: [
    { required: true, message: '请输入支付密码', trigger: 'blur' }
  ]
};

// 工具方法
const formatPhone = (phone) => {
  if (!phone) return '';
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

const formatDate = (dateStr) => {
  if (!dateStr) return '未知';
  return new Date(dateStr).toLocaleString('zh-CN');
};

// 更新设置
const updateSetting = (key) => {
  ElMessage.success(`${getSettingName(key)}设置已更新`);
  // 这里可以添加实际的设置更新逻辑
};

const getSettingName = (key) => {
  const names = {
    passwordFreePayment: '免密支付',
    fingerprintPayment: '指纹支付',
    smsVerification: '短信验证',
    transactionNotification: '交易通知',
    marketingNotification: '营销推送',
    systemNotification: '系统通知',
    darkMode: '深色模式',
    riskAlert: '风险提醒'
  };
  return names[key] || '设置';
};

// 初始化数据
const initData = async () => {
  try {
    const currentUser = getCurrentUser();
    if (currentUser) {
      userInfo.value = { ...currentUser };
      editForm.value = {
        username: currentUser.username || '',
        phone: currentUser.phone || '',
        avatar: currentUser.avatar || ''
      };
    }

    // 获取账户信息
    // const accountResponse = await getAccountInfo(userInfo.value.userId);
    // if (accountResponse.code === 0) {
    //   accountInfo.value = accountResponse.data;
    // }
  } catch (error) {
    console.error('初始化数据失败:', error);
  }
};

// 发送验证码
const sendVerificationCode = async () => {
  if (!userInfo.value.phone) {
    ElMessage.error('手机号不能为空');
    return;
  }

  try {
    sendingCode.value = true;
    const response = await sendSmsCode(userInfo.value.phone, 2); // 2表示操作验证码

    if (response.code === 0) {
      ElMessage.success('验证码发送成功');
      startCountdown();
    } else {
      ElMessage.error(response.msg || '验证码发送失败');
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    ElMessage.error('验证码发送失败，请重试');
  } finally {
    sendingCode.value = false;
  }
};

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60;
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};

// 更新个人资料
const updateProfile = async () => {
  try {
    updating.value = true;
    const response = await updateUser({
      userId: userInfo.value.userId,
      username: editForm.value.username
    });

    if (response.code === 0) {
      userInfo.value.username = editForm.value.username;
      showEditProfileDialog.value = false;
      ElMessage.success('个人资料更新成功！');
    } else {
      ElMessage.error(response.msg || '更新失败');
    }
  } catch (error) {
    console.error('更新个人资料失败:', error);
    ElMessage.error('更新失败，请重试');
  } finally {
    updating.value = false;
  }
};

// 更新支付密码
const updatePaymentPassword = async () => {
  try {
    await passwordFormRef.value.validate();
    updating.value = true;

    const response = await payPasswordApi.setDefaultCardPayPassword({
      userId: userInfo.value.userId,
      payPassword: passwordForm.value.newPassword,
      loginPassword: passwordForm.value.currentPassword
    });

    if (response.code === 0) {
      userInfo.value.payPassword = passwordForm.value.newPassword;
      showPaymentPasswordDialog.value = false;
      ElMessage.success('支付密码设置成功！');

      // 重置表单
      passwordForm.value = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
    } else {
      ElMessage.error(response.msg || '设置失败');
    }
  } catch (error) {
    console.error('设置支付密码失败:', error);
    if (error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error('设置失败，请重试');
    }
  } finally {
    updating.value = false;
  }
};

// 更新支付限额
const updatePaymentLimit = async () => {
  try {
    await limitFormRef.value.validate();
    updating.value = true;

    const dailyLimit = parseFloat(limitForm.value.dailyLimit);
    const monthlyLimit = parseFloat(limitForm.value.monthlyLimit);
    const singleLimit = parseFloat(limitForm.value.singleLimit);

    if (singleLimit > dailyLimit) {
      ElMessage.error('单笔限额不能大于日限额');
      return;
    }

    if (dailyLimit > monthlyLimit) {
      ElMessage.error('日限额不能大于月限额');
      return;
    }

    const response = await setPaymentLimits(userInfo.value.userId, {
      dailyLimit,
      monthlyLimit,
      singleLimit,
      payPassword: limitForm.value.payPassword
    });

    if (response.code === 0) {
      accountInfo.value.dailyLimit = dailyLimit;
      accountInfo.value.monthlyLimit = monthlyLimit;
      accountInfo.value.singleLimit = singleLimit;

      showPaymentLimitDialog.value = false;
      ElMessage.success('支付限额设置成功！');

      // 重置表单
      limitForm.value = {
        dailyLimit: '',
        monthlyLimit: '',
        singleLimit: '',
        payPassword: ''
      };
    } else {
      ElMessage.error(response.msg || '设置失败');
    }
  } catch (error) {
    console.error('设置支付限额失败:', error);
    if (error.message) {
      ElMessage.error(error.message);
    } else {
      ElMessage.error('设置失败，请重试');
    }
  } finally {
    updating.value = false;
  }
};

// 更新登录密码
const updateLoginPassword = async () => {
  if (!loginPasswordForm.value.currentPassword || !loginPasswordForm.value.newPassword || !loginPasswordForm.value.confirmPassword) {
    ElMessage.error('请填写完整信息');
    return;
  }

  if (loginPasswordForm.value.newPassword !== loginPasswordForm.value.confirmPassword) {
    ElMessage.error('两次输入的密码不一致');
    return;
  }

  if (loginPasswordForm.value.newPassword.length < 6) {
    ElMessage.error('密码长度不能少于6位');
    return;
  }

  try {
    updating.value = true;

    const response = await changePassword({
      userId: userInfo.value.userId,
      oldPassword: loginPasswordForm.value.currentPassword,
      newPassword: loginPasswordForm.value.newPassword
    });

    if (response.code === 0) {
      showChangePasswordDialog.value = false;
      ElMessage.success('登录密码修改成功！');

      // 重置表单
      loginPasswordForm.value = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
    } else {
      ElMessage.error(response.msg || '密码修改失败');
    }
  } catch (error) {
    console.error('修改登录密码失败:', error);
    ElMessage.error('密码修改失败，请重试');
  } finally {
    updating.value = false;
  }
};



// 头像上传相关方法
const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPG) {
    ElMessage.error('上传头像图片只能是 JPG/PNG 格式!');
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!');
  }
  return isJPG && isLt2M;
};

const handleAvatarUpload = (options) => {
  const { file } = options;
  const reader = new FileReader();
  reader.onload = (e) => {
    avatarUrl.value = e.target.result;
  };
  reader.readAsDataURL(file);
};

const confirmAvatarUpload = () => {
  editForm.value.avatar = avatarUrl.value;
  userInfo.value.avatar = avatarUrl.value;
  showAvatarUpload.value = false;
  ElMessage.success('头像更新成功！');
};

// 账户状态管理
const freezeAccount = async () => {
  try {
    ElMessageBox.confirm('确定要冻结账户吗？冻结后将无法进行任何操作。', '确认冻结', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      // 调用冻结账户API
      ElMessage.success('账户已冻结');
      userInfo.value.status = 0;
    });
  } catch (error) {
    console.error('冻结账户失败:', error);
  }
};

const unfreezeAccount = async () => {
  try {
    ElMessageBox.confirm('确定要解冻账户吗？', '确认解冻', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success'
    }).then(async () => {
      // 调用解冻账户API
      ElMessage.success('账户已解冻');
      userInfo.value.status = 1;
    });
  } catch (error) {
    console.error('解冻账户失败:', error);
  }
};

// 安全设备管理
const removeDevice = (device) => {
  ElMessageBox.confirm(`确定要移除设备"${device.deviceName}"的授权吗？`, '确认移除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = loginDevices.value.findIndex(d => d.id === device.id);
    if (index > -1) {
      loginDevices.value.splice(index, 1);
      ElMessage.success('设备授权已移除');
    }
  });
};

const clearAllDevices = () => {
  ElMessageBox.confirm('确定要清除所有设备授权吗？这将强制所有设备重新登录。', '确认清除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    loginDevices.value = loginDevices.value.filter(d => d.isCurrent);
    ElMessage.success('已清除所有设备授权');
  });
};

// 用户切换
const switchUser = () => {
  if (!switchUserPhone.value) {
    ElMessage.error('请输入手机号');
    return;
  }

  if (!/^1[3-9]\d{9}$/.test(switchUserPhone.value)) {
    ElMessage.error('请输入正确的手机号格式');
    return;
  }

  showUserSwitchDialog.value = false;
  localStorage.removeItem('token');
  localStorage.removeItem('userInfo');
  router.push(`/login?phone=${switchUserPhone.value}`);
};

// 网络设置
const updateNetworkSettings = () => {
  ElMessage.success('网络设置已保存');
  showNetworkSettingsDialog.value = false;
};

// 注销账户
const deleteAccount = async () => {
  if (!deleteAccountPassword.value) {
    ElMessage.error('请输入支付密码');
    return;
  }

  try {
    deleting.value = true;

    ElMessageBox.confirm('最后确认：注销账户将永久删除所有数据，此操作不可恢复！', '最终确认', {
      confirmButtonText: '确认注销',
      cancelButtonText: '取消',
      type: 'error'
    }).then(async () => {
      // 调用注销账户API
      ElMessage.success('账户已注销');
      localStorage.clear();
      router.push('/login');
    });
  } catch (error) {
    console.error('注销账户失败:', error);
    ElMessage.error('注销失败，请重试');
  } finally {
    deleting.value = false;
    showDeleteAccountDialog.value = false;
  }
};

// 清除缓存
const clearCache = () => {
  ElMessageBox.confirm('确定要清除应用缓存吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    localStorage.removeItem('settings');
    sessionStorage.clear();
    ElMessage.success('缓存清除成功！');
  }).catch(() => {
    // 用户取消了操作
  });
};

// 退出登录
const handleLogout = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('userInfo');
  showLogoutDialog.value = false;
  ElMessage.success('已安全退出');
  router.push('/login');
};

// 组件挂载时初始化数据
onMounted(() => {
  initData();
});
</script>

<style scoped>
.settings-container {
  padding: 20px;
}

/* 页面头部 */
.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-header h2 {
  font-size: 28px;
  color: #2c3e50;
  margin: 0 0 10px 0;
}

.page-header p {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0;
}

/* 用户信息卡片 */
.user-info-card {
  margin-bottom: 30px;
  border-radius: 16px;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.user-info-card :deep(.el-card__body) {
  padding: 30px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid rgba(255, 255, 255, 0.3);
  position: relative;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-upload {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 24px;
  height: 24px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 12px;
}

.avatar-upload:hover {
  background: #337ecc;
}

.user-details {
  flex: 1;
}

.user-details h3 {
  font-size: 24px;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.user-details p {
  margin: 5px 0;
  opacity: 0.9;
  font-size: 14px;
}

.user-actions .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.user-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 设置网格 */
.settings-grid {
  margin-bottom: 30px;
}

.settings-grid .el-col {
  margin-bottom: 20px;
}

/* 设置卡片 */
.setting-card {
  border-radius: 12px;
  border: none;
  height: 100%;
  transition: all 0.3s ease;
}

.setting-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

/* 设置项 */
.setting-items {
  padding: 0;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: all 0.2s ease;
}

.setting-item:hover {
  background: #f8f9fa;
  margin: 0 -20px;
  padding: 20px;
  border-radius: 8px;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
}

.setting-info h4 {
  font-size: 16px;
  color: #2c3e50;
  margin: 0 0 5px 0;
  font-weight: 600;
}

.setting-info p {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.setting-arrow {
  color: #bdc3c7;
  font-size: 16px;
}

/* 对话框样式 */
.el-dialog {
  border-radius: 12px;
}

.el-dialog__header {
  padding: 20px 20px 10px;
}

.el-dialog__body {
  padding: 10px 20px 20px;
}

.el-dialog__footer {
  padding: 10px 20px 20px;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 20px;
}

.el-input__wrapper {
  border-radius: 8px;
}

/* 开关样式 */
.el-switch {
  --el-switch-on-color: #667eea;
}

/* 新增样式 */
.avatar-upload-area {
  display: flex;
  align-items: center;
  gap: 15px;
}

.avatar-preview {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e1e8ed;
}

.avatar-upload-container {
  text-align: center;
}

.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
  border-radius: 6px;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: .2s;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.upload-tips {
  margin-top: 10px;
  color: #999;
  font-size: 12px;
}

.verification-input {
  display: flex;
  gap: 10px;
}

.verification-input .el-input {
  flex: 1;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.account-status-info {
  padding: 10px 0;
}

.account-actions {
  text-align: center;
}

.security-devices {
  max-height: 400px;
  overflow-y: auto;
}

.user-switch {
  padding: 10px 0;
}

.delete-account-warning {
  padding: 10px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-container {
    padding: 15px;
  }

  .user-info {
    flex-direction: column;
    text-align: center;
  }

  .user-avatar {
    width: 100px;
    height: 100px;
  }

  .settings-grid .el-col {
    margin-bottom: 15px;
  }

  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .setting-arrow {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .page-header h2 {
    font-size: 24px;
  }

  .user-details h3 {
    font-size: 20px;
  }

  .card-title {
    font-size: 16px;
  }

  .setting-info h4 {
    font-size: 15px;
  }

  .setting-info p {
    font-size: 13px;
  }
}
</style>