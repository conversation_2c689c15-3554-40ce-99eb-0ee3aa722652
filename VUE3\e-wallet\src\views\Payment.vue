<template>
  <div class="payment-container">
    <!-- 支付途径选择 -->
    <div class="payment-channels">
      <div class="channel-header">
        <h2>选择支付途径</h2>
        <p>请选择您的支付方式</p>
      </div>
      <div class="channels-grid">
        <div
          class="channel-card"
          @click="openPaymentDialog('merchant')"
        >
          <div class="channel-icon">🏪</div>
          <div class="channel-content">
            <h3>商户付款</h3>
            <p>选择商户进行付款</p>
            <span class="channel-desc">支持钱包和银行卡</span>
          </div>
        </div>

        <div
          class="channel-card"
          @click="openPaymentDialog('qrcode')"
        >
          <div class="channel-icon">📱</div>
          <div class="channel-content">
            <h3>扫码付款</h3>
            <p>扫描商户二维码付款</p>
            <span class="channel-desc">快速便捷</span>
          </div>
        </div>

        <div
          class="channel-card"
          @click="openPaymentDialog('nfc')"
        >
          <div class="channel-icon">📡</div>
          <div class="channel-content">
            <h3>NFC支付</h3>
            <p>贴近POS机完成支付</p>
            <span class="channel-desc">无接触支付</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 支付对话框 -->
    <el-dialog
      v-model="paymentDialogVisible"
      :title="dialogTitle"
      width="600px"
      :before-close="closePaymentDialog"
      center
    >
      <div class="payment-dialog-content">
        <!-- 支付金额 -->
        <div class="dialog-amount-section">
          <h4>支付金额</h4>
          <div class="amount-input-wrapper">
            <span class="currency-symbol">¥</span>
            <el-input
              v-model="paymentAmount"
              placeholder="请输入支付金额"
              type="number"
              size="large"
              class="amount-input"
            />
          </div>
          <div class="quick-amounts">
            <el-button
              v-for="amount in [50, 100, 200, 500]"
              :key="amount"
              size="small"
              @click="setAmount(amount)"
              class="quick-amount-btn"
            >
              ¥{{ amount }}
            </el-button>
          </div>
        </div>

        <!-- 支付方式选择 -->
        <div class="dialog-payment-methods">
          <h4>支付方式</h4>
          <div class="methods-list">
            <div
              class="method-option"
              :class="{ selected: selectedMethod === 'wallet' }"
              @click="selectedMethod = 'wallet'"
            >
              <div class="method-icon">💰</div>
              <div class="method-info">
                <h5>钱包余额</h5>
                <p>余额 ¥{{ walletBalance.toFixed(2) }}</p>
              </div>
              <div class="method-radio">
                <div class="radio-dot" v-if="selectedMethod === 'wallet'"></div>
              </div>
            </div>

            <div
              class="method-option"
              :class="{ selected: selectedMethod === 'bankcard' }"
              @click="selectedMethod = 'bankcard'"
            >
              <div class="method-icon">💳</div>
              <div class="method-info">
                <h5>银行卡</h5>
                <p>{{ bankCards.length }}张可用</p>
              </div>
              <div class="method-radio">
                <div class="radio-dot" v-if="selectedMethod === 'bankcard'"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 银行卡选择 -->
        <div v-if="selectedMethod === 'bankcard'" class="dialog-card-selection">
          <h4>选择银行卡</h4>
          <div class="cards-list">
            <div
              v-for="card in bankCards"
              :key="card.cardId"
              class="card-option"
              :class="{ selected: selectedCard === card.cardId }"
              @click="selectedCard = card.cardId"
            >
              <div class="card-info">
                <span class="card-bank">{{ card.bankName }}</span>
                <span class="card-number">**** {{ card.cardNumber.slice(-4) }}</span>
              </div>
              <div class="card-check" v-if="selectedCard === card.cardId">✓</div>
            </div>
          </div>
        </div>

        <!-- 商品品类选择（仅商户付款时显示） -->
        <div v-if="currentChannel === 'merchant'" class="dialog-category-selection">
          <h4>商品品类</h4>
          <el-select
            v-model="selectedCategory"
            placeholder="请选择商品品类"
            size="large"
            style="width: 100%"
          >
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closePaymentDialog" size="large">
            取消
          </el-button>
          <el-button
            type="primary"
            @click="executePayment"
            :disabled="!canExecutePayment"
            :loading="paymentLoading"
            size="large"
          >
            {{ paymentLoading ? '支付中...' : `确认支付 ¥${paymentAmount || 0}` }}
          </el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { paymentApi } from '@/api/transaction';
import { getCurrentUserId } from '@/stores/user';

const router = useRouter();

// 基础数据
const walletBalance = ref(0);
const bankCards = ref([]);
const paymentAmount = ref('');
const selectedMethod = ref('wallet');
const selectedCard = ref('');
const selectedCategory = ref('');
const paymentLoading = ref(false);

// 对话框相关
const paymentDialogVisible = ref(false);
const currentChannel = ref('');
const dialogTitle = ref('');

// 商品品类数据
const categories = ref([
  { id: 1, name: '餐饮美食' },
  { id: 2, name: '购物消费' },
  { id: 3, name: '交通出行' },
  { id: 4, name: '生活服务' },
  { id: 5, name: '娱乐休闲' },
  { id: 6, name: '医疗健康' },
  { id: 7, name: '教育培训' },
  { id: 8, name: '其他' }
]);

// 计算属性：是否可以执行支付
const canExecutePayment = computed(() => {
  if (!paymentAmount.value || parseFloat(paymentAmount.value) <= 0) {
    return false;
  }

  if (!selectedMethod.value) {
    return false;
  }

  // 商户付款需要选择商品品类
  if (currentChannel.value === 'merchant' && !selectedCategory.value) {
    return false;
  }

  // 银行卡支付需要选择银行卡
  if (selectedMethod.value === 'bankcard' && !selectedCard.value) {
    return false;
  }

  // 钱包支付需要检查余额
  if (selectedMethod.value === 'wallet' && parseFloat(paymentAmount.value) > walletBalance.value) {
    return false;
  }

  return true;
});

// 加载用户数据
const loadUserData = async () => {
  try {
    const userId = getCurrentUserId();

    if (!userId) {
      // 临时使用测试用户ID
      console.log('使用测试用户ID: 4');
      const testUserId = 4;
      
      // 获取钱包余额
      const balanceResponse = await paymentApi.getWalletBalance(testUserId);
      if (balanceResponse.code === 0) {
        walletBalance.value = balanceResponse.data.balance;
      }

      // 获取银行卡列表
      const cardsResponse = await paymentApi.getBankCards(testUserId);
      if (cardsResponse.code === 0) {
        bankCards.value = cardsResponse.data;
      }
      return;
    }

    // 获取钱包余额
    const balanceResponse = await paymentApi.getWalletBalance(userId);
    if (balanceResponse.code === 0) {
      walletBalance.value = balanceResponse.data.balance;
    }

    // 获取银行卡列表
    const cardsResponse = await paymentApi.getBankCards(userId);
    if (cardsResponse.code === 0) {
      bankCards.value = cardsResponse.data;
    }
  } catch (error) {
    console.error('加载用户数据失败:', error);
    ElMessage.error('加载数据失败，请刷新重试');
  }
};



// 打开支付对话框
const openPaymentDialog = (channel) => {
  currentChannel.value = channel;
  paymentDialogVisible.value = true;

  // 设置对话框标题
  const channelNames = {
    'merchant': '商户付款',
    'qrcode': '扫码付款',
    'nfc': 'NFC支付'
  };
  dialogTitle.value = channelNames[channel];

  // 重置表单
  paymentAmount.value = '';
  selectedMethod.value = 'wallet';
  selectedCard.value = '';
  selectedCategory.value = '';
};

// 关闭支付对话框
const closePaymentDialog = () => {
  paymentDialogVisible.value = false;
  currentChannel.value = '';
  paymentAmount.value = '';
  selectedMethod.value = 'wallet';
  selectedCard.value = '';
  selectedCategory.value = '';
};

// 设置快速金额
const setAmount = (amount) => {
  paymentAmount.value = amount.toString();
};

// 执行支付
const executePayment = async () => {
  if (!canExecutePayment.value) {
    ElMessage.warning('请完善支付信息');
    return;
  }

  // 根据支付途径执行不同的处理
  if (currentChannel.value === 'qrcode') {
    ElMessage.info('正在启动摄像头扫码...');
    // 模拟扫码过程
    await new Promise(resolve => setTimeout(resolve, 1000));
    ElMessage.success('扫码成功！检测到商户二维码');
  } else if (currentChannel.value === 'nfc') {
    ElMessage.info('请将设备靠近POS机...');
    // 模拟NFC连接过程
    await new Promise(resolve => setTimeout(resolve, 1000));
    ElMessage.success('NFC连接成功！');
  }

  paymentLoading.value = true;

  try {
    const userId = getCurrentUserId() || 4; // 使用测试用户ID
    const amount = parseFloat(paymentAmount.value);

    // 获取支付途径名称
    const channelNames = {
      'merchant': '商户付款',
      'qrcode': '扫码付款',
      'nfc': 'NFC支付'
    };

    // 获取商品品类名称
    const categoryName = categories.value.find(c => c.id === selectedCategory.value)?.name || '';

    // 构建支付数据
    const paymentData = {
      userId,
      amount,
      paymentMethod: selectedMethod.value === 'wallet' ? 1 : 2,
      cardId: selectedCard.value || null,
      merchantId: null, // 暂时设为null，实际应用中可以根据商户选择设置
      paymentType: 'payment',
      payPassword: '123456', // 实际应用中应该让用户输入支付密码
      channel: currentChannel.value,
      remark: `${channelNames[currentChannel.value]} - ${categoryName} - ${selectedMethod.value === 'wallet' ? '钱包' : '银行卡'}支付`
    };

    console.log('支付数据:', paymentData);

    // 调用真实的支付API
    let response;
    if (selectedMethod.value === 'wallet') {
      // 钱包支付
      response = await paymentApi.walletPayment(paymentData);
    } else {
      // 银行卡支付
      response = await paymentApi.bankCardPayment(paymentData);
    }

    if (response && response.code === 0) {
      ElMessage.success(`${channelNames[currentChannel.value]}成功！金额：¥${amount.toFixed(2)}`);

      // 关闭对话框
      closePaymentDialog();

      // 刷新用户数据
      await loadUserData();
    } else {
      throw new Error(response?.msg || '支付失败');
    }

  } catch (error) {
    console.error('支付失败:', error);
    ElMessage.error('支付失败，请重试');
  } finally {
    paymentLoading.value = false;
  }
};

// 组件挂载时初始化
onMounted(async () => {
  // 加载用户数据
  await loadUserData();
});
</script>

<style scoped>
/* 支付应用样式 */
.payment-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px;
  background: #f5f5f5;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 支付途径选择区域 */
.payment-channels {
  background: white;
  border-radius: 20px;
  padding: 48px;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.08);
}

.channel-header {
  text-align: center;
  margin-bottom: 40px;
}

.channel-header h2 {
  font-size: 28px;
  color: #333;
  margin: 0 0 12px 0;
  font-weight: 600;
}

.channel-header p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.channels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  max-width: 1000px;
  margin: 0 auto;
}

.channel-card {
  background: #fafafa;
  border: 3px solid #f0f0f0;
  border-radius: 20px;
  padding: 40px 32px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.channel-card:hover {
  border-color: #007aff;
  background: #f8fbff;
  transform: translateY(-6px);
  box-shadow: 0 15px 50px rgba(0, 122, 255, 0.2);
}

.channel-icon {
  font-size: 56px;
  margin-bottom: 20px;
  display: block;
}

.channel-content h3 {
  font-size: 22px;
  color: #333;
  margin: 0 0 12px 0;
  font-weight: 600;
}

.channel-content p {
  font-size: 16px;
  color: #666;
  margin: 0 0 16px 0;
  line-height: 1.5;
}

.channel-desc {
  font-size: 14px;
  color: #007aff;
  background: rgba(0, 122, 255, 0.1);
  padding: 6px 12px;
  border-radius: 12px;
  display: inline-block;
  font-weight: 500;
}

/* 支付对话框样式 */
.payment-dialog-content {
  padding: 20px 0;
}

.dialog-amount-section {
  margin-bottom: 32px;
}

.dialog-amount-section h4 {
  font-size: 18px;
  color: #333;
  margin: 0 0 16px 0;
  font-weight: 600;
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.currency-symbol {
  font-size: 24px;
  color: #333;
  margin-right: 8px;
  font-weight: 600;
}

.amount-input {
  flex: 1;
}

.quick-amounts {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-amount-btn {
  min-width: 80px;
}

.dialog-payment-methods {
  margin-bottom: 32px;
}

.dialog-payment-methods h4 {
  font-size: 18px;
  color: #333;
  margin: 0 0 16px 0;
  font-weight: 600;
}

.methods-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.method-option {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.method-option:hover {
  border-color: #007aff;
  background: #f8fbff;
}

.method-option.selected {
  border-color: #007aff;
  background: #e3f2fd;
}

.method-option .method-icon {
  font-size: 24px;
}

.method-info {
  flex: 1;
}

.method-info h5 {
  font-size: 16px;
  color: #333;
  margin: 0 0 4px 0;
  font-weight: 600;
}

.method-info p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.method-radio {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.method-option.selected .method-radio {
  border-color: #007aff;
}

.radio-dot {
  width: 10px;
  height: 10px;
  background: #007aff;
  border-radius: 50%;
}

.dialog-card-selection {
  margin-bottom: 32px;
}

.dialog-card-selection h4 {
  font-size: 18px;
  color: #333;
  margin: 0 0 16px 0;
  font-weight: 600;
}

.cards-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.card-option:hover {
  border-color: #007aff;
}

.card-option.selected {
  border-color: #007aff;
  background: #e3f2fd;
}

.card-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.card-bank {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

.card-number {
  font-size: 14px;
  color: #666;
}

.card-check {
  color: #007aff;
  font-size: 16px;
  font-weight: bold;
}

.dialog-category-selection h4 {
  font-size: 18px;
  color: #333;
  margin: 0 0 16px 0;
  font-weight: 600;
}







.tip-card {
  text-align: center;
  max-width: 400px;
  margin: 0 auto;
}

.tip-card .tip-icon {
  font-size: 64px;
  margin-bottom: 24px;
  display: block;
}

.tip-card h3 {
  font-size: 24px;
  color: #333;
  margin: 0 0 12px 0;
  font-weight: 600;
}

.tip-card p {
  font-size: 16px;
  color: #666;
  margin: 0 0 24px 0;
}

.tip-steps {
  display: flex;
  flex-direction: column;
  gap: 12px;
  text-align: left;
}

.step {
  background: #f8f8f8;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  border-left: 4px solid #007aff;
}





/* 支付按钮区域 */
.pay-section {
  margin-top: 32px;
  text-align: center;
}

.pay-btn {
  width: 100%;
  max-width: 400px;
  height: 56px;
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
  color: white;
  border: none;
  border-radius: 28px;
  font-size: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
}

.pay-btn:hover:not(.disabled) {
  background: linear-gradient(135deg, #0056cc 0%, #003d99 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 122, 255, 0.4);
}

.pay-btn.disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .payment-container {
    max-width: 400px;
    padding: 16px;
  }

  .payment-channels {
    padding: 24px;
  }

  .channel-header h2 {
    font-size: 24px;
  }

  .channels-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .channel-card {
    padding: 24px 20px;
  }

  .channel-icon {
    font-size: 40px;
  }

  .channel-content h3 {
    font-size: 18px;
  }
}
</style>
