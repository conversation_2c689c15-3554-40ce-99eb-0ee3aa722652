package com.icss.springbootbig.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.icss.springbootbig.entity.Account;
import com.icss.springbootbig.entity.Transaction;
import com.icss.springbootbig.entity.User;
import com.icss.springbootbig.mapper.AccountMapper;
import com.icss.springbootbig.mapper.TransactionMapper;
import com.icss.springbootbig.mapper.UserMapper;
import com.icss.springbootbig.exception.ApiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TransactionService {
    @Autowired
    private TransactionMapper transactionMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AccountMapper accountMapper;

    public List<Transaction> queryTransactions(Integer userId, Integer type,
                                               Date startDate, Date endDate) {
        QueryWrapper<Transaction> wrapper = new QueryWrapper<>();

        if (userId != null) {
            wrapper.eq("user_id", userId);
        }
        if (type != null) {
            wrapper.eq("type", type);
        }
        if (startDate != null && endDate != null) {
            if (startDate.after(endDate)) {
                throw new ApiException("开始时间不能晚于结束时间");
            }
            wrapper.between("created_at", startDate, endDate);
        } else if (startDate != null) {
            wrapper.ge("created_at", startDate);
        } else if (endDate != null) {
            wrapper.le("created_at", endDate);
        }

        wrapper.orderByDesc("created_at");
        return transactionMapper.selectList(wrapper);
    }

    public List<Transaction> getRecentTransactions(Integer userId, Integer limit) {
        return transactionMapper.selectList(
                new QueryWrapper<Transaction>()
                        .eq("user_id", userId)
                        .orderByDesc("created_at")
                        .last("LIMIT " + limit));
    }

    public Integer deleteTransaction(Integer txnId) {
        int result = transactionMapper.deleteById(txnId);
        if (result <= 0) {
            throw new ApiException("交易记录不存在");
        }
        return result;
    }

    public Integer batchDeleteTransactions(List<Integer> txnIds) {
        if (txnIds == null || txnIds.isEmpty()) {
            throw new ApiException("请选择要删除的记录");
        }
        return transactionMapper.deleteBatchIds(txnIds);
    }

    /**
     * 获取交易详情
     */
    public Transaction getTransactionDetail(Integer txnId, Integer userId) {
        Transaction transaction = transactionMapper.selectById(txnId);
        if (transaction == null) {
            throw new ApiException("交易记录不存在");
        }

        // 验证交易记录是否属于该用户
        if (!transaction.getUserId().equals(userId)) {
            throw new ApiException("无权查看该交易记录");
        }

        return transaction;
    }

    /**
     * 获取交易统计信息
     */
    public Map<String, Object> getTransactionStats(Integer userId, Date startDate, Date endDate) {
        QueryWrapper<Transaction> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);

        if (startDate != null && endDate != null) {
            wrapper.between("created_at", startDate, endDate);
        }

        List<Transaction> transactions = transactionMapper.selectList(wrapper);

        Map<String, Object> stats = new HashMap<>();

        // 总交易数
        stats.put("totalCount", transactions.size());

        // 按类型统计
        Map<Integer, Long> typeStats = transactions.stream()
            .collect(Collectors.groupingBy(Transaction::getType, Collectors.counting()));
        stats.put("typeStats", typeStats);

        // 按状态统计
        Map<Integer, Long> statusStats = transactions.stream()
            .collect(Collectors.groupingBy(Transaction::getStatus, Collectors.counting()));
        stats.put("statusStats", statusStats);

        // 金额统计
        BigDecimal totalAmount = transactions.stream()
            .map(Transaction::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.put("totalAmount", totalAmount);

        // 收入支出统计
        BigDecimal income = transactions.stream()
            .filter(t -> t.getType() == 1 || t.getType() == 3) // 充值、收款
            .map(Transaction::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal expense = transactions.stream()
            .filter(t -> t.getType() == 2 || t.getType() == 4 || t.getType() == 5) // 转账、消费、提现
            .map(Transaction::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        stats.put("income", income);
        stats.put("expense", expense);

        return stats;
    }

    /**
     * 生成收款码信息
     */
    public Map<String, Object> generateReceiveCode(Integer userId, BigDecimal amount, String remark) {
        // 验证用户
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ApiException("用户不存在");
        }

        // 验证账户状态
        Account account = accountMapper.selectOne(
            new QueryWrapper<Account>().eq("user_id", userId)
        );
        if (account == null || account.getStatus() != 1) {
            throw new ApiException("账户不存在或已冻结");
        }

        // 生成收款码
        String receiveCode = "RC" + System.currentTimeMillis() + userId;

        Map<String, Object> codeInfo = new HashMap<>();
        codeInfo.put("receiveCode", receiveCode);
        codeInfo.put("userId", userId);
        codeInfo.put("username", user.getUsername());
        codeInfo.put("phone", user.getPhone());
        codeInfo.put("amount", amount);
        codeInfo.put("remark", remark);
        codeInfo.put("expireTime", new Date(System.currentTimeMillis() + 30 * 60 * 1000)); // 30分钟过期

        return codeInfo;
    }

    /**
     * 扫码收款
     */
    @Transactional
    public void scanCodeReceive(String receiveCode, Integer payerUserId, String payPassword) {
        // 这里应该从缓存中获取收款码信息，为了演示简化处理
        // 实际应用中应该将收款码信息存储在Redis等缓存中

        // 验证支付用户和密码
        User payerUser = userMapper.selectById(payerUserId);
        if (payerUser == null) {
            throw new ApiException("支付用户不存在");
        }
        // 支付密码现在存储在银行卡表中，需要验证默认银行卡的支付密码
        // 暂时跳过支付密码验证，或者集成PayPasswordService
        // if (!payPassword.equals(payerUser.getPayPassword())) {
        //     throw new ApiException("支付密码错误");
        // }

        // 这里应该解析收款码获取收款信息
        // 为了演示，假设收款码格式为 "RC{timestamp}{userId}"
        // 实际应用中应该有更安全的编码方式

        throw new ApiException("收款码功能需要配合前端实现完整的二维码生成和解析");
    }
}