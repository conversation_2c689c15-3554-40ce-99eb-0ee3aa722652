package com.icss.springbootbig.controller;

import com.icss.springbootbig.entity.BankCard;
import com.icss.springbootbig.result.R;
import com.icss.springbootbig.service.BankCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@CrossOrigin(origins = {"http://localhost:5173", "http://127.0.0.1:5173"})
@RestController
@RequestMapping("/api/bank-card")
public class BankCardController {
    @Autowired
    private BankCardService bankCardService;

    @PostMapping
    public R<BankCard> addBankCard(@RequestBody BankCard bankCard) {
        System.out.println("控制器接收到的银行卡数据:");
        System.out.println("userId: " + bankCard.getUserId());
        System.out.println("bankName: " + bankCard.getBankName());
        System.out.println("cardNumber: " + bankCard.getCardNumber());
        System.out.println("cardType: " + bankCard.getCardType());
        System.out.println("cardHolder: " + bankCard.getCardHolder());
        System.out.println("phone: " + bankCard.getPhone());
        System.out.println("status: " + bankCard.getStatus());
        System.out.println("isDefault: " + bankCard.getIsDefault());

        return R.success("添加成功", bankCardService.addBankCard(bankCard));
    }

    @GetMapping("/user/{userId}")
    public R<List<BankCard>> getCardsByUser(@PathVariable Integer userId) {
        return R.success("查询成功", bankCardService.getCardsByUserId(userId));
    }

    @GetMapping("/{cardId}")
    public R<BankCard> getCardDetails(@PathVariable Integer cardId) {
        return R.success("查询成功", bankCardService.getCardDetails(cardId));
    }



    @DeleteMapping("/{cardId}")
    public R<Integer> deleteCard(
            @PathVariable Integer cardId,
            @RequestParam(required = false) Integer userId) {
        return R.success("删除成功",
                bankCardService.deleteCard(userId, cardId));
    }

    @DeleteMapping("/admin/{cardId}")
    public R<Integer> adminDeleteCard(
            @PathVariable Integer cardId) {
        return R.success("删除成功",
                bankCardService.adminDeleteCard(cardId));
    }

    @PutMapping("/unbind/{cardId}")
    public R<Integer> unbindCard(
            @PathVariable Integer cardId,
            @RequestParam Integer userId) {
        return R.success("解绑成功",
                bankCardService.unbindCard(userId, cardId));
    }

    @GetMapping("/credit-cards/{userId}")
    public R<List<BankCard>> getCreditCards(@PathVariable Integer userId) {
        return R.success("查询成功", bankCardService.getCreditCards(userId));
    }

    @GetMapping("/admin/all")
    public R<List<BankCard>> getAllCards() {
        return R.success("查询成功", bankCardService.getAllCards());
    }

    @GetMapping("/bound-cards/{userId}")
    public R<List<BankCard>> getBoundCards(@PathVariable Integer userId) {
        return R.success("查询成功", bankCardService.getBoundCards(userId));
    }

    @PutMapping("/bind/{cardId}")
    public R<Integer> bindCard(
            @PathVariable Integer cardId,
            @RequestParam Integer userId) {
        return R.success("绑定成功", bankCardService.bindCard(userId, cardId));
    }

    @PostMapping("/validate")
    public R<Boolean> validateBankCard(@RequestBody BankCard bankCard) {
        boolean isValid = bankCardService.validateBankCard(bankCard);
        return R.success("验证完成", isValid);
    }

    @PostMapping("/send-verification/{cardId}")
    public R<Boolean> sendVerificationCode(
            @PathVariable Integer cardId,
            @RequestParam String phone) {
        boolean sent = bankCardService.sendVerificationCode(cardId, phone);
        return R.success("验证码发送成功", sent);
    }

    @PostMapping("/verify-connection/{cardId}")
    public R<Boolean> verifyCardConnection(
            @PathVariable Integer cardId,
            @RequestParam String verificationCode) {
        boolean verified = bankCardService.verifyCardConnection(cardId, verificationCode);
        return R.success(verified ? "连接成功" : "验证失败", verified);
    }

    @PutMapping("/disconnect/{cardId}")
    public R<Integer> disconnectCard(
            @PathVariable Integer cardId,
            @RequestParam Integer userId) {
        return R.success("断开连接成功",
                bankCardService.disconnectCard(userId, cardId));
    }

    @GetMapping("/connection-stats/{userId}")
    public R<Map<String, Integer>> getConnectionStats(@PathVariable Integer userId) {
        return R.success("查询成功", bankCardService.getConnectionStats(userId));
    }

    @GetMapping("/operational/{cardId}")
    public R<Boolean> isCardOperational(@PathVariable Integer cardId) {
        boolean operational = bankCardService.isCardOperational(cardId);
        return R.success("查询成功", operational);
    }

    @GetMapping("/default/{userId}")
    public R<BankCard> getDefaultCard(@PathVariable Integer userId) {
        return R.success("查询成功", bankCardService.getDefaultCard(userId));
    }

    @PutMapping("/set-default/{cardId}")
    public R<String> setDefaultCard(
            @PathVariable Integer cardId,
            @RequestParam Integer userId) {
        try {
            bankCardService.setDefaultCard(userId, cardId);
            return R.success("设置默认卡成功");
        } catch (Exception e) {
            return R.failure("设置失败: " + e.getMessage());
        }
    }

    @GetMapping("/detail-info/{cardId}")
    public R<Map<String, Object>> getCardDetailInfo(
            @PathVariable Integer cardId,
            @RequestParam Integer userId) {
        try {
            Map<String, Object> cardInfo = bankCardService.getCardDetailInfo(cardId, userId);
            return R.success("查询成功", cardInfo);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/batch-delete")
    public R<Integer> batchDeleteCards(@RequestBody Map<String, Object> request) {
        try {
            Integer userId = (Integer) request.get("userId");
            @SuppressWarnings("unchecked")
            List<Integer> cardIds = (List<Integer>) request.get("cardIds");

            Integer deletedCount = bankCardService.batchDeleteCards(userId, cardIds);
            return R.success("批量删除成功", deletedCount);
        } catch (Exception e) {
            return R.failure("删除失败: " + e.getMessage());
        }
    }

    @GetMapping("/usage-stats/{userId}")
    public R<Map<String, Object>> getCardUsageStats(@PathVariable Integer userId) {
        try {
            Map<String, Object> stats = bankCardService.getCardUsageStats(userId);
            return R.success("查询成功", stats);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    // 表格编辑 - 更新单个字段
    @PutMapping("/field/{cardId}")
    public R<BankCard> updateField(
            @PathVariable Integer cardId,
            @RequestBody Map<String, Object> fieldData) {

        System.out.println("更新银行卡字段 - cardId: " + cardId);
        System.out.println("字段数据: " + fieldData);

        try {
            BankCard updatedCard = bankCardService.updateField(cardId, fieldData);
            return R.success("字段更新成功", updatedCard);
        } catch (Exception e) {
            System.err.println("更新字段失败: " + e.getMessage());
            return R.failure("更新失败: " + e.getMessage());
        }
    }

    // 批量更新银行卡
    @PutMapping("/batch")
    public R<List<BankCard>> batchUpdate(@RequestBody List<BankCard> bankCards) {
        System.out.println("批量更新银行卡，数量: " + bankCards.size());

        try {
            List<BankCard> updatedCards = bankCardService.batchUpdate(bankCards);
            return R.success("批量更新成功", updatedCards);
        } catch (Exception e) {
            System.err.println("批量更新失败: " + e.getMessage());
            return R.failure("批量更新失败: " + e.getMessage());
        }
    }

    /**
     * 根据手机号查询银行卡
     */
    @GetMapping("/by-phone/{phone}")
    public R<List<BankCard>> getCardsByPhone(@PathVariable String phone) {
        try {
            List<BankCard> cards = bankCardService.getCardsByPhone(phone);
            return R.success("查询成功", cards);
        } catch (Exception e) {
            return R.failure("查询失败: " + e.getMessage());
        }
    }

    /**
     * 设置银行卡余额（测试用）
     */
    @PostMapping("/set-balance")
    public R<String> setBankCardBalance(@RequestBody Map<String, Object> request) {
        try {
            Integer cardId = (Integer) request.get("cardId");
            BigDecimal balance = new BigDecimal(request.get("balance").toString());
            bankCardService.setBankCardBalance(cardId, balance);
            return R.success("银行卡余额设置成功");
        } catch (Exception e) {
            return R.failure("设置银行卡余额失败: " + e.getMessage());
        }
    }
}